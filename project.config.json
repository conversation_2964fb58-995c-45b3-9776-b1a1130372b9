{"miniprogramRoot": "dist/", "description": "", "appid": "wx37660d3efda5980e", "setting": {"urlCheck": true, "es6": true, "enhance": true, "compileHotReLoad": false, "postcss": true, "minified": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false, "compileWorklet": true, "ignoreUploadUnusedFiles": false, "minifyWXML": false, "swc": false, "coverView": false, "showShadowRootInWxmlPanel": false, "packNpmRelationList": []}, "compileType": "miniprogram", "srcMiniprogramRoot": "dist/", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "projectname": "汽配运营通", "libVersion": "2.32.3"}