{"name": "store", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "default", "typescript": true, "css": "Sass", "framework": "React"}, "sideEffects": ["*.css", "*.scss"], "scripts": {"build": "taro build --type h5", "dev": "npm run build -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@capacitor-community/bluetooth-le": "^7.1.1", "@capacitor/app": "^7.0.1", "@capacitor/barcode-scanner": "^2.0.3", "@capacitor/camera": "^7.0.1", "@capacitor/core": "^7.2.0", "@nutui/nutui-react-taro": "^2.6.8", "@qiun/ucharts": "^2.5.0-20230101", "@tarojs/components": "4.1.5", "@tarojs/helper": "4.1.5", "@tarojs/plugin-framework-react": "4.1.5", "@tarojs/plugin-html": "^4.1.5", "@tarojs/plugin-platform-weapp": "4.1.5", "@tarojs/react": "4.1.5", "@tarojs/runtime": "4.1.5", "@tarojs/shared": "4.1.5", "@tarojs/taro": "4.1.5", "add": "^2.0.6", "ahooks": "^3.8.0", "babel-plugin-import": "^1.13.8", "classnames": "^2.5.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.11", "iconv-lite": "^0.6.3", "lodash": "^4.17.21", "react": "^18.0.0", "react-dom": "^18.0.0", "react-intl": "^7.1.11", "react-is": "^19.1.0", "wx-mini-qrcode": "^1.0.3", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.8.0", "@dcasia/mini-program-tailwind-webpack-plugin": "^1.5.6", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "4.1.5", "@tarojs/plugin-platform-h5": "^4.1.5", "@tarojs/taro-loader": "4.1.5", "@tarojs/test-utils-react": "^0.1.1", "@tarojs/webpack5-runner": "4.1.5", "@types/jest": "^29.3.1", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "@umijs/fabric": "^4.0.1", "autoprefixer": "^10.4.19", "babel-preset-taro": "4.1.5", "eslint": "^8.12.0", "eslint-config-taro": "4.1.5", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.5.0", "mini-css-extract-plugin": "^2.9.0", "postcss": "^8.4.38", "react-refresh": "^0.11.0", "stylelint": "^14.4.0", "tailwindcss": "^3.4.3", "taro-plugin-tailwind": "^1.3.1", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.1.0", "webpack": "5.78.0"}}