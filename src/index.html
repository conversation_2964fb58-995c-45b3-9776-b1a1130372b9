<!DOCTYPE html>
<html>
  <head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta
      content="width=device-width,initial-scale=1,user-scalable=no,viewport-fit=cover"
      name="viewport"
    />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no,address=no" />
    <meta name="apple-mobile-web-app-status-bar-style" content="white" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <title>GRIPX</title>
    <script>
      <%= htmlWebpackPlugin.options.script %>
    </script>
    <style>
      html,
      body {
        height: 100%;
      }
    </style>
  </head>

  <body>
    <div id="app"></div>
  </body>
  <script>
    (function () {
      const host = location.hostname;
      const port = location.port;
      const needVConsole = port || host.startsWith("pre-i0");
      if (needVConsole) {
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/vconsole@latest/dist/vconsole.min.js';
        script.onload = function () {
          new VConsole();
        };
        document.head.appendChild(script);
      }
    })();
  </script>
</html>
