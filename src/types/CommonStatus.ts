/**
 * 状态 0-禁用  1-启用
 */
export const CommonStatusValueEnum = {
  0: { text: '禁用', status: 'Error' },
  1: { text: '启用', status: 'Success' },
};
/**
 状态 0-否 1-是
 */
export const CommonYesNoValueEnum = {
  0: { text: '否', status: 'Error' },
  1: { text: '是', status: 'Success' },
};
/**
 * 优惠类型
 */
export const DiscountTypeValueEnum = {
  0: { text: '无优惠', status: 'Error' },
  1: { text: '整单打折', status: 'Success' },
  2: { text: '整单减', status: 'Default' },
};
/**
 * 付款方式
 */
export const PayWayValueEnum = {
  '0': { text: '挂账', status: 'Error' },
  '1': { text: '现款', status: 'Error' },
};
/**
 * 配送方式
 */
export const DeliverWayValueEnum = {
  0: { text: '快递', status: 'Error' },
};

/**
 * 查询类型
 */
export const SearchTypeValueEnum = {
  0: { text: '精确查找', status: 'Error' },
  1: { text: '仅看有货', status: 'Success' },
};
/**
 * 0表示否定 1为肯定
 */
export type YesOrNoType = 0 | 1;

export const ReverseCommonStatusValueEnum = {
  0: { text: '启用', status: 'Success' },
  1: { text: '禁用', status: 'Error' },
};
/**逾期状态 */
export const OverdueStatusValueEnum = {
  1: { text: '正常', status: 'Success' },
  2: { text: '逾期', status: 'Error' },
  3: { text: '停用', status: 'Error' },
};
/**
 * 0:现款 10:挂账
 */
export enum RefundTypeEnum {
  /**
   * 现款
   */
  Cash = 0,
  /**
   * 挂账
   */
  Account = 10,
}
/**
 * 退款方式-现款&挂账
 */
export const RefundTypeValueEnum = [
  { label: '现款', value: 0 },
  { label: '挂账', value: 10 },
  // { label: '原路退', value: 20 },
];
/**
 * 退款方式-现款
 */
export const CashRefundTypeValueEnum = [
  { label: '现款', value: 0 },
  // { label: '原路退', value: 20 },
];

/**
 * 额度变更日志类型
 */
export const LogTypeMap: Record<number, string> = {
  1: '新增额度账户',
  2: '变更额度账户',
  3: '启用',
  4: '锁定',
  5: '注销',
};
