body {
  padding-top: env(safe-area-inset-top);
  .nut-navbar-left {
    height: 100%;
  }
}

page, body {
  margin: 0;
  font-size: 32px;

  p {
    margin: 0;
  }

  a {
    color: #176EFF;

    &:active {
      opacity: 0.6;
    }
  }

  --nutui-color-primary: #F49C1F;
  --nutui-color-primary-stop-1: #F49C1F;
  --nutui-color-primary-stop-2: #F49C1F;

  // 搜索框中间内容区的背景色
  --nutui-searchbar-content-background: rgba(0, 0, 0, 0.03);
  // 搜索框的padding值
  --nutui-searchbar-padding: 12px 28px;
  // 按钮的圆角设置
  --nutui-button-border-radius: 8px;

  // 分割线 padding 值 0
  --nutui-divider-margin: 0px;

  --nutui-input-font-size: 32px;

  --nutui-checkbox-button-background: rgba(0, 0, 0, 0.03);

  // 数字输入框左右按钮的宽度
  --nutui-inputnumber-button-width: 60px;
  --stepper-input-width: 145px;
  // 数字输入框左右按钮的高度
  --nutui-inputnumber-button-height: 60px;
  // 数字输入框左右按钮的圆角
  --nutui-inputnumber-button-border-radius: 4px;
  // 数字输入框左右按钮的背景色
  --nutui-inputnumber-button-background-color: #f4f4f4;
  // 数字输入框中input的高度
  --nutui-inputnumber-input-height: 60px;
  // 数字输入框中input的宽度
  --nutui-inputnumber-input-width: 140px;
  // 数字输入框中input的margin值
  --nutui-inputnumber-input-margin: 0 4px;
  //shape为button的圆角
  --nutui-checkbox-button-border-radius:8px;
  //shape为button的内边距
  --nutui-checkbox-button-padding:12px 24px;
  --nutui-textarea-padding: 24px 28px; // Menu.Item

  // Price large 尺寸整数部分字体大小
  --nutui-price-integer-big-size: 40px;

  //tag 内边距
  --nutui-tag-padding: 18px 12px;

  // 菜单选项容器的最大高度
  --nutui-menu-content-max-height: 70vh;
  // 菜单选项容器的内边距
  --nutui-menu-content-padding: 12px 28px;

  /**
   * 表单样式全局修改
   */
  --nutui-form-item-label-width: 'unset';

  .nut-form {
    .nut-cell {
      padding: 0;
    }

    .nut-cell-group-wrap {
      margin: 0;
    }

    .nut-input-native, .nut-textarea-textarea {
      text-align: right !important;
    }

    .nut-cell-extra {
      flex: unset;
    }

    .nut-form-item {
      padding: 24px 0;
      border-top: 1px solid #eee;
      border-radius: 0;

      &:first-child {
        border-top: none;
      }
    }

    .nut-form-item-body-slots {
      width: 100%;
      display: flex;
      justify-content: flex-end;
    }

    .nut-form-item-body {
      align-items: flex-end;
      white-space: nowrap;
    }
  }

  /**
   * TAG全局样式修改
   */
  --nutui-tag-border-radius: 4px;
  --nutui-tag-primary-background-color: #FFF4F4;
  --nutui-tag-success-background-color: #EAF9EC;
  --nutui-tag-background-color: #F5F5F5;
  --nutui-tag-info-background-color: #E7F0FF;

  .nut-tag {
    white-space: nowrap;
  }

  .nut-tag-success {
    color: #33CC47 !important;
    border-color: #ADEBB5;
  }

  .nut-tag-primary {
    color: #F49C1F;
    border-color: #FCAEAD;
  }

  .nut-tag-default {
    color: #aaa;
    border-color: #bbb;
  }

  .nut-tag-info {
    color: #176EFF;
    border-color: #A2C5FF;
  }

  /**
   * 纵向进度条通用样式
   */
  .nut-steps-vertical {
    .nut-step {
      padding: 20px 0;
      overflow: hidden;

      .nut-step-head {
        margin: -20px 0;
      }

      .nut-step-icon {
        margin-top: 30px;
        width: 16px;
        height: 16px;
        border-radius: 16px;
        background: #ddd;
      }

      .nut-step-line {
        top: 0;
        height: 1000px;
        background: #ddd;
      }

      &:first-child {
        .nut-step-line {
          top: 30px;
        }

        &:last-child {
          .nut-step-line {
            display: none;
          }
        }
      }

      &:last-child {
        .nut-step-line {
          display: block;
          height: 30px;
        }
      }
    }

    .nut-step-main {
      width: 100%;

      .nut-step-title {
        margin-bottom: 12px;
      }
    }
  }

  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    color: transparent;
  }

  .nut-searchbar {
    background: none;
  }

  .tabs .nut-tabpane {
    background-color: #F5F5F5 !important;
  }

  .nut-menu-title-text {
    padding: 0px 6px 0px 14px !important;
  }

  .formItemRequired {
    &::after {
      content: "*";
      color: #F49C1F;
      margin-left: 4px;
    }
  }

  .memberBg {
    background-image: url("./assets/member.png");
  }
  .weui-input {
    text-align: inherit;
  }

  .nut-input {
    padding: 28px 0;
  }

  .text-primary {
    color: #F49C1F;
  }

  .nut-collapse-item-header {
    padding: 26px;
  }
  .nut-collapse-item-content-text {
    padding: 0 26px 26px;
  }
}
