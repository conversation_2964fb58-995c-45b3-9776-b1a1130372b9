import {post} from '@/utils/request';
import {PageResponseDataType} from "@/types/PageResponseDataType";
import {QueryMsgListRequest} from "@/packages/message/types/query.msg.list.request";
import {PageRequestParamsType} from "@/types/PageRequestParamsType";
import {MsgListItemEntity} from "@/packages/message/types/msg.list.item.entity";

/**
 * 查看消息列表
 * @param params
 */
export const queryMsgList = async (params: QueryMsgListRequest & PageRequestParamsType) => {
  return post<PageResponseDataType<MsgListItemEntity>>(
    `/ipmsmessagecenter/MessageServiceFacade/queryMsgList`,
    {
      data: params,
    },
  );
};

/**
 * 查看消息详情
 * @param id
 */
export const queryMsgDetail = async (id: number) => {
  return post<MsgListItemEntity>(`/ipmsmessagecenter/MessageServiceFacade/queryMsgDetail`, {
    data: { id },
  });
};


/**
 * 设置消息已读
 */
export const setRead = async (idList: number[]) => {
  return post<boolean>(`/ipmsmessagecenter/MessageServiceFacade/setRead`, {
    data: { idList },
  });
};
