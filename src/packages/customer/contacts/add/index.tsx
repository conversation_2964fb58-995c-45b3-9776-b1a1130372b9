import { ConfigProvider, Input, Switch, TextArea } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro, { useLoad, useRouter } from '@tarojs/taro';
import { find, isEmpty, size, uniqueId } from 'lodash';
import { useEffect, useState } from 'react';
import { FormItemType } from '../../add';
import useCustomerAddStore from '../../add/store';
import { CustomerContactEntity } from '../../list/types/CustomerContactEntity';
import './index.scss';
import CustomNavBar from "@/components/CustomNavBar";
export default () => {
  const { params } = useRouter();
  console.log(params.id);
  const store = useCustomerAddStore();
  const [contact, setContact] = useState<CustomerContactEntity>({ id: '' });
  useLoad(() => {
    let title = '';
    if (params.id) {
      title = '编辑联系人';
    } else {
      title = '新增联系人';
    }
    Taro.setNavigationBarTitle({ title });
  });
  useEffect(() => {
    if (params.id) {
      const contactItem = find(store.contacts, { id: params.id });
      if (contactItem) {
        setContact(contactItem);
      }
    }
  }, [params.id]);

  const FormItems: FormItemType<CustomerContactEntity>[] = [
    {
      key: 'name',
      label: '联系人',
      name: 'name',
      value: contact.name,
      required: true,
    },
    { key: 'phone', label: '联系方式', name: 'phone', value: contact.phone },
    {
      key: 'position',
      label: '职务',
      name: 'position',
      value: contact.position,
    },
    {
      key: 'qq',
      label: 'QQ',
      name: 'qq',
      value: contact.qq,
    },
    {
      key: 'wechat',
      label: '微信',
      name: 'wechat',
      value: contact.wechat,
    },
    {
      key: 'email',
      label: '邮箱',
      name: 'email',
      value: contact.email,
    },
  ];
  return (
    <ConfigProvider
      theme={{
        nutuiInputPadding: '8px 0px',
        nutuiTextareaPadding: '0px 0px',
      }}
    >
      <CustomNavBar title={params.id ? "编辑联系人" : "新增联系人"} showBack={true} />
      <div className="p-[28px] flex flex-col gap-[24px] pb-[120px] text-main">
        <div id="customerAddForm" className="px-[28px] rounded-[16px] bg-white flex flex-col">
          {FormItems.map((t) => {
            const { key, name, label, placeholder = '请输入', value, required } = t;
            return (
              <View key={key} className="formItem">
                <div className="flex justify-between items-center ">
                  <span className={`text-[32px] text-main  ${required && 'formItemRequired'}`}>
                    {label}
                  </span>
                  <span className="flex items-center text-[32px] gap-[24px]">
                    <Input
                      placeholder={placeholder}
                      placeholderClass="text-[#0000004D]"
                      align="right"
                      value={value}
                      onChange={(v) => {
                        setContact({ ...contact, [name]: v });
                      }}
                    ></Input>
                  </span>
                </div>
              </View>
            );
          })}
        </div>
        <div className="p-[28px] rounded-[16px] bg-white flex justify-between items-center">
          <span className="text-[32px]">设为默认联系人</span>
          <Switch
            checked={contact.isDefault == 1}
            onChange={(value) => {
              setContact({ ...contact, isDefault: value ? 1 : 0 });
            }}
          ></Switch>
        </div>

        <div className="p-[28px] rounded-[16px] bg-white">
          <div className="flex justify-between items-center">
            <span className="text-[32px] mb-[32px]">备注</span>
            <span className="text-thirdary">{size(contact.remark)}/100</span>
          </div>
          <TextArea
            onChange={(value) => {
              setContact({ ...contact, remark: value });
            }}
            value={contact.remark}
            className="h-[100px]"
            maxLength={100}
            placeholder="请输入备注信息"
          />
        </div>
        <div className="py-[20px] relative z-10">
          {isEmpty(params.id) && (
            <span
              onClick={() => {
                if (isEmpty(contact.name)) {
                  Taro.showToast({ icon: 'none', title: '联系人为必填项' });
                  return;
                }
                let isDefault = contact.isDefault ?? 0;
                if (isEmpty(store.contacts)) {
                  isDefault = 1;
                }
                store.updateContact({ ...contact, id: uniqueId('contact_'), isDefault });
                Taro.navigateBack();
              }}
              className=" bg-[#F49C1F] text-[32px] text-white rounded-[8px] py-[16px] flex justify-center items-center"
            >
              新增
            </span>
          )}
          {params.id && (
            <>
              <span
                onClick={() => {
                  if (isEmpty(contact.name)) {
                    Taro.showToast({ icon: 'none', title: '联系人为必填项' });
                    return;
                  }
                  store.updateContact(contact);
                  Taro.navigateBack();
                }}
                className=" bg-[#F49C1F] text-[32px] text-white rounded-[8px] py-[16px] flex justify-center items-center"
              >
                保存
              </span>
              <span
                onClick={() => {
                  store.removeContact(params.id!);
                  Taro.navigateBack();
                }}
                className=" bg-white mt-[40px] text-[32px] text-main rounded-[8px] py-[16px] flex justify-center items-center border-[1px] border-solid border-[#00000073]"
              >
                删除联系人
              </span>
            </>
          )}
        </div>
      </div>
    </ConfigProvider>
  );
};
