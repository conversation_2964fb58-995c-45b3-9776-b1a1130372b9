page, html {

    #customerAddForm,
    #customerAddBillsForm {
        .formItem {
            &:not(:last-child) {
                border-bottom: solid 1px #0000001A;
            }
        }
    }

    #customerContactList,
    #customerAddressList {
        .listItem {
            padding-bottom: 24px;
            border-bottom: solid 1px #0000001A;
        }
    }

    .nut-collapse {
        border-radius: 16px;
        overflow: hidden;

        .nut-collapse-item-header::after {
            border: none;
        }
    }
}
