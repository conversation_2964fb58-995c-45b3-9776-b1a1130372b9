import { StoreEntity } from '@/components/ChooseStoreAndWarehouseModal/types/store.entity';
import { PostSelect } from '@/packages/finance/payment/list/types/post.select';
import { post } from '@/utils/request';
import { CustomerTagEntity } from '../list/types/CustomerTagEntity';

/**
 * 查询账户有权限的所有门店
 * @param params
 * @returns
 */
export const queryStore = () => {
  return post<StoreEntity[]>(`/ipmspassport/AccountFacade/queryStore`, {
    data: { status: 1 },
  });
};
/**
 * 查询门店下的所有业务员
 * @param params
 * @returns
 */
export const queryStoreAccount = (storeId: string) => {
  return post<PostSelect[]>(`/ipmspassport/AccountFacade/queryStoreAccount`, {
    data: { storeId },
  });
};

/**
 * 用户管理-根据条件查询全量标签
 *
 * @param params
 * @returns
 */
export const getTagList = async () => {
  return post<CustomerTagEntity[]>(`/ipmscst/CstTagManageFacade/getTagList`, {
    data: { "tagStatus": 0, "tagType": 1 },
  });
};
