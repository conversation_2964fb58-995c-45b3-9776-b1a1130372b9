import iconCall from '@/assets/icons/icon_call.png';
import iconEmail from '@/assets/icons/icon_email.png';
import iconPhone from '@/assets/icons/icon_phone.png';
import iconPosition from '@/assets/icons/icon_position.png';
import iconWechat from '@/assets/icons/icon_wechat.png';
import { CustomerContactEntity } from '@/components/ChooseCstModal/types/CustomerContactEntity';
import { Image } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
interface ContactItemProps {
  record: CustomerContactEntity;
  showCall?: boolean;
}
export default (props: ContactItemProps) => {
  const { record, showCall = false } = props;
  return (
    <div className="flex flex-col gap-[16px] text-secondary relative text-[28px]">
      <div className="flex gap-[20px] items-center">
        <Image width="16px" height="16px" src={iconPhone}></Image>
        <span>{record.phone ?? '-'}</span>
      </div>
      <div className="flex gap-[20px] items-center">
        <Image width="16px" height="16px" src={iconEmail}></Image>
        <span>{record.email ?? '-'}</span>
      </div>
      <div className="flex gap-[20px] items-center">
        <Image width="16px" height="16px" src={iconWechat}></Image>
        <span>{record.wechat ?? '-'}</span>
      </div>
      <div className="flex gap-[20px] items-center">
        <Image width="16px" height="16px" src={iconPosition}></Image>
        <span className="break-all flex-1 flex-shrink-0"> {record.remark ?? '-'}</span>
      </div>
      {record.phone && showCall && (
        <span
          className="absolute top-[34px] right-[32px]"
          onClick={() => {
            if (record.phone) {
              Taro.makePhoneCall({ phoneNumber: record.phone });
            }
          }}
        >
          <Image width="36px" height="36px" src={iconCall}></Image>
        </span>
      )}
    </div>
  );
};
