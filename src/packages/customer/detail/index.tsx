import iconDisabled from '@/assets/icons/icon_disabled.png';
import CustomNavBar from '@/components/CustomNavBar';
import Descriptions, { Column } from '@/components/Descriptions';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import RouterUtils from '@/utils/RouterUtils';
import { Image, SafeArea } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import { useDidShow, useRouter } from '@tarojs/taro';
import { defaultTo, isEmpty } from 'lodash';
import { useState } from 'react';
import CustomerItem from '../list/components/CustomerItem';
import { getCstDetail } from '../list/services';
import { CustomerItemRecordType } from '../list/types/CustomerItemRecordType';
import { CustomerSaveEntity } from '../list/types/CustomerSaveEntity';
import ContactItem from './components/ContactItem';
import './index.scss';
const columns: Column[] = [
  {
    title: '归属门店',
    key: 'storeName',
  },
  {
    title: '业务员',
    key: 'salesmanName',
  },
  {
    title: '创建时间',
    key: 'createTime',
  },
  {
    title: '备注',
    key: 'remark',
  },
];

export default () => {
  const { params } = useRouter();
  const { cstId } = params;
  const [detail, setDetail] = useState<CustomerSaveEntity>({});
  const [customerDetail, setCustomerDetail] = useState<CustomerItemRecordType>({});
  useDidShow(async () => {
    const result = await getCstDetail({ cstId: cstId! });
    setDetail(result);
    const { base, tags, images, settle } = result;
    setCustomerDetail({
      cstName: base?.cstName,
      cstSn: base?.cstSn,
      nickName: base?.nickName,
      tags,
      images,
      credit: settle?.credit,
    });
  });
  return (
    <div className="flex flex-col px-[28px] gap-[24px] pb-[220px]">
      <CustomNavBar title="客户详情" showBack={true} />
      <div className="px-[28px] flex flex-col relative">
        <CustomerItem source="detail" record={customerDetail}></CustomerItem>
        {detail.base?.cstStatus == 1 && (
          <span className="absolute top-[8px] right-[16px]">
            <Image src={iconDisabled} width="140px" height="140px"></Image>
          </span>
        )}
      </div>
      {/* 基本信息 */}
      <div className="flex flex-col gap-[20px]">
        <Descriptions
          className="!rounded-[16px] !bg-white text-[28px]"
          emptyText="-"
          columns={columns}
          record={detail.base}
        ></Descriptions>
      </div>
      {/* 联系人信息 */}
      {!isEmpty(detail.contacts) && (
        <div className="p-[28px] rounded-[16px] bg-white flex flex-col">
          <div className="mb-[32px] font-medium">联系人信息 </div>
          <div id="customerList" className="flex flex-col gap-[24px]">
            {detail?.contacts &&
              detail?.contacts.map((contact) => {
                return (
                  <View className="listItem">
                    <div className="flex flex-col gap-[28px]">
                      <div className="flex gap-[16px] items-center">
                        <span className="text-main text-[32px]">{contact.name}</span>
                        {contact.isDefault == 1 && (
                          <span className="px-[12px] py-[6px] bg-[#FFF4F4] text-primary text-[20px] rounded-[4px] border-[#FCAEADFF] border-solid border-[1px]">
                            默认联系人
                          </span>
                        )}
                        {contact.position && (
                          <span className="px-[12px] py-[6px] bg-[#E7F0FF] text-[#176EFFFF] text-[20px] rounded-[4px] border-[#A2C5FF] border-solid border-[1px]">
                            {contact.position}
                          </span>
                        )}
                      </div>
                      <ContactItem showCall={true} record={contact}></ContactItem>
                    </div>
                  </View>
                );
              })}
          </div>
        </div>
      )}
      {/* 地址信息 */}
      {!isEmpty(detail.addresses) && (
        <div className="p-[28px] rounded-[16px] bg-white flex flex-col">
          <div className="mb-[32px] font-medium">地址信息 </div>
          <div id="customerAddressList" className="flex flex-col">
            {detail?.addresses &&
              detail?.addresses.map((address) => {
                return (
                  <View className="listItem">
                    <div className="flex flex-col gap-[24px]">
                      <div className="flex gap-[16px] text-[32px] text-main items-center">
                        <span>{address.name}</span>
                        <span>{address.phone}</span>
                        {address.isDefault == 1 && (
                          <span className="px-[12px] py-[6px] bg-[#FFF4F4] text-primary text-[20px] rounded-[4px] border-[#FCAEADFF] border-solid border-[1px]">
                            默认地址
                          </span>
                        )}
                      </div>
                      {address.address && (
                        <span className="text-[28px] text-[#777777]">
                          {address.provinceName}
                          {address.cityName}
                          {address.prefectureName}
                          {address.address}
                        </span>
                      )}
                    </div>
                  </View>
                );
              })}
          </div>
        </div>
      )}
      {/* 结算信息 */}
      <div className="p-[28px] rounded-[16px] bg-white flex flex-col">
        <div className="mb-[28px] font-medium">结算信息 </div>
        <div className="text-[32px] text-main py-[32px] flex justify-between items-center">
          <span>期初应收</span>
          <span>{detail.settle?.orgReceivableAmount}</span>
        </div>
        {detail.settle?.credit && (
          <>
            <div className="text-[32px] text-main pt-[24px] flex  items-center  border-0 border-t border-solid border-t-[#0000001A]">
              <span>支持挂账</span>
            </div>
            <div className="flex justify-between text-[24px]">
              <div className="flex flex-col mt-2 gap-[16px] flex-[2]">
                <span className="text-secondary">挂账额度</span>
                <span className="text-main text-[48px]">￥{detail.settle?.totalAmount ?? 0}</span>
                <div className="flex gap-[16px] text-secondary">
                  <span>已用￥{detail.settle?.usedAmount ?? 0}</span>
                  <span>冻结￥{detail.settle?.freezeAmount ?? 0}</span>
                  <span>可用￥{detail.settle?.availableAmount ?? 0}</span>
                </div>
              </div>
              <div className="flex flex-col gap-[16px] mt-2 flex-[1]">
                <span className="text-secondary">账期(天)</span>
                <span className="text-main text-[48px]">{detail.settle?.creditTerms ?? 0}</span>
                <span className="text-secondary">
                  剩余{defaultTo(detail.settle?.remainTerms, detail.settle?.creditTerms ?? 0)}天
                </span>
              </div>
            </div>
          </>
        )}
      </div>
      <PermissionComponent permission='addCustomer'>
        <div className="bg-white py-[20px] px-[28px] fixed left-0 right-0 bottom-0">
          <span
            onClick={() => {
              RouterUtils.navigateTo({
                url: '/packages/customer/add/index',
                params: { cstId: detail.base?.id },
              });
            }}
            className="text-main text-[32px] border border-solid border-thirdary rounded-[8px] py-[16px] flex justify-center items-center"
          >
            编辑客户
          </span>
          <SafeArea position="bottom"></SafeArea>
        </div>
      </PermissionComponent>
    </div>
  );
};
