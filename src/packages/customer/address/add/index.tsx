import { ArrowRight } from '@nutui/icons-react-taro';
import { Address, ConfigProvider, Input, Switch } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import Taro, { useLoad, useRouter } from '@tarojs/taro';
import { useAsyncEffect } from 'ahooks';
import { find, isEmpty, join, uniqueId } from 'lodash';
import { useEffect, useState } from 'react';
import useCustomerAddStore from '../../add/store';
import { CustomerAddressEntity } from '../../list/types/CustomerAddressEntity';
import './index.scss';
import { queryDistrictAreaTree } from './services';
import { PostDistrictArea } from './types/PostDistrictArea';
import CustomNavBar from "@/components/CustomNavBar";
export default () => {
  const { params } = useRouter();
  const store = useCustomerAddStore();
  const [address, setAddress] = useState<CustomerAddressEntity & { location?: string }>({ id: '' });
  const [visible, setVisible] = useState(false);
  const [addressData, setAddressData] = useState<PostDistrictArea[]>([]);
  const [key, setKey] = useState<string>('defaultAddressKey');
  useAsyncEffect(async () => {
    const result = await queryDistrictAreaTree();
    setAddressData(result);
    // 强制刷新Address组件
    setKey(uniqueId('defaultAddressKey_'));
  }, []);

  useLoad(() => {
    let title = '';
    if (params.id) {
      title = '编辑地址';
    } else {
      title = '新增地址';
    }
    Taro.setNavigationBarTitle({ title });
  });

  useEffect(() => {
    if (params.id) {
      const item = find(store.addresses, { id: params.id });
      if (item) {
        setAddress(item);
      }
    }
  }, [params.id]);

  const addressValue = [
    address.provinceCode ?? '',
    address.cityCode ?? '',
    address.prefectureCode ?? '',
  ];
  const addressName = join(
    [address.provinceName ?? '', address.cityName ?? '', address.prefectureName ?? ''],
    '',
  );
  const FormItems = [
    {
      key: 'location',
      label: '所在地区',
      placeholder: '请选择',
      name: 'location',
      type: 'select',
      required: true,
      value: addressName,
      onClick: () => {
        setVisible(true);
      },
    },
    { key: 'address', label: '详细地址', name: 'address', value: address.address },
    { key: 'name', label: '联系人', name: 'name', value: address.name },
    {
      key: 'phone',
      label: '联系方式',
      name: 'phone',
      value: address.phone,
    },
  ];
  return (
    <ConfigProvider
      theme={{
        nutuiInputPadding: '8px 0px',
        nutuiTextareaPadding: '0px 0px',
      }}
    >
      <CustomNavBar title={params.id ? "编辑地址" : "新增地址"} showBack={true} />
      <div className="p-[28px] flex flex-col gap-[24px] pb-[120px] text-main">
        <div id="customerAddForm" className="px-[28px] rounded-[16px] bg-white flex flex-col">
          {FormItems.map((t) => {
            const { onClick, key, name, label, placeholder = '请输入', value, type, required } = t;
            return (
              <View key={key} className="formItem">
                <div className="flex justify-between items-center ">
                  <span className={`text-[32px] text-main  ${required && 'formItemRequired'}`}>
                    {label}
                  </span>
                  <span onClick={onClick} className="flex items-center gap-[24px]">
                    <Input
                      disabled={type == 'select'}
                      placeholder={placeholder}
                      placeholderClass="text-[#0000004D] text-[32px]"
                      align="right"
                      value={value as string}
                      onChange={(v) => {
                        if (type !== 'select') {
                          setAddress({ ...address, [name]: v });
                        }
                      }}
                    ></Input>
                    {type == 'select' && (
                      <ArrowRight color="#0000004D" height="26px" width="16px"></ArrowRight>
                    )}
                  </span>
                </div>
              </View>
            );
          })}
        </div>

        <div className="p-[28px] rounded-[16px] bg-white flex justify-between items-center">
          <span className="text-[32px]">设为默认地址</span>
          <Switch
            checked={address.isDefault == 1}
            onChange={(value) => {
              setAddress({ ...address, isDefault: value ? 1 : 0 });
            }}
          ></Switch>
        </div>

        <div className="py-[20px]">
          {isEmpty(params.id) && (
            <span
              onClick={() => {
                if (isEmpty(address.provinceCode)) {
                  Taro.showToast({ icon: 'none', title: '所在地区为必填项' });
                  return;
                }
                let isDefault = address.isDefault ?? 0;
                if (isEmpty(store.addresses)) {
                  isDefault = 1;
                }
                store.updateAddress({ ...address, id: uniqueId('address_'), isDefault });
                Taro.navigateBack();
              }}
              className=" bg-[#F49C1FFF] text-[32px] text-white rounded-[8px] py-[16px] flex justify-center items-center"
            >
              新增
            </span>
          )}
          {params.id && (
            <>
              <span
                onClick={() => {
                  if (isEmpty(address.provinceCode)) {
                    Taro.showToast({ icon: 'none', title: '所在地区为必填项' });
                    return;
                  }
                  let isDefault = address.isDefault ?? 0;
                  if (isEmpty(store.addresses)) {
                    isDefault = 1;
                  }
                  store.updateAddress({ ...address, isDefault });
                  Taro.navigateBack();
                }}
                className=" bg-[#F49C1F] text-[32px] text-white rounded-[8px] py-[16px] flex justify-center items-center"
              >
                保存
              </span>
              <span
                onClick={() => {
                  store.removeAddress(params.id!);
                  Taro.navigateBack();
                }}
                className=" bg-white mt-[40px] text-[32px] text-main rounded-[8px] py-[16px] flex justify-center items-center border-[1px] border-solid border-[#00000073]"
              >
                删除地址
              </span>
            </>
          )}
        </div>
      </div>
      {addressData && (
        <Address
          key={key}
          visible={visible}
          options={addressData}
          title="请选择地址"
          value={addressValue}
          optionKey={{
            valueKey: 'areaId',
            textKey: 'areaName',
            childrenKey: 'children',
          }}
          onChange={(_, params: { value: string; text: string }[]) => {
            if (params) {
              console.log(params);
              const [
                { value: provinceCode, text: provinceName },
                { value: cityCode, text: cityName },
                { value: prefectureCode, text: prefectureName },
              ] = params;
              console.log(provinceCode, provinceName);
              console.log(cityCode, cityName);
              console.log(prefectureCode, prefectureName);
              setAddress({
                ...address,
                provinceCode,
                provinceName,
                cityCode,
                cityName,
                prefectureCode,
                prefectureName,
              });
            }
          }}
          onClose={() => setVisible(false)}
        />
      )}
    </ConfigProvider>
  );
};
