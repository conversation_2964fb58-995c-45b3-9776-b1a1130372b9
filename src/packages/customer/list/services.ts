import { CustomerEntity } from "@/components/ChooseCstModal/types/CustomerEntity";
import { PageRequestParamsType } from "@/types/PageRequestParamsType";
import { post } from "@/utils/request";
import { PageResponseDataType } from "../../../../types/PageResponseDataType";
import { CustomerAccountChangeLogType } from "./types/CustomerAccountChangeLogType";
import { CustomerSaveEntity } from "./types/CustomerSaveEntity";
import { GetCstListRequest } from "./types/get.cst.list.request";
import { GetCstPagedRequest } from "./types/get.cst.paged.request";

/**
 * 用户管理-分页查询
 *
 * @param params
 * @returns
 */
export const getCstPaged = async (params: PageRequestParamsType & GetCstPagedRequest) => {
    return post<PageResponseDataType<CustomerEntity>>(`/ipmscst/CstManageFacade/getCstPaged`, {
        data: params,
    });
};


/**
 * 客户模糊查询
 */
export const getCstList = (params: GetCstListRequest) => {
    return post<CustomerEntity[]>(`/ipmscst/CstManageFacade/getCstList`, {
        data: params,
    });
};

/**
 * 用户管理-保存客户信息
 *
 * @param params
 * @returns
 */
export const saveCst = async (params: CustomerSaveEntity) => {
    return post<string>(`/ipmscst/CstManageFacade/saveCst`, {
        data: params,
    });
};
/**
 * 用户管理-获取客户详情
 *
 * @param params
 * @returns
 */
export const getCstDetail = async (params: { cstId: string }) => {
    return post<CustomerSaveEntity>(`/ipmscst/CstManageFacade/getCstDetail`, {
        data: params,
    });
};

/**
 * 用户管理-启用、禁用客户
 *
 * @param params
 * @returns
 */
export const changeStatus = async (params: { cstId: string; status: number }) => {
    return post<boolean>(`/ipmscst/CstManageFacade/changeStatus`, {
        data: params,
    });
};
/**
 * 用户管理-挂账账户操作记录查询
 * @param params
 * @returns
 */
export const queryAccountChangeLogPage = async (
    params: PageRequestParamsType & {
        customerId: string;
        accountType: number;
    },
) => {
    return post<PageResponseDataType<CustomerAccountChangeLogType>>(
        `/ipmsaccount/queryAccountChangeLogPage`,
        {
            data: params,
        },
    );
};