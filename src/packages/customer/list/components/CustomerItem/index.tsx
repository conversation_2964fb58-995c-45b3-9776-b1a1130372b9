import iconCustDef from '@/assets/icons/icon_cust_def.png';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import { Image } from '@nutui/nutui-react-taro';
import classNames from 'classnames';
import { defaultTo, size } from 'lodash';
import { useIntl } from 'react-intl';
import { CustomerItemRecordType } from '../../types/CustomerItemRecordType';
export interface CustomerItemProps {
  record: CustomerItemRecordType;
  source: string;
}
export default (props: CustomerItemProps) => {
  const intl = useIntl();
  const { record, source = 'detail' } = props;
  let tags = record.tags ?? [];
  if (record.credit) {
    tags = [{ tagName: intl.formatMessage({ id: 'customer.list.tag.credit' }), id: '0' }, ...tags];
  }
  const cstName = record.cstName;
  const cstSn = record.cstSn;
  const nickName = record.nickName;
  const defaultContact = record?.contacts?.[0];
  const usedAmount = record.usedAmount;
  const availableAmount = record.availableAmount;
  const creditAble = record.credit;
  const receivableAmount = defaultTo(record.receivableAmount, '-');
  const imageUrl = defaultTo(record?.images?.[0]?.url, iconCustDef);
  // const tagnames = tagList.map((t) => t.tagName).join('');

  return (
    <div className="flex gap-[20px]">
      <span className="flex-shrink-0">
        <Image radius={4} src={imageUrl} width="70px" height="70px"></Image>
      </span>
      <div className="flex-1 flex flex-col flex-grow">
        <div className="pb-[16px] flex flex-col">
          <span className="text-main text-[32px] font-medium">{cstName}</span>
          <div className="flex text-[26px] text-thirdary my-[12px]">
            {source == 'list' && (
              <ItemsWithDivider
                items={[
                  defaultTo(defaultContact?.name, intl.formatMessage({ id: 'customer.list.item.noContactPerson' })),
                  defaultTo(defaultContact?.phone, intl.formatMessage({ id: 'customer.list.item.noContactInfo' })),
                ]}
              />
            )}
            {source == 'detail' && (
              <ItemsWithDivider items={[defaultTo(cstSn, ''), defaultTo(nickName, '')]} />
            )}
          </div>
          <div className="flex gap-[12px] flex-nowrap">
            {tags &&
              tags.slice(0, 4).map((t) => {
                const className = classNames(
                  'truncate max-w-[114px] text-[20px] px-[12px] py-[6px] border rounded-[4px]',
                  {
                    'bg-[#FFF4F4FF] text-primary border-[#FCAEADFF]': t.id == '0',
                    'bg-[#E7F0FFFF] text-[#176EFFFF] border-[#A2C5FFFF]': t.id !== '0',
                  },
                );
                return <span className={className}>{t.tagName}</span>;
              })}

            {size(tags) > 4 && (
              <span className="num text-[20px] px-[12px] py-[6px] border rounded-[4px] bg-[#E7F0FFFF] text-[#176EFFFF] border-[#A2C5FFFF]">
                {`+${tags.length - 4}`}
              </span>
            )}
          </div>
        </div>
        {creditAble && source == 'list' && (
          <div className="text-secondary text-[24px] flex flex-col gap-[8px]  pt-[16px] border-t-[1px] border-0 border-t-[#00000014] border-solid">
            <span>
              {intl.formatMessage({ id: 'customer.list.item.creditAmount' }, { used: defaultTo(usedAmount, 0), available: defaultTo(availableAmount, 0) })}
            </span>
            <span>{intl.formatMessage({ id: 'customer.list.item.receivableAmount' }, { amount: defaultTo(receivableAmount, 0) })}</span>
          </div>
        )}
      </div>
    </div>
  );
};
