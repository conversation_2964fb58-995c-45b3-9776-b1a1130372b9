export interface CustomerAccountChangeLogType {
  afterJson: string; // Information after modification
  logType: number; // Log type
  beforeJson: string; // Information before modification
  changeJson: string; // Information about changes
  customerId: string; // Customer ID
  accountType: number; // Account type
  id: number; // ID
  createTime: Date; // Creation time
  updateTime: Date; // Update time
  isDelete: number; // Deletion status
  updatePerson: string; // Person who updated
  createPerson: string; // Person who created
  memberId: string; // Member ID
}
