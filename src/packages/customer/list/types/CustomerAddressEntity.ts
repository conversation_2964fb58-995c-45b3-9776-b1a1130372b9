export interface CustomerAddressEntity {
  /** 省编码 */
  provinceCode?: string;
  /** 省名称 */
  provinceName?: string;
  /** 市编码 */
  cityCode?: string;
  /** 市名称 */
  cityName?: string;
  /** 区县编码 */
  prefectureCode?: string;
  /** 区县名称 */
  prefectureName?: string;
  /** 1:默认地址 0:非默认地址 */
  isDefault?: number;
  /** 联系人电话 */
  phone?: string;
  /** 详细地址 */
  address?: string;
  /** 联系人姓名 */
  name?: string;
  /** 编辑传入id */
  id?: string;
  /**省市区编码 */
  addressCode?: string[];
}
