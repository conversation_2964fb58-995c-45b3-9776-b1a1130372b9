import { CustomerContactEntity } from "./CustomerContactEntity";
import { CustomerImageEntity } from "./CustomerImageEntiry";
import { CustomerTagEntity } from "./CustomerTagEntity";

export interface CustomerItemRecordType {
    cstName?: string;
    cstSn?: string;
    nickName?: string;
    contacts?: CustomerContactEntity[];
    usedAmount?: number;
    availableAmount?: number;
    receivableAmount?: number;
    credit?: boolean;
    images?: CustomerImageEntity[];
    tags?: CustomerTagEntity[];
}