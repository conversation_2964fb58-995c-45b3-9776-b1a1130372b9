import {post} from '@/utils/request';
import {PageResponseDataType} from "@/types/PageResponseDataType";
import {FinPaymentEntity} from "@/packages/finance/payment/list/types/FinPaymentEntity";
import {PageRequestParamsType} from "@/types/PageRequestParamsType";
import {PaymentFlowDetailEntity} from "@/packages/finance/payment/list/types/PaymentFlowDetailEntity";
import {SupplierList} from "@/packages/finance/payment/list/types/supplier.post.entity";
import {FinPayableEntity} from "@/components/ChooseSupplierModal/types/FinPayableEntity";

/**
 * 付款分页查询
 *
 * @param params
 * @returns
 */
export const queryPaymentPage = async (params: Partial<FinPaymentEntity> & PageRequestParamsType) => {
  return post<PageResponseDataType<FinPaymentEntity>>(`/ipmsaccount/queryPaymentPage`, {
    data: params,
  });
};
/**
 * 付款流水
 *
 * @param params
 * @returns
 */
export const queryPaymentFlowList = async (params: string) => {
  return post<PaymentFlowDetailEntity>(`/ipmsaccount/queryPaymentFlowList`, {
    data: {paymentSerialNumber: params},
  });
};

/**
 * 查询供应商 下来框
 * @param params
 * @returns
 */
export const querySupplierList = async ({supplierStatus = 1,}: { supplierStatus?: number; }) => {
  return post<SupplierList[]>(`/ipmspurchase/vendor/SupplierFacade/querySupplierList`, {
    data: {supplierStatus},
  });
};

/**
 * 应付列表
 *
 * @param params
 * @returns
 */
export const queryPayableList = async (params: Partial<FinPayableEntity> & PageRequestParamsType) => {
  return post<FinPayableEntity[]>(`/ipmsaccount/queryPayableList`, {
    data: params,
  });
};

/**
 * 实付
 *
 * @param params
 * @returns
 */
export const paymentConfirmation = async (params: { paymentAccountId: string; sellerId: string | undefined; paymentAccountName: any; totalPaymentAmountYuan: number; sellerName: any; finPaymentOrderDetailCmdList: { ledgerType: number | undefined; payableId: string | undefined; paymentAmountYuan: string | undefined }[]; memberAccountName: any; ledgerType: number; remark: string }) => {
  return post<PageResponseDataType<string>>(`/ipmsaccount/paymentConfirmation`, {
    data: params,
  });
};
