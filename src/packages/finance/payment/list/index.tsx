import '@/assets/lightPage.scss';
import CalendarRangeCardChoose from '@/components/CalendarRangeCardChoose';
import FilterSupplierPicker from '@/components/FilterSupplierPicker';
import MenuWrap from '@/components/MenuWrap';
import FunPagination from '@/components/Pagination/FunPagination';
import PaymentItem from '@/packages/finance/payment/components/paymentItem';
import {FinPaymentEntity} from '@/packages/finance/payment/list/types/FinPaymentEntity';
import {queryPaymentPage} from '@/packages/finance/payment/services';
import {QueryPostListRequest} from '@/packages/stocks/output/list/types/query.post.list.request';
import {Add} from '@nutui/icons-react-taro';
import {Menu, SafeArea} from '@nutui/nutui-react-taro';
import {navigateTo, useDidShow} from '@tarojs/taro';
import dayjs from 'dayjs';
import _ from 'lodash';
import {useRef, useState} from 'react';
import PermissionComponent from "@/pages/splash/PermissionComponent";

export default function Index() {
  const calendarItemRef = useRef<any>(null);
  const [params, setParams] = useState<any>({});
  const [onLoadFlag, setOnLoadFlag] = useState<boolean>(false);
  const supplierMenuRef = useRef<any>();

  useDidShow(() => {
    if (onLoadFlag) {
      setOnLoadFlag(false);
      setParams((prevData) => ({...prevData, time: _.now()}));
    }
  });

  /**
   * 列表查询
   * @param page
   */
  const fetchData = (param?: QueryPostListRequest): Promise<FinPaymentEntity[]> => {
    return queryPaymentPage({
      ...param,
      pageSize: 10,
    }).then((result) => result?.data ?? []);
  };

  return (
    <div className="pt-[16px] flex flex-col h-screen">
      <div className="flex justify-between">
        <MenuWrap
          menu={
            <Menu>
              <Menu.Item title="付款时间" ref={calendarItemRef}>
                <CalendarRangeCardChoose
                  value={
                    params.startBusinessTime
                      ? [
                        dayjs(params.startBusinessTime).toDate(),
                        dayjs(params.endBusinessTime).toDate(),
                      ]
                      : []
                  }
                  onChange={(value) => {
                    if (value) {
                      setParams({
                        ...params,
                        startBusinessTime: value[0]
                          ? dayjs(value[0]).format('YYYY-MM-DD 00:00:00')
                          : undefined,
                        endBusinessTime: value[1]
                          ? dayjs(value[1]).format('YYYY-MM-DD 23:59:59')
                          : undefined,
                      });
                      calendarItemRef.current?.toggle(false);
                    }
                  }}
                />
              </Menu.Item>
              <Menu.Item title="供应商" ref={supplierMenuRef}>
                <FilterSupplierPicker
                  supplierId={params.sellerId}
                  onChange={(sellerId) => {
                    setParams({...params, sellerId: sellerId === '' ? null : sellerId});
                    supplierMenuRef.current?.toggle(false);
                  }}
                />
              </Menu.Item>
            </Menu>
          }
        />
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <PermissionComponent permission={'addPayment'}>
            <Add
              className="mx-[28px]"
              onClick={() => {
                navigateTo({url: '/packages/finance/payment/add/index'});
                setOnLoadFlag(true);
              }}
            />
          </PermissionComponent>
        </div>
      </div>
      <div className="flex-1 min-h-0 overflow-scroll mt-[24px]">
        <FunPagination
          fetchData={fetchData}
          params={params}
          renderItem={(record) => <PaymentItem record={record}/>}
        />
      </div>
      <SafeArea position={'bottom'}/>
    </div>
  );
}
