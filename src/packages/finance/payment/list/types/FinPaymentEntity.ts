export interface FinPaymentEntity {
  totalPaymentAmountYuan?: string;
  paymentAccountId?: string;
  buyerName?: string;
  totalPaymentAmount?: string;
  paymentAccountType?: string;
  businessTime?: string;
  buyerId?: string;
  ledgerType?: string;
  serialNumber?: string;
  createPerson?: string;
  remark?: string;
  paymentAccountName?: string;
  sellerId?: string;
  sellerName?: string;
  storeName?: string;
  endBusinessTime?: string;
  startBusinessTime?: string;
}
