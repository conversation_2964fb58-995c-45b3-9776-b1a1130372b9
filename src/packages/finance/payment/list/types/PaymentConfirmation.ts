import {PaymentConfirmationOrderDetail} from "@/packages/finance/payment/list/types/PaymentConfirmationOrderDetail";

export interface PaymentConfirmation {
  buyerName?: string;
  paymentAccountName?: string;
  totalPaymentAmountYuan?: string;
  paymentAccountId?: string;
  memberAccountName?: number;
  sellerId?: string;
  sellerName?: string;
  businessTime?: string;
  buyerId?: string;
  ledgerType?: string;
  remark?: string;
  finPaymentOrderDetailCmdList?: PaymentConfirmationOrderDetail[];
}
