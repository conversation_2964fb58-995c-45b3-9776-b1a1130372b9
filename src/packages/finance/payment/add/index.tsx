import '@/assets/lightPage.scss';
import AccountSelectModal from '@/components/AccountSelectModal';
import {SupplierPostEntity} from '@/components/ChooseSupplierModal/types/supplier.post.entity';
import SupplierCard from '@/components/SupplierCard';
import {ArrowDown, IconFont} from '@nutui/icons-react-taro';
import {Button, Divider, Input, InputNumber, Price, Space, TextArea} from '@nutui/nutui-react-taro';
import classNames from 'classnames';
import {useState} from 'react';
import Card from "@/components/Card";
import {useAsyncEffect} from "ahooks";
import {paymentConfirmation, queryPayableList} from "@/packages/finance/payment/services";
import {FinPayableEntity} from "@/components/ChooseSupplierModal/types/FinPayableEntity";
import infoIcon from "./images/info.svg"
import Taro from "@tarojs/taro";
import _, {isEmpty} from "lodash";
import './index.scss';
import CustomNavBar from "@/components/CustomNavBar";
import {checkAmount, showToast} from "@/utils/financeUtils";

export default function Index() {
  const [isAdding, setIsAdding] = useState<boolean>(false);
  const [chooseSupplier, setChooseSupplier] = useState<SupplierPostEntity>();
  const [visibleAccount, setVisibleAccount] = useState<boolean>(false);
  const [accountId, setAccountId] = useState<string>(null);
  const [accountName, setAccountName] = useState<string>(null);
  const [paymentList, setPaymentList] = useState<FinPayableEntity[]>([]);
  const [remark, setRemark] = useState('');
  const [totalPaymentAmountYuan, setTotalPaymentAmountYuan] = useState<string>('');
  const [currTotalPaymentAmountYuan, setCurrTotalPaymentAmountYuan] = useState<string>('');

  useAsyncEffect(async () => {
    await chooseSupplierHandle();
  }, [chooseSupplier]);

  const chooseSupplierHandle = async () => {
    if (!chooseSupplier) {
      setChooseSupplier(null);
      return;
    }
    setPaymentList([]);
    setRemark('');
    setTotalPaymentAmountYuan('');
    setCurrTotalPaymentAmountYuan('');

    const data = await queryPayableList({sellerId: chooseSupplier.supplierInfo?.id, payableFlag: 1});
    setPaymentList(data ?? []);
  }

  const handleUpdate = (payableId, inputValue) => {
    const updatedPaymentList = paymentList.map(item =>
      item.id === payableId
        ? {...item, currPayAmount: inputValue}
        : item
    );

    let totalPaymentAmount = 0;
    updatedPaymentList.forEach(item => {
      if (item.id == payableId) {
        item.currPayAmount = inputValue;
      }
      if (item.currPayAmount) {
        totalPaymentAmount = _.round(_.add(Number(item.currPayAmount), totalPaymentAmount), 2);
      }
    })
    setPaymentList(updatedPaymentList);
    setCurrTotalPaymentAmountYuan(totalPaymentAmount.toString());
  };

  const autoAssignOrders = () => {
    let inTotalPaymentAmountYuan = Number(totalPaymentAmountYuan);
    const oldTotalPaymentAmountYuan = Number(totalPaymentAmountYuan);
    if (!inTotalPaymentAmountYuan) {
      showToast('付款金额不能为空！');
      return;
    }
    if (!chooseSupplier) {
      showToast('请先选择供应商！');
      return;
    }
    if (inTotalPaymentAmountYuan <= 0) {
      showToast('付款金额小于等于0，请手动分配核销金额！');
      return;
    }
    const newPaymentList = paymentList.map(item => {
      if (inTotalPaymentAmountYuan == 0) {
        item.currPayAmount = undefined;
        return item;
      }
      if (inTotalPaymentAmountYuan >= item.remainPayableAmountYuan) {
        item.currPayAmount = item.remainPayableAmountYuan?.toString();
        inTotalPaymentAmountYuan = _.round(_.subtract(inTotalPaymentAmountYuan, item.remainPayableAmountYuan as number), 2);
        return item;
      }

      item.currPayAmount = inTotalPaymentAmountYuan.toString();
      inTotalPaymentAmountYuan = 0;
      return item;
    })
    setPaymentList(newPaymentList);
    const currTotalPaymentAmount = _.round(_.subtract(oldTotalPaymentAmountYuan, inTotalPaymentAmountYuan), 2);
    setCurrTotalPaymentAmountYuan(currTotalPaymentAmount.toString());
  };

  const remarkTitleWithCount = (
    <>
      备注
      <span
        style={{
          float: 'right',
          fontWeight: 'normal',
          fontStyle: 'normal',
          color: '#666666'
        }}
      >{remark.length}/100</span>
    </>
  );

  const addPayment = async () => {
    if (!accountId) {
      showToast('尚未选择付款账户，请先选择！');
      return false;
    }
    const selectedOrderDetailList = paymentList.filter(item => item.currPayAmount).map(item => ({
      payableId: item.id,
      paymentAmountYuan: item.currPayAmount,
      ledgerType: item.ledgerType
    }));
    if (!selectedOrderDetailList || selectedOrderDetailList.length == 0) {
      showToast('本次付款尚未选择具体的单据，请检查！');
      return false;
    }
    const totalPayedAmount = selectedOrderDetailList.reduce((accumulator, currentItem) => {
      return _.add(accumulator, Number(currentItem.paymentAmountYuan));
    }, 0);
    if (totalPaymentAmountYuan != totalPayedAmount) {
      showToast('各单据付款总金额与输入付款金额不一致，请修改！');
      return false;
    }

    setIsAdding(true);
    const result = await paymentConfirmation({
      sellerId: chooseSupplier?.supplierInfo?.id,
      totalPaymentAmountYuan: Number(totalPaymentAmountYuan),
      paymentAccountId: accountId,
      remark: remark,
      memberAccountName: accountName,
      paymentAccountName: accountName,
      sellerName: chooseSupplier?.supplierInfo?.supplierName,
      ledgerType: Number(totalPaymentAmountYuan) > 0 ? 2 : 1,
      finPaymentOrderDetailCmdList: selectedOrderDetailList
    });
    if (result) {
      Taro.showToast({
        title: '新增付款操作成功！',
        icon: 'none',
        duration: 2000,
      }).then(() => Taro.navigateBack());
    }
    setIsAdding(false);
  }

  const systemInfo = Taro.getSystemInfoSync();
  const { windowHeight } = systemInfo;

  return (
    <div>
      <div className="px-[28px]">
        <CustomNavBar title="新增付款"/>
        <SupplierCard
          className="mt-[24px]"
          onConfirm={setChooseSupplier}
          currentSupplier={chooseSupplier}
        />

        <div className="rounded-[16px] mt-[24px] px-[28px] pb-[12px] bg-white">
          <div className="text-[32px] font-medium text-block pb-[8px] pt-[28px]">付款金额</div>
          <div className="flex grow text-[#666666] pt-[8px] justify-between mb-[24px]">
            <Input
              className="w-1/2"
              style={{'--nutui-input-font-size': '24px', '--nutui-input-padding': '10px 0px'}}
              value={totalPaymentAmountYuan}
              placeholder="0.00"
              onChange={(input) => {
                if(!checkAmount(input)){
                  return;
                }
                setTotalPaymentAmountYuan(input)
              }}
            />
            <div className="flex justify-between items-center">
              <div></div>
              <div className="text-[32px] mr-[10px]" onClick={() => setVisibleAccount(true)}>
                {accountName ?? '付款账户'}
              </div>
              <div className="flex items-center" onClick={() => setVisibleAccount(true)}>
                <ArrowDown className={classNames('self-center', {'opacity-[0.5]': !accountId})}/>
              </div>
            </div>
          </div>
          <Button className="text-[32px] w-full" onClick={() => autoAssignOrders()}>自动分配</Button>
        </div>
      </div>

      <div>
        {chooseSupplier &&
          <>
            <div style={{overflowY: 'auto', maxHeight: `${windowHeight - 490}px`}}>
              <Card title="核销订单" className="my-0">
                {paymentList.map((item) => (
                  <div>
                    <div className="flex justify-start py-[24px]">
                      <div className="px-[20px] flex flex-col flex-1">
                        <div className=" text-[32px] font-medium text-[#111111]">{item.orderNo}</div>
                        <div className="flex grow text-[#666666] pt-[8px] justify-between">
                          <div className="text-[28px] w-1/2">
                            {item.buyerName}
                          </div>
                          <div className="text-[28px] w-1/2">
                            {item.billDate}
                          </div>
                        </div>
                        <div className="flex  items-center grow text-[28px] text-[#666666] pt-[8px] justify-between">
                          <div className="w-1/2 whitespace-nowrap">
                            未付：
                            <Price
                              price={item?.remainPayableAmountYuan ?? 0}
                              size="normal"
                              style={{color: '#666666'}}
                            />
                          </div>
                          <div className="flex items-center w-1/2 justify-between">
                            <span className="w-1/2">本次核销</span>
                            <Input
                              type="text"
                              style={{
                                '--nutui-input-font-size': '14',
                                '--nutui-input-padding': '0px',
                                '--nutui-input-border-bottom-width': '1px'
                              }}
                              placeholder="0.00"
                              value={item.currPayAmount}
                              onChange={(input) => {
                                if(!checkAmount(input)){
                                  return;
                                }
                                handleUpdate(item.id, input);
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <Divider/>
                  </div>
                ))}
              </Card>

              <Card title={remarkTitleWithCount} style={{display: 'flex', flexDirection: 'column'}}>
                <TextArea
                  onChange={(e) => setRemark(e)}
                  value={remark ?? ''}
                  placeholder="请输入备注信息"
                  maxLength={100}
                  autoSize={false}
                  style={{height: '40px', '--nutui-textarea-padding': '0px'}}
                />
              </Card>
            </div>

            <div className="empty:hidden">
              {Number(totalPaymentAmountYuan ?? 0) != Number(currTotalPaymentAmountYuan ?? 0) &&
                <div className="fixed justify-between payment-info z-20">
                  <IconFont name={infoIcon} className="inline-block w-auto h-auto"/>
                  <span className="inline-block">本次核销合计需等于付款金额</span>
                </div>
              }
              <div className="fixed justify-between left-0 right-0 bottom-[24px] bg-white z-10">
                <div className="flex justify-between items-center h-[150px]">
                  <div className="flex flex-col items-start px-[28px]">
                    <div className="flex flex-grow">
                      <span className="opacity-60">核销合计：</span>
                      <Price
                        price={currTotalPaymentAmountYuan ?? 0}
                        size="large"
                        style={{fontWeight: 'bold'}}
                      />
                    </div>
                    <div className="flex flex-grow">
                      <span className="opacity-45">付款金额：</span>
                      <Price
                        price={totalPaymentAmountYuan ?? 0}
                        size="normal"
                        style={{color: 'rgba(0,0,0,0.45)'}}
                      />
                    </div>
                  </div>
                  <Space className="px-[28px] items-center">
                    <Button
                      loading = {isAdding}
                      type="primary"
                      onClick={() => addPayment()}
                    >
                      确定
                    </Button>
                  </Space>
                </div>
              </div>
            </div>
          </>
        }
      </div>

      <AccountSelectModal
        title="选择付款账户"
        handleClose={() => setVisibleAccount(false)}
        handleConfirm={(option: { title: string; value: string }) => {
          setAccountId(option.value);
          setAccountName(option.title);
          setVisibleAccount(false);
        }}
        visibleAccount={visibleAccount}
        accountId={accountId}
      />

    </div>
  );
}
