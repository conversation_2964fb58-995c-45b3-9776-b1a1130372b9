import Card from '@/components/Card';
import CustomNavBar from '@/components/CustomNavBar';
import { FinPaymentEntity } from '@/packages/finance/payment/list/types/FinPaymentEntity';
import { FinPaymentFlowEntity } from '@/packages/finance/payment/list/types/FinPaymentFlowEntity';
import { queryPaymentFlowList } from '@/packages/finance/payment/services';
import { Divider, Price } from '@nutui/nutui-react-taro';
import { useLoad, useRouter } from '@tarojs/taro';
import { useState } from 'react';

export default function Index() {
  const [finPaymentRo, setFinPaymentRo] = useState<FinPaymentEntity | undefined>(null);
  const [finPaymentFlowRoList, setFinPaymentFlowRoList] = useState<FinPaymentFlowEntity[]>([]);

  const router = useRouter();
  const { paymentSerialNumber } = router.params;

  useLoad(async () => {
    if (!paymentSerialNumber) {
      return;
    }
    const paymentFlow = await queryPaymentFlowList(paymentSerialNumber);
    setFinPaymentRo(paymentFlow?.finPaymentRo);
    setFinPaymentFlowRoList(
      paymentFlow?.finPaymentFlowRoList?.map((flow) => ({
        ...flow,
        storeName: paymentFlow?.finPaymentRo?.storeName,
      })),
    );
  });

  return (
    <div className="h-screen flex flex-col">
      <CustomNavBar title="付款详情" />
      <div className="pt-[32px] pl-[28px] text-[48px] font-medium text-block opacity-90">
        {finPaymentRo?.sellerName}
      </div>
      <div className="pt-[12px] pl-[28px] text-[28px]  text-block opacity-60">
        {finPaymentRo?.serialNumber}
      </div>
      <Card>
        <div className="flex justify-between">
          <div className="flex flex-col items-left gap-[8px] w-1/2 text-block">
            <div className="text-[28px] opacity-60">付款金额</div>
            <Price
              size="normal"
              price={finPaymentRo?.totalPaymentAmountYuan ?? 0}
              style={{
                '--nutui-price-integer-medium-size': '48px',
                '--nutui-price-decimal-medium-size': '48px',
                color: 'rgba(0,0,0,0.9)'
            }}
            />
          </div>
          <div className="flex flex-col items-left gap-[8px] w-1/2 text-block">
            <div className="text-[28px] opacity-60">付款账户</div>
            <div className="text-[48px] opacity-90">{finPaymentRo?.paymentAccountName}</div>
          </div>
        </div>
        <Divider />
        <div className="flex mt-[20px] py-[12px] text-black text-[28px]">
          <span className=" text-[#666666] w-[140px]">付款门店：</span>
          <span className="text-[#111111]">{finPaymentRo?.storeName}</span>
        </div>
        <div className="flex py-[12px] text-black text-[28px]">
          <span className="opacity-60 w-[140px]">付款时间：</span>
          <span className="opacity-90">{finPaymentRo?.businessTime}</span>
        </div>
        <div className="flex py-[12px] text-black text-[28px]">
          <span className="opacity-60 w-[112px]">制单人：</span>
          <span className="opacity-90">{finPaymentRo?.createPerson}</span>
        </div>
        <div className="flex py-[12px] text-[28px]">
          <span className="opacity-60 w-[84px]">备注：</span>
          <span className="opacity-90">{finPaymentRo?.remark}</span>
        </div>
      </Card>
      <Card title="核销订单">
        {finPaymentFlowRoList.map((item) => (
          <div>
            <div className="flex justify-start py-[24px] text-block opacity-90 ">
              <div className="px-[20px] flex flex-col flex-1">
                <div className=" text-[32px] font-medium">{item.orderNo}</div>
                <div className=" py-[12px] text-[28px] opacity-60">{item.storeName}</div>
                <div className="flex grow pt-[8px] justify-between">
                  <div className="text-[28px] flex grow w-1/2 items-center">
                    <span className="opacity-60">订单金额：</span>
                    <Price
                      price={item?.orderAmountYuan ?? 0}
                      size="normal"
                      style={{ color: 'rgba(0,0,0,0.9)' }}
                    />
                  </div>
                  <div className="text-[28px] flex grow w-1/2 items-center">
                    <span className="opacity-60">核销金额：</span>
                    <Price
                      price={item?.paymentAmountYuan ?? 0}
                      size="normal"
                      style={{ color: 'rgba(0,0,0,0.9)' }}
                    />
                  </div>
                </div>
              </div>
            </div>
            <Divider />
          </div>
        ))}
      </Card>
    </div>
  );
}
