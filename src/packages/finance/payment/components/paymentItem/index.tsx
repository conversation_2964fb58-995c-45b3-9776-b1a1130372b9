import {
  Button,
  Divider,
  Input,
  InputNumber,
  Popup,
  Price,
  SafeArea,
  Space, Tag,
} from '@nutui/nutui-react-taro';
import Taro, {navigateTo} from '@tarojs/taro';
import _ from 'lodash';
import {useEffect, useState} from 'react';
import {FinPaymentEntity} from "@/packages/finance/payment/list/types/FinPaymentEntity";
import {OrderStatus} from "@/packages/sales/order/list/types/OrderStatus";
import {PayStatus} from "@/packages/sales/order/list/types/PayStatus";
import {PaymentStatus} from "@/packages/sales/order/list/types/PaymentStatus";

export interface Index {
  itemId: string;
  price: number;
  number: number;
}

export interface PaymentItemProps {
  record: FinPaymentEntity;
}

const PaymentItem = (props: PaymentItemProps) => {
  const {record} = props;

  return (
    <div
      className="bg-white rounded-[16px] mb-[24px] mx-[28px] px-[28px] py-[20px]"
      onClick={() => navigateTo({url: '/packages/finance/payment/detail/index?paymentSerialNumber=' + record.serialNumber})}
    >
      <div className="flex justify-between pb-[16px]">
        <span>{record.sellerName}</span>
      </div>
      <Divider />
      <div className="flex mt-[24px]">
        <div className="flex-1 text-secondary text-[28px] leading-6">
          <div>付款单号&emsp;{record.serialNumber}</div>
          <div>付款时间&emsp;{record.businessTime}</div>
          <div>制单人&emsp;&emsp;{record.createPerson}</div>
        </div>
        <div className="flex flex-col justify-center items-end">
          <div>
            <Price price={record.totalPaymentAmountYuan} size="normal" style={{ color: 'rgba(0,0,0,0.9)'}}/>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentItem;
