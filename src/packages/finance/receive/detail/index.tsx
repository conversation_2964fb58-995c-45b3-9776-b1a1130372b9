import Card from '@/components/Card';
import CustomNavBar from '@/components/CustomNavBar';
import { Divider, Price } from '@nutui/nutui-react-taro';
import { useLoad, useRouter } from '@tarojs/taro';
import { useState } from 'react';
import {queryReceivedFlowList} from "@/packages/finance/receive/services";
import {ReceivedEntity} from "@/packages/finance/receive/list/types/ReceivedEntity";
import {ReceivedFlowEntity} from "@/packages/finance/receive/list/types/ReceivedFlowEntity";

export default function Index() {
  const [finReceivedRo, setFinReceivedRo] = useState<ReceivedEntity | undefined>(null);
  const [finReceivedFlowRoList, setFinReceivedFlowRoList] = useState<ReceivedFlowEntity[]>([]);

  const router = useRouter();
  const { receiveSerialNumber } = router.params;

  useLoad(async () => {
    if (!receiveSerialNumber) {
      return;
    }
    const receiveFlow = await queryReceivedFlowList(receiveSerialNumber);
    setFinReceivedRo(receiveFlow?.finReceivedRo);
    setFinReceivedFlowRoList(
      receiveFlow?.finReceivedFlowRoList?.map((flow) => ({
        ...flow,
        storeName: receiveFlow?.finReceivedRo?.storeName,
      })),
    );
  });

  return (
    <div className="h-screen flex flex-col">
      <CustomNavBar title="收款详情" />
      <div className="pt-[32px] pl-[28px] text-[48px] font-medium text-block opacity-90">
        {finReceivedRo?.buyerName}
      </div>
      <div className="pt-[12px] pl-[28px] text-[28px] mt-1 text-block opacity-60">
        {finReceivedRo?.serialNumber}
      </div>
      <Card>
        <div className="flex justify-between">
          <div className="flex flex-col items-left gap-[8px] w-1/2 text-block">
            <div className="text-[28px] opacity-60">收款金额</div>
            <Price
              size="normal"
              price={finReceivedRo?.totalReceivedAmountYuan ?? 0}
              style={{
                '--nutui-price-integer-medium-size': '24px',
                '--nutui-price-decimal-medium-size': '24px',
                color: 'rgba(0,0,0,0.9)'
              }}
            />
          </div>
          <div className="flex flex-col items-left gap-[8px] w-1/2 text-block">
            <div className="text-[28px] opacity-60">收款账户</div>
            <div className="text-[48px] opacity-90">{finReceivedRo?.receivedAccountName}</div>
          </div>
        </div>
        <div className="flex mt-[20px] py-[12px] text-black text-[28px]">
          <span className="opacity-60 w-[140px]">收款门店：</span>
          <span className="opacity-90">{finReceivedRo?.storeName}</span>
        </div>
        <div className="flex py-[12px] text-black text-[28px]">
          <span className="opacity-60 w-[140px]">收款时间：</span>
          <span className="opacity-90">{finReceivedRo?.businessTime}</span>
        </div>
        <div className="flex py-[12px] text-black text-[28px]">
          <span className="opacity-60 w-[112px]">制单人：</span>
          <span className="opacity-90">{finReceivedRo?.createPerson}</span>
        </div>
        <div className="flex py-[12px] text-[28px]">
          <span className="opacity-60 w-[84px]">备注：</span>
          <span className="opacity-90">{finReceivedRo?.remark}</span>
        </div>
      </Card>
      <Card title="核销订单" className="text-block opacity-90 text-[32px]">
        {finReceivedFlowRoList.map((item) => (
          <div>
            <div className="flex justify-start py-[24px] text-block opacity-90 ">
              <div className="px-[20px] flex flex-col flex-1">
                <div className=" text-[32px] font-medium">{item.orderNo}</div>
                <div className=" py-[12px] text-[28px] opacity-60">{item.storeName}</div>
                <div className="flex grow pt-[8px] justify-between">
                  <div className="text-[28px] flex grow w-1/2 items-center">
                    <span className="opacity-60">订单金额：</span>
                    <Price
                      price={item?.orderAmountYuan ?? 0}
                      size="normal"
                      className="opacity-90"
                      style={{ color: '#000000' }}
                    />
                  </div>
                  <div className="text-[28px] flex grow w-1/2 items-center">
                    <span className="opacity-60">核销金额：</span>
                    <Price
                      price={item?.receivedAmountYuan ?? 0}
                      size="normal"
                      className="opacity-90"
                      style={{ color: '#000000' }}
                    />
                  </div>
                </div>
              </div>
            </div>
            <Divider />
          </div>
        ))}
      </Card>
    </div>
  );
}
