import {post} from '@/utils/request';
import {PageResponseDataType} from "@/types/PageResponseDataType";
import {PageRequestParamsType} from "@/types/PageRequestParamsType";
import {GetCstListRequest} from "@/packages/finance/receive/list/types/get.cst.list.request";
import {CustomerEntity} from "@/packages/finance/receive/list/types/CustomerEntity";
import {ReceivedEntity} from "@/packages/finance/receive/list/types/ReceivedEntity";
import {ReceivedFlowDetailEntity} from "@/packages/finance/receive/list/types/ReceivedFlowDetailEntity";
import {FinReceivableEntity} from "@/packages/finance/receive/list/types/FinReceivableEntity.entity";
import {ReceivedConfirmation} from "@/packages/finance/receive/list/types/ReceivedConfirmation";
import {FinReceivableGroupEntity} from "@/packages/finance/receive/list/types/FinReceivableGroupEntity.entity";

/**
 * 收款分页查询
 *
 * @param params
 * @returns
 */
export const queryReceivedPage = async (params: Partial<ReceivedEntity> & PageRequestParamsType) => {
  return post<PageResponseDataType<ReceivedEntity>>(`/ipmsaccount/queryRecevedPage`, {
    data: params,
  });
};

/**
 * 查询实收流水
 *
 * @param params
 * @returns
 */
export const queryReceivedFlowList = async (params: string) => {
  return post<ReceivedFlowDetailEntity>(`/ipmsaccount/queryReceivedFlowList`, {
    data: {receivedSerialNumber : params},
  });
};

/**
 * 客户模糊查询
 */
export const getCstList = (params: GetCstListRequest) => {
  return post<CustomerEntity[]>(`/ipmscst/CstManageFacade/getCstList`, {
    data: params,
  });
};

/**
 * 应收明细查询
 *
 * @param params
 * @returns
 */
export const queryReceivableList = async (params: Partial<FinReceivableEntity> & PageRequestParamsType) => {
  return post<FinReceivableEntity[]>(`/ipmsaccount/queryReceivableList`, {
    data: params,
  });
};

/**
 * 实收
 *
 * @param params
 * @returns
 */
export const receivedConfirmation = async (params: ReceivedConfirmation) => {
  return post<PageResponseDataType<string>>(`/ipmsaccount/receivedConfirmation`, {
    data: params,
  });
};

/**
 * 应收分页查询
 *
 * @param params
 * @returns
 */
export const queryFinReceivablePage = async (params: Partial<FinReceivableEntity> & PageRequestParamsType) => {
  return post<PageResponseDataType<FinReceivableGroupEntity>>(`/ipmsaccount/queryReceivablePageGroup`, {
    data: params,
  });
};
