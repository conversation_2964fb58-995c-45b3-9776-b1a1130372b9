import {ReceivedConfirmationOrderDetail} from "@/packages/finance/receive/list/types/ReceivedConfirmationOrderDetail";

export interface ReceivedConfirmation {
  receivedAccountId?: string;
  totalReceivedAmount?: string;
  buyerName?: string;
  receivedAccountName?: string;
  sellerId?: number;
  sellerName?: string;
  buyerId?: string;
  ledgerType?: number;
  remark?: string;
  totalReceivedAmountYuan?: string;
  finReceivedOrderDetailCmdList?: ReceivedConfirmationOrderDetail[];
}
