export interface FinReceivableEntity {
  id?: number;
  status?: number;
  buyerId?: string;
  buyerIdList?: string[];
  sellerName?: string;
  sellerId?: string;
  buyerName?: string;
  receivableAmount?: string;
  billDate?: string;
  arrearsDay?: string;
  totalAmount?: string;
  storeId?: string;
  storeName?: string;
  overdueAmount?: string;
  creditTerms?: string;
  orderNo?: string;
  orderAmount?: string;
  receivedAmount?: string;
  remainReceivableAmount?: string;
  receivableFlag?: number;
  currReceivedAmount?: string;
  remainReceivableAmountYuan?: number;
  receivableAmountYuan?: number;
  overdueAmountYuan?: number;
  totalAmountYuan?: number;
  availableAmountYuan?: number;
  orderAmountYuan?: number;
  receivedAmountYuan?: number;
  ledgerType?: number;
  storeIdList: string[] | undefined;
  // 逾期状态，0：逾期，1：未逾期（默认1）
  overdueFlag?: number;
  operatorNo?: string;
}
