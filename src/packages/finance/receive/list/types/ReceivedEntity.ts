
export interface ReceivedEntity {
  storeId?: string;
  storeName?: string;
  receivedAccountId?: string;
  receivedAccountName?: string;
  receivedAccountType?: number;
  totalReceivedAmount?: string;
  buyerName?: string;
  businessTime?: string;
  buyerId?: string;
  ledgerType?: number;
  serialNumber?: string;
  createPerson?: string;
  remark?: string;
  totalReceivedAmountYuan?: number;
  startBusinessTime?: string;
  endBusinessTime?: string;
}
