export enum OpenFailCodeType {
  Ok,
  AlreadyConnect = -1,
  NotInit = 10000,
  NotAvailable = 10001,
  NoDevice = 10002,
  ConnectionFail = 10003,
  NoService = 10004,
  NoCharacteristic = 10005,
  NoConnection = 10006,
  propertyNotSupport = 10007,
  SystemError = 10008,
  SystemNotSupport = 10009,
  OperateTimeOut = 10012,
  InvalidData = 10013,
}

export enum OpenFailCodeTypeName {
  Ok,
  AlreadyConnect = '已连接',
  NotInit = '未初始化蓝牙适配器',
  NotAvailable = '当前蓝牙适配器不可用',
  NoDevice = '没有找到指定设备',
  ConnectionFail = '连接失败',
  NoService = '没有找到指定服务',
  NoCharacteristic = '没有找到指定特征',
  NoConnection = '当前连接已断开',
  propertyNotSupport = '当前特征不支持此操作',
  SystemError = '其余所有系统上报的异常',
  SystemNotSupport = '系统版本低于 4.3 不支持 BLE',
  OperateTimeOut = '连接超时',
  InvalidData = '连接 deviceId 为空或者是格式不正确',
}
