export enum OpenFailStateType {
  UnKnown,
  Resetting,
  UnSupport,
  UnAuthorization,
  UnOpen,
}

export enum OpenFailStateTypeName {
  UnKnown = '未知',
  Resetting = '重置中',
  UnSupport = '不支持',
  UnAuthorization = '未授权',
  UnOpen = '未开启',
}

export enum OpenFailStateTypeTip {
  UnKnown = '未知错误',
  Resetting = '重置中',
  UnSupport = '当前设备不支持蓝牙',
  UnAuthorization = '小程序未授权蓝牙权限',
  UnOpen = '当前设备未开启蓝牙',
}
