import { OpenFailCodeType, OpenFailCodeTypeName } from '@/packages/print/types/OpenFailCodeType';
import { OpenFailStateType, OpenFailStateTypeTip } from '@/packages/print/types/OpenFailStateType';
import Taro from '@tarojs/taro';

export class BELUtils {
  /**
   * 以Promise方式调用 微信api
   * @param {string} name 微信api的名称 ，如 wxAsyncPromise("getSystemInfo",options)
   * @param {object} options 除了success 和 fail 的其他参数
   * @returns
   */
  static wxAsyncPromise = (name, options) => {
    return new Promise((resolve, reject) => {
      wx[name]({
        ...(options || {}),
        success(res) {
          resolve(res);
        },
        fail(res) {
          console.error(name, res);
          reject(res);
        },
      });
    });
  };

  /**
   * 初始化蓝牙
   */
  static openBluetoothAdapter = () => {
    return this.wxAsyncPromise('openBluetoothAdapter', {}).catch((res) => {
      if (typeof res.state !== 'undefined') {
        Taro.showToast({
          title: OpenFailStateTypeTip[OpenFailStateType[res.state]],
          icon: 'none',
        });
      } else if (res.errCode !== OpenFailCodeType.Ok) {
        Taro.showToast({
          title: OpenFailCodeTypeName[OpenFailCodeType[res.errCode]],
          icon: 'none',
        });
      }
    });
  };

  //在多个服务services中递归查找能用的特征值
  static getDeviceCharacteristics = (deviceId, serviceId) => {
    return this.wxAsyncPromise('getBLEDeviceCharacteristics', {
      deviceId,
      serviceId,
    }).then((res: any) => {
      let finish = false;
      for (let i = 0; i < res.characteristics.length; i++) {
        if (res.characteristics[i].properties.write) {
          finish = true;
          return {
            characteristicId: res.characteristics[i].uuid,
          };
        }
      }
      if (!finish) {
        this.getDeviceCharacteristics(deviceId, serviceId);
      }
    });
  };

  /**
   * 生成左边二维码右边文本的CPCL指令
   * @param qrData
   * @param textLines
   * @param maxCharsPerLine
   */
  static generateCPCLCommand = (qrData, textLines, maxCharsPerLine = 14) => {
    // 初始化CPCL命令
    let commands = `
! 0 200 200 400 1
`;
    // 打印左侧二维码
    commands += `
B QR 10 20 M 2 U 8
MA,${qrData}
ENDQR
`;
    // 打印右侧文本，多行处理
    let yPosition = 25; // 初始Y轴位置
    for (let i = 0; i < textLines.length; i++) {
      let text = textLines[i];
      let isFirstLine = true;

      while (text.length > 0) {
        let lineText = text.substring(0, maxCharsPerLine);
        text = text.substring(maxCharsPerLine);

        if (isFirstLine && i === 0) {
          // 第一行文本，字体大一些
          commands += `
TEXT 7 0 200 ${yPosition} ${lineText}
`;
          isFirstLine = false;
        } else {
          // 其他行文本，普通字体
          commands += `
TEXT 5 0 200 ${yPosition} ${lineText}
`;
        }
        yPosition += 40; // 调整Y轴位置，以便打印下一行
      }
    }
    // 结束打印命令
    commands += `
FORM
PRINT
`;
    console.log('commands', commands);
    return commands;
  };
}
