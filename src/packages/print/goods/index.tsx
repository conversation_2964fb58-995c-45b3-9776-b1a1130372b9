import GoodItem from '@/packages/print/goods/components/GoodItem';
import { queryStoreGoods } from '@/packages/sales/order/chooseGoodsPage/services';
import { StoreGoodsEntity } from '@/packages/sales/returns/selectedPage/types/store.goods.entity';
import { Button, SafeArea } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import _ from 'lodash';
import { useEffect, useState } from 'react';

const GoodsList = () => {
  const [goodList, setGoodList] = useState<StoreGoodsEntity[]>([]);

  useEffect(() => {
    const eventChannel = Taro.getCurrentInstance()?.page?.getOpenerEventChannel?.();
    eventChannel?.on?.('goods', function ({ goods }) {
      queryStoreGoods({
        pageNo: 1,
        pageSize: 999,
        itemSnList: goods.map((item) => item.itemSn),
      }).then(async (result) => {
        if (result) {
          setGoodList(
            result.data?.map((item) => {
              const number = goods.find((n) => n.itemSn === item.itemSn)?.number ?? 0;
              return (
                {
                  ...item,
                  printNumber: number,
                } ?? []
              );
            }),
          );
        }
      });
    });
  }, []);

  const handleSubmit = () => {
    const goods = goodList
      .filter((item) => item.printNumber > 0)
      .map((item) => ({ itemSn: item.itemSn, number: item.printNumber }));
    if (goods.length === 0) {
      Taro.showToast({ title: '请至少打印1个', icon: 'none' });
      return;
    }
    Taro.navigateTo({
      url: `/packages/print/preview/index`,
      success(res) {
        res.eventChannel.emit('goods', {
          goods,
        });
      },
    });
  };

  return (
    <div>
      {goodList.map((item) => (
        <GoodItem
          record={item}
          key={item.itemId}
          onChange={(itemId, value) => {
            const newList = _.cloneDeep(goodList);
            // @ts-ignore
            newList.find((n) => n.itemId === itemId).printNumber = value;
            setGoodList(newList);
          }}
          onDelete={(itemId) => {
            const newList = _.cloneDeep(goodList);
            _.remove(newList, (m) => m.itemId === itemId);
            setGoodList(newList);
          }}
        />
      ))}
      <div className="bg-white p-[28px] fixed left-0 bottom-0 right-0">
        <div className="flex justify-between ">
          <Button type="primary" size={'large'} block onClick={handleSubmit}>
            打印
          </Button>
        </div>
        <SafeArea position="bottom" />
      </div>
    </div>
  );
};

export default GoodsList;
