import { StoreGoodsEntity } from '@/packages/sales/order/chooseGoodsPage/types/store.goods.entity';
import { convertStringToNumber } from '@/utils/convertStringToNumber';
import { Trash } from '@nutui/icons-react-taro';
import { Divider, InputNumber } from '@nutui/nutui-react-taro';
import classNames from 'classnames';

export interface GoodItemProps {
  record: StoreGoodsEntity;
  onChange: (itemId: string, value: number) => void;
  onDelete: (itemId: string) => void;
}

const GoodItem = (props: GoodItemProps) => {
  const { record, onChange, onDelete } = props;

  /**
   * 列表中直接更新数量事件
   */
  const handleUpdateNumber = (v: number) => {
    const value = convertStringToNumber({
      value: v?.toString() ?? 1,
      min: 0,
      max: 999999,
      decimal: 1,
    });
    onChange(record.itemId, value);
  };

  return (
    <>
      <div className={classNames('flex bg-white rounded my-[24px] mx-[28px] py-[24px] px-[28px]')}>
        <img
          src={record.images?.[0]}
          className="rounded mr-[20px] bg-[#f5f5f5] w-[88px] h-[88px]"
        />
        <div className="flex-1">
          <div className="flex justify-between">
            <span className="text-[28px]">{record.itemName}</span>
            <Trash className="text-gray-500" onClick={() => onDelete(record.itemId)} />
          </div>
          <div className="text-[24px] text-thirdary my-[12px]">
            {record.itemSn}
            <Divider direction="vertical" />
            {record.brandName}
            <Divider direction="vertical" />
            {record.categoryName}
            <Divider direction="vertical" />
            {record.unitName}
          </div>
          <div className="flex justify-between items-center">
            <span className="text-[24px] text-secondary">打印份数：</span>
            <InputNumber
              max={999999}
              async={true}
              allowEmpty={true}
              value={record.printNumber ?? 1}
              min={0}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onChange={handleUpdateNumber}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default GoodItem;
