import CustomNavBar from '@/components/CustomNavBar';
import { BELUtils } from '@/packages/print/utils/BELUtils';
import usePrintStore from '@/stores/print';
import { Refresh } from '@nutui/icons-react-taro';
import { Cell } from '@nutui/nutui-react-taro';
import Taro, { useDidHide, useDidShow } from '@tarojs/taro';
import { useEffect, useState } from 'react';

export interface DeviceItem {
  name: string;
  deviceId: string;
}

const Print = () => {
  const [deviceList, setDeviceList] = useState<DeviceItem[]>([]);
  const [myDeviceItem, setMyDeviceItem] = useState<DeviceItem>();
  const printStore = usePrintStore();

  useEffect(() => {
    if (myDeviceItem) {
      Taro.setStorage({ key: 'myDeviceItem', data: myDeviceItem });
    }
  }, [myDeviceItem]);

  /**
   * 扫描开始
   */
  const scan = () => {
    scanCancel();
    // 扫描蓝牙设备
    BELUtils.openBluetoothAdapter().then(() => {
      // 开始搜索附近的蓝牙外围设备
      // wx.startBluetoothDevicesDiscovery({
      //   allowDuplicatesKey: false,
      //   fail(res) {
      //     console.error('startBluetoothDevicesDiscovery', res);
      //   },
      // });
    });

    // 监听扫描结果
    // wx.onBluetoothDeviceFound((res) => {
    //   res.devices.forEach((device) => {
    //     setDeviceList((prevDeviceList) => {
    //       if (
    //         prevDeviceList.filter((item) => item.deviceId === device.deviceId).length === 0 &&
    //         device.name
    //       ) {
    //         return [...prevDeviceList, { name: device.name, deviceId: device.deviceId }];
    //       } else {
    //         return prevDeviceList;
    //       }
    //     });
    //   });
    // });
  };

  /**
   * 扫描停止
   */
  const scanCancel = () => {
    // wx.offBluetoothDeviceFound();
    // wx.closeBluetoothAdapter();
  };

  /**
   * 连接蓝牙
   */
  const connect = (deviceItem: DeviceItem) => {
    Taro.showLoading({ title: '正在连接中' });
    if (printStore.isConnect && myDeviceItem?.deviceId) {
      disconnect(myDeviceItem.deviceId);
    }
    // wx.createBLEConnection({
    //   deviceId: deviceItem.deviceId,
    //   success(res) {
    //     console.log('createBLEConnection => success', res);
    //     if (res.errCode !== 0) {
    //       Taro.showToast({ title: res.errMsg, icon: 'none' });
    //     } else {
    //       Taro.showToast({ title: '连接成功', icon: 'none' });
    //       setMyDeviceItem(deviceItem);
    //     }
    //   },
    //   fail(res) {
    //     Taro.showToast({ title: res.errMsg, icon: 'none' });
    //   },
    // });
  };

  /**
   * 断开蓝牙
   */
  const disconnect = (deviceId: string, showToast?: boolean) => {
    if (showToast) {
      Taro.showLoading({ title: '正在断开中' });
    }
    // wx.closeBLEConnection({
    //   deviceId,
    //   success(res) {
    //     console.log('closeBLEConnection => success', res);
    //     if (res.errCode !== 0) {
    //       Taro.showToast({ title: res.errMsg, icon: 'none' });
    //     } else {
    //       if (showToast) {
    //         Taro.showToast({ title: '断开成功', icon: 'none' });
    //       }
    //     }
    //   },
    //   fail(res) {
    //     Taro.showToast({ title: res.errMsg, icon: 'none' });
    //   },
    // });
  };

  useDidShow(() => {
    setMyDeviceItem(Taro.getStorageSync('myDeviceItem'));
    scan();
  });

  useDidHide(() => {
    scanCancel();
  });

  console.log('deviceList', deviceList);

  return (
    <>
      <CustomNavBar title="蓝牙打印机" />

      <div className="mx-[28px]">
        <Cell.Group title="我的设备">
          {myDeviceItem ? (
            <Cell
              title={myDeviceItem.name}
              extra={
                printStore.isConnect ? (
                  <span className="text-green-600">已连接</span>
                ) : (
                  <span className="text-red-600">未连接</span>
                )
              }
              onClick={() =>
                printStore.isConnect
                  ? disconnect(myDeviceItem.deviceId, true)
                  : connect(myDeviceItem)
              }
            />
          ) : (
            <Cell title="请在下方列表选择可用蓝牙打印机" />
          )}
        </Cell.Group>
        <Cell.Group
          title={
            <div className="flex items-center">
              <span>附近的设备</span>
              <Refresh className="nut-icon-am-rotate nut-icon-am-infinite ml-[10px]" size={14} />
            </div>
          }
        >
          {deviceList.map((item) => (
            <Cell
              title={item.name}
              onClick={() => {
                connect(item);
              }}
            />
          ))}
        </Cell.Group>
      </div>
    </>
  );
};

export default Print;
