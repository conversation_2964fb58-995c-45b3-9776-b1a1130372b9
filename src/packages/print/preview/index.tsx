import BluePrintCell from '@/components/BluePrintCell';
import Card from '@/components/Card';
import { DeviceItem } from '@/packages/print/setting';
import { BELUtils } from '@/packages/print/utils/BELUtils';
import { queryStoreGoods } from '@/packages/sales/order/chooseGoodsPage/services';
import { StoreGoodsEntity } from '@/packages/sales/order/chooseGoodsPage/types/store.goods.entity';
import usePrintStore from '@/stores/print';
import { Button, Cell, SafeArea } from '@nutui/nutui-react-taro';
import Taro, { useDidShow } from '@tarojs/taro';
import iconv from 'iconv-lite';
import { useEffect, useRef, useState } from 'react';
import qrcode from 'wx-mini-qrcode/src/index';

const encodeText = (text) => {
  return iconv.encode(text, 'gb18030');
};

const GoodPrint = () => {
  const [goodList, setGoodList] = useState<StoreGoodsEntity[]>([]);
  const [myDeviceItem, setMyDeviceItem] = useState<DeviceItem>();
  const printStore = usePrintStore();
  const serviceIdRef = useRef<string>();
  const characteristicIdRef = useRef<string>();
  const [printing, setPrinting] = useState(false);

  useDidShow(() => {
    setMyDeviceItem(Taro.getStorageSync('myDeviceItem'));
  });

  useEffect(() => {
    const eventChannel = Taro.getCurrentInstance()?.page?.getOpenerEventChannel?.();
    eventChannel?.on?.('goods', function ({ goods }) {
      queryStoreGoods({
        pageNo: 1,
        pageSize: 999,
        itemSnList: goods.map((item) => item.itemSn),
      }).then(async (result) => {
        if (result) {
          setGoodList(
            result.data?.map((item) => {
              const number = goods.find((n) => n.itemSn === item.itemSn)?.number ?? 0;
              const qrCode = qrcode.outputQRCodeBase64(item.itemId, {
                size: 400,
                color: '#000000',
                padding: 0,
                background: '#ffffff',
              });
              return (
                {
                  ...item,
                  printNumber: number,
                  qrCode,
                } ?? []
              );
            }),
          );
        }
      });
    });
  }, []);

  const connect = () => {
    // wx.createBLEConnection({
    //   deviceId: myDeviceItem!.deviceId,
    //   success(res) {
    //     console.log('createBLEConnection => success', res);
    //   },
    //   fail(res) {
    //     console.error('createBLEConnection => fail', res);
    //   },
    // });
  };

  useEffect(() => {
    if (myDeviceItem) {
      if (myDeviceItem.deviceId && !printStore.isConnect) {
        BELUtils.openBluetoothAdapter().then(() => {
          connect();
        });
      }
    }
    // return () => {
    //   if (myDeviceItem) {
    //     if (printStore.isConnect) {
    //       BELUtils.wxAsyncPromise('closeBLEConnection', { deviceId: myDeviceItem.deviceId });
    //     }
    //     BELUtils.wxAsyncPromise('closeBluetoothAdapter', {});
    //   }
    // };
  }, [myDeviceItem]);

  const getCharacteristicId = () => {
    if (!printStore.isConnect || !myDeviceItem) {
      return;
    }
    BELUtils.wxAsyncPromise('getBLEDeviceServices', {
      deviceId: myDeviceItem?.deviceId,
    })
      .then((res: any) => {
        console.log('getBLEDeviceServices', res);
        if (res?.services?.length) {
          for (let i = 0; i <= res.services.length; i++) {
            BELUtils.getDeviceCharacteristics(myDeviceItem?.deviceId, res.services[i].uuid).then(
              (result) => {
                if (result?.characteristicId) {
                  serviceIdRef.current = res.services[i].uuid;
                  characteristicIdRef.current = result.characteristicId;
                }
              },
            );
            if (characteristicIdRef.current) {
              break;
            }
          }
        }
      })
      .catch((res) => {
        if (res.errCode === 10006) {
          printStore.disconnect();
          connect();
        }
      });
  };

  useEffect(() => {
    getCharacteristicId();
  }, [printStore.isConnect, myDeviceItem]);

  /**
   * 打印方法
   */
  const printFn = (buffer) => {
    const fn = async (value) => {
      setPrinting(true);
      BELUtils.wxAsyncPromise('writeBLECharacteristicValue', {
        deviceId: myDeviceItem?.deviceId,
        serviceId: serviceIdRef.current,
        characteristicId: characteristicIdRef.current,
        value,
      })
        .then((res: any) => {
          console.log('writeBLECharacteristicValue', res);
          if (res.errCode === 0) {
            setTimeout(() => {
              setPrinting(false);
            }, 2000);
          } else {
            Taro.showToast({ title: res.errMsg, icon: 'none' });
            setPrinting(false);
          }
        })
        .catch((res) => {
          Taro.showToast({ title: res.errMsg, icon: 'none' });
          setPrinting(false);
        });
    };
    const maxChunk = 20;
    const delay = 20;
    for (let i = 0, j = 0, length = buffer.byteLength; i < length; i += maxChunk, j++) {
      let subPackage = buffer.slice(i, i + maxChunk <= length ? i + maxChunk : length);
      setTimeout(fn, j * delay, subPackage);
    }
  };

  /**
   * 打印事件
   */
  const handlePrint = async () => {
    if (myDeviceItem?.deviceId && printStore.isConnect) {
      if (!characteristicIdRef.current) {
        Taro.showToast({ title: '蓝牙设备异常', icon: 'none' });
        return;
      }
      console.log(
        'writeBLECharacteristicValueProps',
        myDeviceItem?.deviceId,
        serviceIdRef.current,
        characteristicIdRef.current,
      );

      // Initialize an array to accumulate all the commands
      let allCommands: any = [];

      for (const item of goodList) {
        let qrData = item.itemId;
        let textData = [
          item.itemName,
          `商品编码: ${item.itemSn ?? ''}`,
          `品牌: ${item.brandName ?? ''}`,
          `车型: ${item.adaptSeries ?? ''}`,
        ];
        let cpclCommand = BELUtils.generateCPCLCommand(qrData, textData);

        for (let i = 0; i < item.printNumber ?? 1; i++) {
          // Accumulate the command in the array
          allCommands.push(encodeText(cpclCommand).buffer);
        }
      }
      // Merge all buffers into one
      let mergedCommandBuffer = mergeBuffers(allCommands);
      // Call printFn only once with the merged command
      printFn(mergedCommandBuffer);
    } else {
      Taro.showToast({ title: '请先选择蓝牙打印机', icon: 'none' });
    }
  };

  // Utility function to merge multiple ArrayBuffer objects into one
  const mergeBuffers = (buffers) => {
    let totalLength = buffers.reduce((acc, buffer) => acc + buffer.byteLength, 0);
    let mergedArray = new Uint8Array(totalLength);
    let offset = 0;

    for (let buffer of buffers) {
      mergedArray.set(new Uint8Array(buffer), offset);
      offset += buffer.byteLength;
    }

    return mergedArray.buffer;
  };

  console.log('goodList', goodList);

  return (
    <div className="pb-[120px]">
      {goodList.map((item) => (
        <div>
          <Card>
            <div className="flex">
              <div>
                <img
                  style={{ width: '200px', height: '200px', display: 'block' }}
                  src={item.qrCode}
                />
              </div>
              <div className="flex-1 ml-[20px] text-[28px]">
                <div className="text-[34px] mb-[6px]">{item.itemName}</div>
                <div>商品编码: {item.itemSn}</div>
                <div>品牌: {item.brandName}</div>
                <div>车型: {item.adaptSeries}</div>
              </div>
            </div>
          </Card>
          <div className="flex justify-center text-[28px] text-gray-400">
            {/** @ts-ignore **/}
            打印份数:<span className="text-red-600 mx-[5px]">{item.printNumber}</span>份
          </div>
        </div>
      ))}
      <SafeArea position={'bottom'} />
      <div className="fixed left-0 bottom-0 right-0 bg-white px-[28px] pb-[24px]">
        <Cell.Group>
          <BluePrintCell />
        </Cell.Group>
        <Button
          type="primary"
          block={true}
          size="large"
          disabled={!myDeviceItem?.deviceId || !printStore.isConnect}
          onClick={handlePrint}
          loading={printing}
        >
          确认打印
        </Button>
        <SafeArea position={'bottom'} />
      </div>
    </div>
  );
};

export default GoodPrint;
