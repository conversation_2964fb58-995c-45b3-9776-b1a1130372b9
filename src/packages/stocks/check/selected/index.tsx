import '@/assets/lightPage.scss';
import notPic from '@/assets/not_pic.png';
import ChooseCategoryCard from '@/components/ChooseCategoryCard';
import CodeWithDivider from '@/components/CodeWithDivider';
import CustomMultipleChoose from '@/components/CustomMultipleChoose';
import GoodsSearchBar from '@/components/GoodsSearchBar';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import MenuWrap from '@/components/MenuWrap';
import FunPagination from '@/components/Pagination/FunPagination';
import { IconFont } from '@nutui/icons-react-taro';
import { Badge, Button, Image, Menu, SafeArea } from '@nutui/nutui-react-taro';
import Taro, { navigateTo, useDidShow, useLoad } from '@tarojs/taro';
import { useDebounceFn } from 'ahooks';
import _, { isEmpty } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { queryGoodsPropertyPage, queryLocationByWhId } from '../../inventory/services';
import { CheckDetailPostEntity } from '../detail/types/check.detail.post.entity';
import { StockCheckTypeEnum } from '../list/types/StockCheckTypeEnum';
import { StockCheckDetailCmdList } from '../operation/types/add.check.request';
import {
  checkCreateOrUpdatePost,
  queryCheckDetailAndItemByPagePost,
  queryCountPost,
} from '../services';
import useCheckStore from '../store';
import add from './imgs/add.svg';
import packageIcon from './imgs/package.svg';
import { CheckGoodsEntity } from './types/check.goods.entity';
import { QueryCheckGoodsItemRquest } from './types/check.goods.item.request';
import StableStepper from "@/components/StableStepper";
import CustomNavBar from "@/components/CustomNavBar";

export interface ChooseItem extends StockCheckDetailCmdList {
  number: number;
}

export default function Index() {

  const [checkId, setCheckId] = useState<any>();
  const [params, setParams] = useState<any>({});
  const [locationOptions, setLocationOptions] = useState<any>([]);
  const [locationIdList, setLocationIdList] = useState<string[] | undefined>(undefined);
  const [filterValue, setFilterValue] = useState<(string | number)[]>([]);
  const [brandIdList, setBrandIdList] = useState<any | undefined>(undefined);
  const [brandItems, setBrandItems] = useState<any>([]);
  const [localNumber, setLocalNumber] = useState<number>(0);
  const [updateRecordData, setUpdateRecordData] = useState<CheckGoodsEntity[]>([]);
  const checkStore = useCheckStore();
  const [onLoadFlag, setOnLoadFlag] = useState<boolean>(false);

  useLoad(async () => {
    if (!isEmpty(checkStore.checkId)) {
      setCheckId(checkStore.checkId);
      setParams({
        warehouseId: checkStore.warehouseId,
        checkId: checkStore.checkId,
        mode: checkStore.mode,
      });
    } else {
      setParams({ warehouseId: checkStore.warehouseId, mode: checkStore.mode });
    }
    const data = await queryLocationByWhId({ warehouseIdList: [checkStore.warehouseId] });
    if (data) {
      setLocationOptions(data.warehouseLocationRoList?.map((s) => ({ text: s.code, value: s.id })));
    }
    const { data: brandData } = await queryGoodsPropertyPage(
      { pageNo: 1, pageSize: 1000, brandStatus: '1' },
      'brand',
    );
    if (brandData) {
      setBrandItems([
        {
          keyStr: 'brandIdList',
          multiple: true,
          label: '',
          item: brandData.map((s) => ({ text: s.brandName, value: s.brandId })),
        },
      ]);
    }
  });

  useDidShow(() => {
    if (onLoadFlag) {
      setParams((prevData) => ({ ...prevData, time: _.now() }));
    }
  });

  const fetchData = (param?: QueryCheckGoodsItemRquest) => {
    if (isEmpty(param?.warehouseId)) {
      return [];
    }
    getTotalNumber();
    return queryCheckDetailAndItemByPagePost({ pageSize: 10, ...param, showItemExtra: true }).then(
      (result) => result?.data ?? [],
    );
  };

  //搜索条件
  const setInputValue = (param) => {
    setParams((prevData) => ({
      ...prevData,
      ...param,
    }));
  };

  const getDiffAmountDom = (diffAmount) => {
    if (diffAmount > 0) {
      return <span className="text-[#F83431]">+{diffAmount}</span>;
    } else if (diffAmount! < 0) {
      return <span className="text-[#33CC47]">{diffAmount}</span>;
    } else {
      return <span>{diffAmount}</span>;
    }
  };

  useEffect(() => {
    if (locationIdList !== undefined) {
      setParams((prevData) => ({
        ...prevData,
        locationIdList,
        brandIdList,
      }));
    }
  }, [locationIdList, brandIdList]);

  const handleAddItem = async (itemId) => {
    const data = await checkCreateOrUpdatePost({
      id: isEmpty(checkId) ? undefined : checkId,
      ...params,
      type: StockCheckTypeEnum.PART,
      stockCheckDetailCmdList: [
        {
          itemId,
          checkId: isEmpty(checkId) ? undefined : checkId,
        },
      ],
    });
    if (data && data?.code === 0) {
      if (isEmpty(checkId)) {
        setCheckId(data?.data?.id!);
        checkStore.setCheckId(data?.data?.id!);
        setParams((prevData) => ({ ...prevData, checkId: data?.data?.id! }));
      } else {
        handleUpdateOrAdd({ ...data?.data!, checkId: data?.data?.id });
        getTotalNumber();
      }
    }
  };

  /**
   * 提交数据
   */
  const handleSubmit = () => {
    Taro.navigateBack();
  };

  const categoryMenuRef = useRef(null);
  const itemOneRef = useRef(null);

  const { run } = useDebounceFn(
    (items: CheckGoodsEntity, v: number | string) => handleUpdateNumber(items, v),
    {
      wait: 500,
    },
  );

  const renderAddInput = (record: CheckGoodsEntity) => {
    if (isEmpty(record.checkId) && isEmpty(record.id)) {
      return (
        <span
          className="border-[1px] border-solid border-[#00000026] p-[13px] flex items-center"
          onClick={() => handleAddItem(record.itemId)}
        >
          <IconFont name={add} style={{ width: '15px', height: '15px' }} />
        </span>
      );
    } else if (
      (!isEmpty(record.checkId) || !isEmpty(record.id)) &&
      record.checkAmount != undefined
    ) {
      return (
        <StableStepper value={record.checkAmount} max={999999} min={0} onChange={(e) => run(record, e)} onClick={(e) => {
          e.stopPropagation();
        }}/>
      );
    } else {
      return (
        <StableStepper value={''} max={999999} min={0} onChange={(e) => run(record, e)} onClick={(e) => {
          e.stopPropagation();
        }}/>
      );
    }
  };
  //修改数量
  const handleUpdateNumber = async (items, v: number | string) => {
    if (v != undefined && v != 'NaN' && v !== '') {
      const data = await checkCreateOrUpdatePost({
        ...params,
        id: checkId,
        stockCheckDetailCmdList: [{ ...items, checkAmount: v }],
      });
      if (data && data?.code === 0) {
        handleUpdateOrAdd(data?.data!);
        getTotalNumber();
      }
    }
  };

  const handleUpdateOrAdd = (newRecord: CheckGoodsEntity) => {
    setUpdateRecordData((prevData) => {
      const index = prevData?.findIndex((item) => item?.itemId === newRecord?.itemId);
      if (index != undefined && index !== -1) {
        // 如果找到匹配项，则更新它
        const updatedData = [...prevData!];
        updatedData[index!] = newRecord;
        return updatedData;
      } else {
        // 否则新增一项
        if (prevData) {
          return [...prevData, newRecord];
        } else {
          return [newRecord];
        }
      }
    });
  };

  /**
   * 计算总数
   */
  const getTotalNumber = () => {
    if (!isEmpty(checkId)) {
      queryCountPost({ checkId }).then((s) => {
        setLocalNumber(s);
      });
    }
  };

  const renderItem = (record: CheckGoodsEntity) => {
    const firstMatchingRecord = updateRecordData?.find((s) => s.itemId === record.itemId);
    if (!isEmpty(firstMatchingRecord)) {
      record = { ...record, ...firstMatchingRecord };
    }
    return (
      <div className="px-[28px] py-[24px] bg-white mb-[24px] rounded-xl">
        <div className="flex justify-start">
          <div>
            <Image
              src={record.images?.[0] ?? notPic}
              className="rounded bg-[#f5f5f5]"
              width={'44px'}
              height={'44px'}
            />
          </div>
          <div className="pl-[20px] flex-col flex-1">
            <div className="text-[32px] font-medium text-[#111111] flex justify-between">
              <span>{record.itemName}</span>
              {/* <span>
              <IconFont name={icon_delete} style={{ height: '32px' }} />
            </span> */}
            </div>
            <div className="py-[12px] text-[24px] text-[#666666] flex flex-1">
              <ItemsWithDivider
                items={[record?.itemSn, record?.brandName, record?.categoryName, record?.unitName]}
              />
            </div>
            <div className="flex text-[24px] text-[#666666] pt-[8px] justify-between">
              <div>库存 {record.inventoryNum}</div>
              <div className="flex">
                <span className="pr-[8px]">盘点差异</span>
                {getDiffAmountDom(record?.diffAmount)}
              </div>
              <div>
                <CodeWithDivider key={record.id} title="库位" items={record.code?.split(',')} />
              </div>
            </div>
            <div className="flex text-[24px] text-[#666666] pt-[24px] items-center justify-between">
              <div>盘点库存</div>
              <div>{renderAddInput(record)}</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-screen flex flex-col">
      <CustomNavBar title="添加商品" showBack={true} />
      <div className="mt-[16px] mb-[24px]">
        <GoodsSearchBar inputValue={setInputValue} keyStr={'keyword'} isShowScan={false} />
      </div>
      <div className="custom-menu">
        <MenuWrap
          checkbox={{
            options: [
              {
                label: '有库存',
                value: 1,
              },
            ],
            value: filterValue,
            onChange: (e) => {
              setFilterValue(e);
              setParams((prevData) => ({
                ...prevData,
                onlyHaveInv: e?.[0] ?? '0',
              }));
            },
          }}
          menu={
            <Menu>
              <Menu.Item
                title={'库位'}
                defaultValue={''}
                options={[{ text: '全部', value: '' }, ...locationOptions]}
                onChange={(e) => {
                  if (e.value === '') {
                    setLocationIdList([]);
                  } else {
                    setLocationIdList([e.value]);
                  }
                }}
              ></Menu.Item>
              <Menu.Item title={'品牌'} ref={itemOneRef}>
                <CustomMultipleChoose
                  key={'barnItems'}
                  onClose={() => {
                    (itemOneRef.current as any)?.toggle(false);
                  }}
                  onConfirm={(e) => {
                    setBrandIdList(e);
                  }}
                  items={brandItems}
                />
              </Menu.Item>
              <Menu.Item title={'分类'} ref={categoryMenuRef}>
                <ChooseCategoryCard
                  values={params?.categoryIdList ?? []}
                  onChange={(v) => {
                    setParams({ ...params, categoryIdList: v });
                    (categoryMenuRef.current as any)?.toggle(false);
                  }}
                />
              </Menu.Item>
            </Menu>
          }
        />
      </div>
      <div className="flex-1 min-h-0 mx-[28px] pt-[16px] pb-[8px]">
        <FunPagination fetchData={fetchData} params={params} renderItem={renderItem} />
      </div>

      <div className="bg-white px-[28px] pt-[0px]">
        <div className="flex justify-between py-[24px] items-center">
          <div
            className="flex"
            onClick={() => {
              if (localNumber > 0) {
                setOnLoadFlag(true);
                navigateTo({
                  url: '/packages/stocks/check/checked/index?checkId=' + checkId,
                });
              }
            }}
          >
            <Badge value={localNumber}>
              <IconFont name={packageIcon} style={{ height: '22px', width: '30px' }} />
            </Badge>
          </div>
          <div>
            <Button type="primary" onClick={handleSubmit}>
              <span className="text-white">选好了</span>
            </Button>
          </div>
        </div>
        <SafeArea position="bottom" />
      </div>
    </div>
  );
}
