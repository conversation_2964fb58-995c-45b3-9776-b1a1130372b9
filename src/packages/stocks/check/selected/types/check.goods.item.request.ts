export interface QueryCheckGoodsItemRquest {
  /**
   * 品牌Id
   */
  brandIdList?: string[];
  /**
   * 类目Id
   */
  categoryIdList?: string[];
  /**
   * 盘点主单ID
   */
  checkId?: string;
  /**
   * 商品编码商品名称OE品牌件号模糊搜索
   */
  keyword?: string;
  /**
   * 库位id
   */
  locationIdList?: string[];
  /**
   * None
   */
  memberId?: string;
  /**
   * 盘点方式1:明盘2:盲盘
   */
  mode?: number;
  /**
   * 仅看有库存-值为1
   */
  onlyHaveInv?: number;
  /**
   * 是否商品其他信息（包含图片等）
   */
  showItemExtra?: boolean;
  startRow?: number;
  /**
   * 仓库id
   */
  warehouseId?: string;
}
