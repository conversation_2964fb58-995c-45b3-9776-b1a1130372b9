import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { post } from '@/utils/request';
import { PageResponseDataType } from '../../../../types/PageResponseDataType';
import { ResponseDataType } from '../../../../types/ResponseDataType';
import { CheckApproveEntity } from './detail/types/check.approve.post.entity';
import { CheckDetailPostEntity } from './detail/types/check.detail.post.entity';
import {
  CheckDetailRequest,
  CheckPostCancelRequest,
  StockCheckSimpleRequest,
} from './list/types/check.detail.request';
import { CheckPostEntity } from './list/types/check.post.entity';
import { QueryPostListRequest } from './list/types/query.post.list.request';
import { AddCheckRequest } from './operation/types/add.check.request';
import { DeleteCheckRequest } from './operation/types/delete.check.request';
import { SubmitCheckRequest } from './operation/types/submit.check.request';
import { CheckGoodsEntity } from './selected/types/check.goods.entity';
import { CheckGoodsItemEntity } from './selected/types/check.goods.item.entity';
import { QueryCheckGoodsItemRquest } from './selected/types/check.goods.item.request';
import { GoodsCheckRquest } from './selected/types/goods.check.request';

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryCheckPagePost = async (params: Partial<QueryPostListRequest>) => {
  return post<PageResponseDataType<CheckPostEntity>>(`/ipmswarehouse/stockcheck/queryByPage`, {
    data: params,
  });
};

/**
 * 详情查询
 * @param params
 * @returns
 */
export const queryCheckByIdOrNo = async (params: Partial<StockCheckSimpleRequest>) => {
  return post<CheckPostEntity>(`/ipmswarehouse/stockcheck/queryByIdOrNo`, {
    data: params,
  });
};

/**
 * 详情查询
 * @param params
 * @returns
 */
export const queryCheckPostDetail = async (params: Partial<CheckDetailRequest>) => {
  return post<PageResponseDataType<CheckDetailPostEntity>>(
    `/ipmswarehouse/stockcheck/queryDetailByPage`,
    {
      data: params,
    },
  );
};

/**
 * 添加商品详情查询
 * @param params
 * @returns
 */
export const queryPostItemDetail = async (params: Partial<CheckDetailRequest>) => {
  return post<PageResponseDataType<CheckDetailPostEntity>>(
    `/ipmswarehouse/stockcheck/queryItemByPage`,
    {
      data: params,
    },
  );
};

/**
 * 取消
 * @param params
 * @returns
 */
export const cancelCheckPost = async (params: Partial<CheckPostCancelRequest>) => {
  return post<boolean>(`/ipmswarehouse/stockcheck/cancel`, {
    data: params,
  });
};

/**
 *
 * @param params
 * @returns
 */
export const queryCheckGoodsPost = async (
  params: Partial<GoodsCheckRquest> & PageRequestParamsType,
) => {
  return post<PageResponseDataType<CheckGoodsEntity>>(`/ipmswarehouse/stockcheck/queryItemByPage`, {
    data: params,
  });
};
/**
 * 商品分页查询-盘点添加商品时,取已盘点明细+商品明细并集
 * @param params
 * @returns
 */
export const queryCheckDetailAndItemByPagePost = async (
  params: Partial<QueryCheckGoodsItemRquest> & PageRequestParamsType,
) => {
  return post<PageResponseDataType<CheckGoodsItemEntity>>(
    `/ipmswarehouse/stockcheck/queryCheckDetailAndItemByPage`,
    {
      data: params,
    },
  );
};

/**
 * 创建or更新盘点点
 * @param params
 * @returns
 */
export const checkCreateOrUpdatePost = async (params: Partial<AddCheckRequest>) => {
  return post<ResponseDataType<CheckPostEntity>>(`/ipmswarehouse/stockcheck/createOrUpdate`, {
    origin: true,
    data: params,
  });
};

/**
 * 查询明细个数
 * @param params
 * @returns
 */
export const queryCountPost = async (params: { checkId: string }) => {
  return post<number>(`/ipmswarehouse/stockcheck/count`, {
    data: params,
  });
};

/**
 * 删除明细
 * @param params
 * @returns
 */
export const modifyCheckDeletePost = async (params: Partial<DeleteCheckRequest>) => {
  return post<boolean>(`/ipmswarehouse/stockcheck/deleteDetail`, {
    data: params,
  });
};

/**
 * 通过
 * @param params
 * @returns
 */
export const approveCheckPost = async (params: Partial<CheckApproveEntity>) => {
  return post<boolean>(`/ipmswarehouse/stockcheck/approval`, {
    data: params,
  });
};
/**
 * 拒绝
 * @param params
 * @returns
 */
export const rejectCheckPost = async (params: Partial<CheckApproveEntity>) => {
  return post<boolean>(`/ipmswarehouse/stockcheck/reject`, {
    data: params,
  });
};

/**
 * 盘点确认
 * @param params
 * @returns
 */
export const confirmCheckPost = async (params: Partial<SubmitCheckRequest>) => {
  return post<boolean>(`/ipmswarehouse/stockcheck/confirm`, {
    data: params,
  });
};
