import icon_delete from '@/assets/icons/icon_remove.png';
import '@/assets/lightPage.scss';
import notPic from '@/assets/not_pic.png';
import scan_add from '@/assets/scan_add.svg';
import CodeWithDivider from '@/components/CodeWithDivider';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import FunPagination from '@/components/Pagination/FunPagination';
import { IconFont } from '@nutui/icons-react-taro';
import { Button, Dialog, Drag, Image, SafeArea } from '@nutui/nutui-react-taro';
import Taro, { useLoad } from '@tarojs/taro';
import { useDebounceFn } from 'ahooks';
import _, { isEmpty } from 'lodash';
import { useState } from 'react';
import { StockCheckTypeEnum } from '../list/types/StockCheckTypeEnum';
import { CheckDetailRequest } from '../list/types/check.detail.request';
import { CheckGoodsEntity } from '../selected/types/check.goods.entity';
import {
  checkCreateOrUpdatePost,
  modifyCheckDeletePost,
  queryCheckPostDetail,
  queryCountPost,
} from '../services';
import useCheckStore from '../store';
import { DeleteCheckRequest } from './types/delete.check.request';
import StableStepper from "@/components/StableStepper";
import {scanCode} from "@/utils/scanCode";
import CustomNavBar from "@/components/CustomNavBar";

export default function Index() {
  const [paramData, setParamData] = useState<{ checkId: string }>({ checkId: '' });
  const checkStore = useCheckStore();
  useLoad(() => {
    setParamData({ checkId: checkStore.checkId });
  });
  const [updateRecordData, setUpdateRecordData] = useState<CheckGoodsEntity[]>([]);
  const [localNumber, setLocalNumber] = useState<number>();
  const getDiffAmountDom = (diffAmount) => {
    if (diffAmount > 0) {
      return <span className="text-[#F83431]">+{diffAmount}</span>;
    } else if (diffAmount! < 0) {
      return <span className="text-[#33CC47]">{diffAmount}</span>;
    } else {
      return <span>{diffAmount}</span>;
    }
  };

  const { run } = useDebounceFn(
    (items: CheckGoodsEntity, v: number | string) => handleUpdateNumber(items, v),
    {
      wait: 500,
    },
  );

  /**
   * 计算总数
   */
  const getTotalNumber = () => {
    if (!isEmpty(paramData?.checkId)) {
      queryCountPost({ checkId: paramData?.checkId }).then((s) => {
        setLocalNumber(s);
      });
    }
  };

  //修改数量
  const handleUpdateNumber = async (items, v: number | string) => {
    if (v != undefined && v != 'NaN' && v !== '') {
      const data = await checkCreateOrUpdatePost({
        id: paramData?.checkId,
        stockCheckDetailCmdList: [{ ...items, checkAmount: v }],
      });
      if (data && data?.code === 0) {
        handleUpdateOrAdd(data?.data!);
        getTotalNumber();
      }
    }
  };

  const handleUpdateOrAdd = (newRecord: CheckGoodsEntity) => {
    setUpdateRecordData((prevData) => {
      const index = prevData?.findIndex((item) => item?.itemId === newRecord?.itemId);
      if (index != undefined && index !== -1) {
        // 如果找到匹配项，则更新它
        const updatedData = [...prevData!];
        updatedData[index!] = newRecord;
        return updatedData;
      } else {
        // 否则新增一项
        if (prevData) {
          return [...prevData, newRecord];
        } else {
          return [newRecord];
        }
      }
    });
  };

  /**
   * 提交数据
   */
  const handleSubmit = () => {
    Taro.navigateBack();
  };

  const renderAddInput = (record: CheckGoodsEntity) => {
    if ((!isEmpty(record.checkId) || !isEmpty(record.id)) && record.checkAmount != undefined) {
      return (
        <StableStepper
          max={999999}
          min={0}
          key={record.id}
          value={record.checkAmount}
          onChange={(e: number) => run(record, e)}
          onClick={(e) => {
            e.stopPropagation();
          }}
        />
      );
    } else {
      return (
        <StableStepper
          max={999999}
          min={0}
          value={''}
          key={record.id}
          onChange={(e: number) => run(record, e)}
          onClick={(e) => {
            e.stopPropagation();
          }}
        />
      );
    }
  };

  const fetchData = (param?: CheckDetailRequest) => {
    if (isEmpty(param?.checkId)) {
      return [];
    }
    getTotalNumber();
    return queryCheckPostDetail({
      pageSize: 10,
      ...param,
      showItemExtra: true,
      isEdit: true,
      orderParam: 'updateTime',
      orderType: 1,
    }).then((result) => result?.data ?? []);
  };

  const handleDeleteItem = async (param: DeleteCheckRequest) => {
    const data = await modifyCheckDeletePost(param);
    if (data) {
      setParamData((prevData) => ({ ...prevData, time: _.now() }));
    }
    Dialog.close('confirm');
  };

  /**
   * 扫码新增
   * @param itemId
   */
  const handleAddItem = async (itemId) => {
    const data = await checkCreateOrUpdatePost({
      id: paramData?.checkId,
      type: StockCheckTypeEnum.PART,
      stockCheckDetailCmdList: [
        {
          itemId,
          increment: true,
          checkId: paramData?.checkId,
        },
      ],
    });
    if (data && data?.code === 0) {
      setUpdateRecordData([]); //清空值重新刷新列表
      setParamData((prevData) => ({ ...prevData, time: _.now() }));
    }
  };

  const renderItem = (record: CheckGoodsEntity) => {
    const idList = [record?.id!]
    const firstMatchingRecord = updateRecordData?.find((s) => s.itemId === record.itemId);
    if (!isEmpty(firstMatchingRecord)) {
      record = { ...record, ...firstMatchingRecord };
    }
    return (
      <div className="px-[28px] py-[24px] bg-white mb-[24px] rounded-xl">
        <div className="flex justify-start">
          <div>
            <Image
              src={record.images?.[0] ?? notPic}
              className="rounded bg-[#f5f5f5]"
              width={'44px'}
              height={'44px'}
            />
          </div>
          <div className="pl-[20px] flex-col flex-1">
            <div className="text-[32px] font-medium text-[#111111] flex justify-between">
              <span>{record.itemName}</span>
              <span
                onClick={() => {
                  Dialog.open('confirm', {
                    title: '确认删除',
                    content: '是否确认删除？',
                    onConfirm: () => {
                      handleDeleteItem({
                        idList,
                        checkId: record?.checkId,
                      });
                    },
                    onCancel: () => {
                      Dialog.close('confirm');
                    },
                  });
                }}
              >
                <IconFont name={icon_delete} width={'12px'} height={'14px'} />
              </span>
            </div>
            <div className="py-[12px] text-[24px] text-[#666666] flex flex-1">
              <ItemsWithDivider
                items={[record?.itemSn, record?.brandName, record?.categoryName, record?.unitName]}
              />
            </div>
            <div className="flex text-[24px] text-[#666666] pt-[8px] justify-between">
              <div>库存 {record.inventoryNum}</div>
              <div className="flex">
                <span className="pr-[8px]">盘点差异</span>
                {getDiffAmountDom(record?.diffAmount)}
              </div>
              <div>
                <CodeWithDivider key={record.id} title="库位" items={record.code?.split(',')} />
              </div>
            </div>
            <div className="flex text-[24px] text-[#666666] pt-[24px] items-center justify-between">
              <div>盘点库存</div>
              <div>{renderAddInput(record)}</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-screen flex flex-col">
      <CustomNavBar title="已选择商品列表" showBack={true} />
      <div className="flex-1 min-h-0 overflow-y-scroll box-border mb-[24px] px-[28px] rounded-b-xl">
        <FunPagination fetchData={fetchData} params={paramData} renderItem={renderItem} />
      </div>
      <Drag style={{ bottom: '150px', right: '14px' }}>
        <div
          className="shadow shadow-transparent"
          onClick={() => {
            //扫码
            scanCode().then(result => {
              if (result) {
                handleAddItem(result);
              }
            })
          }}
        >
          <Image src={scan_add} width={'44px'} height={'44px'} />
        </div>
      </Drag>
      <div className="bg-white px-[28px] py-[32px]">
        <div className="flex justify-between items-center">
          <div className="flex text-[28px] text-black/60">已选 {localNumber}</div>
          <div className="">
            <Button type="primary" onClick={handleSubmit}>
              选好了
            </Button>
          </div>
        </div>
        <SafeArea position="bottom" className="bg-white" />
      </div>
      <Dialog id="confirm" />
    </div>
  );
}
