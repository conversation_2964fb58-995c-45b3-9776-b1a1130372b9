import { create } from 'zustand';
import { StockCheckModeEnum } from './list/types/StockCheckModeEnum';

type Store = {
  checkId: string;
  warehouseId: string;
  mode: StockCheckModeEnum;
  setCheckId: (checkId?: string) => void;
  setWarehouseId: (warehouseId?: string) => void;
  setMode: (mode?: StockCheckModeEnum) => void;
  reset: () => void;
};

const useCheckStore = create<Store>()((set) => ({
  checkId: '',
  warehouseId: '',
  mode: StockCheckModeEnum.MING_PAN,
  setCheckId: (checkId) => set(() => ({ checkId })),
  setWarehouseId: (warehouseId) => set(() => ({ warehouseId })),
  setMode: (mode: StockCheckModeEnum) => set(() => ({ mode })),
  reset: () => set(() => ({ checkId: '', warehouseId: '', mode: StockCheckModeEnum.MING_PAN })),
}));

export default useCheckStore;
