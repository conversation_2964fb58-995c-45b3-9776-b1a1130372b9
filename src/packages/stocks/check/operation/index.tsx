import notPic from '@/assets/not_pic.png';
import ChooseWarehouse from '@/components/ChooseWarehouse';
import CodeWithDivider from '@/components/CodeWithDivider';
import CustomNavBar from '@/components/CustomNavBar';
import GoodsSearchBar from '@/components/GoodsSearchBar';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import FunPagination from '@/components/Pagination/FunPagination';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import { ArrowRight, IconFont } from '@nutui/icons-react-taro';
import {
  Button,
  Cell,
  Checkbox,
  ConfigProvider, Dialog,
  Divider,
  Popup,
  Radio,
  SafeArea,
  Tag,
  TextArea,
} from '@nutui/nutui-react-taro';
import { Image } from '@tarojs/components';
import Taro, { navigateTo, showToast, useDidShow } from '@tarojs/taro';
import { useDebounceFn } from 'ahooks';
import _, { isEmpty, uniqueId } from 'lodash';
import { useEffect, useState } from 'react';
import { StockCheckModeEnum } from '../list/types/StockCheckModeEnum';
import { StockCheckStatusNameOptions } from '../list/types/StockCheckStatusEnum';
import { StockCheckTypeEnum } from '../list/types/StockCheckTypeEnum';
import { CheckDetailRequest } from '../list/types/check.detail.request';
import { CheckPostEntity } from '../list/types/check.post.entity';
import { CheckGoodsEntity } from '../selected/types/check.goods.entity';
import {
  checkCreateOrUpdatePost,
  confirmCheckPost, modifyCheckDeletePost,
  queryCheckByIdOrNo,
  queryCheckPostDetail,
} from '../services';
import useCheckStore from '../store';
import add from './image/add.svg';
import scan from './image/scan.svg';
import StableStepper from "@/components/StableStepper";
import icon_delete from "@/assets/icons/icon_remove.png";
import {DeleteCheckRequest} from "@/packages/stocks/check/checked/types/delete.check.request";
import {scanCode} from "@/utils/scanCode";

export default function Index() {
  const [recordData, setRecordData] = useState<CheckPostEntity>({});
  // 选择仓库
  const [visibleWarehouse, setVisibleWarehouse] = useState<boolean>(false);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [title, setTitle] = useState<string>('新增盘点');
  const [remarks, setRemarks] = useState<string>('');
  const [mode, setMode] = useState<StockCheckModeEnum | undefined>(StockCheckModeEnum.MING_PAN);
  const [paramData, setParamData] = useState<{ checkId: string }>({ checkId: '' });
  const [warehouseName, setWarehouseName] = useState<string | undefined>('请选择');
  const [warehouseId, setWarehouseId] = useState<string | undefined>('');
  const [onLoadFlag, setOnLoadFlag] = useState<boolean>(false);
  const [updateRecordData, setUpdateRecordData] = useState<CheckGoodsEntity[]>([]);
  const [updateLoading, setUpdateLoading] = useState<boolean>(false);
  const checkStore = useCheckStore();


  useEffect(() => {
    if (!isEmpty(checkStore.checkId)) {
      setParamData({ checkId: checkStore.checkId });
      loadData(checkStore.checkId);
      setTitle('继续盘点');
    }
  }, [checkStore.checkId]);

  useDidShow(() => {
    if (onLoadFlag) {
      setParamData((prevData) => ({ ...prevData, time: _.now() }));
    }
  });

  const loadData = async (id) => {
    const data = await queryCheckByIdOrNo({ id });
    if (data) {
      setRecordData(data);
      if (data) {
        setWarehouseId(data.warehouseId);
        setWarehouseName(data.warehouseName);
        setMode(data.mode);
        setParamData((prevData) => ({
          ...prevData,
          warehouseId: data.warehouseId,
          mode: data.mode,
        }));
      }
    }
  };

  const fetchData = (param?: CheckDetailRequest) => {
    if (isEmpty(param?.checkId)) {
      return [];
    }
    return queryCheckPostDetail({
      pageSize: 10,
      ...param,
      showItemExtra: true,
      isEdit: true,
      orderParam: 'updateTime',
      orderType: 1,
    }).then((result) => result?.data ?? []);
  };

  const getDiffAmountDom = (diffAmount) => {
    if (diffAmount > 0) {
      return <span className="text-[#F83431]">+{diffAmount}</span>;
    } else if (diffAmount! < 0) {
      return <span className="text-[#33CC47]">{diffAmount}</span>;
    } else {
      return <span>{diffAmount}</span>;
    }
  };

  const handleFilter = (e: [string]) => {
    let onlyUnInventoried = '';
    let onlyDiff = '';
    if (e.includes('1')) {
      onlyUnInventoried = '1';
    } else {
      onlyUnInventoried = '';
    }
    if (e.includes('2')) {
      onlyDiff = '1';
    } else {
      onlyDiff = '';
    }
    setParamData((prevData) => ({ ...prevData, onlyUnInventoried, onlyDiff }));
  };

  const { run } = useDebounceFn(
    (items: CheckGoodsEntity, v: number | string) => handleUpdateNumber(items, v),
    {
      wait: 100,
    },
  );

  const handleUpdateOrAdd = (newRecord: CheckGoodsEntity) => {
    setUpdateRecordData((prevData) => {
      const index = prevData?.findIndex((item) => item?.itemId === newRecord?.itemId);
      if (index != undefined && index !== -1) {
        // 如果找到匹配项，则更新它
        const updatedData = [...prevData!];
        updatedData[index!] = newRecord;
        return updatedData;
      } else {
        // 否则新增一项
        if (prevData) {
          return [...prevData, newRecord];
        } else {
          return [newRecord];
        }
      }
    });
  };

  const handleUpdateNumber = async (items, v: number | string) => {
    console.log(v);
    if (v != undefined && v != 'NaN' && v !== '') {
      setUpdateLoading(true)
      checkCreateOrUpdatePost({
        id: paramData?.checkId,
        warehouseId,
        stockCheckDetailCmdList: [{ ...items, checkAmount: v }],
      }).then(data => {
        if (data && data?.code === 0) {
          handleUpdateOrAdd(data?.data!);
        }
      }).finally(() => {
        setUpdateLoading(false);
      });
    }
  };

  /**
   * 扫码新增
   * @param itemId
   */
  const handleAddItem = async (itemId) => {
    const data = await checkCreateOrUpdatePost({
      id: isEmpty(paramData?.checkId) ? undefined : paramData?.checkId,
      warehouseId,
      mode,
      type: StockCheckTypeEnum.PART,
      stockCheckDetailCmdList: [
        {
          itemId,
          increment: true,
          checkId: isEmpty(paramData?.checkId) ? undefined : paramData?.checkId,
        },
      ],
    });
    if (data && data?.code === 0) {
      if (isEmpty(checkStore?.checkId)) {
        checkStore.setCheckId(data?.data?.id);
      }
      // else {
      //   handleUpdateOrAdd({ ...data?.data!, checkId: data?.data?.id });
      // }
      //跳转到已选商品列表
      navigateTo({ url: '/packages/stocks/check/checked/index' });
      setUpdateRecordData([]);
    }
  };

  const handleDeleteItem = async (param: DeleteCheckRequest) => {
    const data = await modifyCheckDeletePost(param);
    if (data) {
      setParamData((prevData) => ({ ...prevData, time: _.now() }));
    }
    Dialog.close('confirm');
  };

  const renderItem = (record: CheckGoodsEntity, index: number, length: number) => {
    const idList = [record?.id!]
    const firstMatchingRecord = updateRecordData?.find((s) => s.itemId === record.itemId);
    if (!isEmpty(firstMatchingRecord)) {
      record = { ...record, ...firstMatchingRecord };
    }
    return (
      <div className="py-[24]">
        <div className="flex justify-start py-[24px]">
          <div>
            <Image src={record.images?.[0] ?? notPic} style={{ width: '44px', height: '44px' }} />
          </div>
          <div className="px-[20px] flex flex-col flex-1">
            <div className="text-[32px] font-medium text-[#111111] flex-1 flex justify-between">
              <span>{record.itemName}</span>
              <span
                onClick={() => {
                  Dialog.open('confirm', {
                    title: '确认删除',
                    content: '是否确认删除？',
                    onConfirm: () => {
                      console.log(1234567, record)
                      handleDeleteItem({
                        idList,
                        checkId: record?.checkId,
                      });
                    },
                    onCancel: () => {
                      Dialog.close('confirm');
                    },
                  });
                }}
              >
                <IconFont name={icon_delete} width={'12px'} height={'14px'} />
              </span>
            </div>
            <div className="py-[12px] text-[24px] text-[#666666] flex flex-1">
              <ItemsWithDivider
                items={[record?.itemSn, record?.brandName, record?.categoryName, record?.unitName]}
              />
            </div>
            <div className="flex text-[24px] text-[#666666] pt-[8px] justify-between">
              <div>库存 {record.inventoryNum}</div>
              <div className="flex">
                <span className="pr-[8px]">盘点差异</span>
                {getDiffAmountDom(record?.diffAmount)}
              </div>
              <div>
                <CodeWithDivider key={record.id} title="库位" items={record.code?.split(',')} />
              </div>
            </div>
            <div className="flex text-[24px] text-[#666666] pt-[24px] justify-between items-center">
              <div>盘点库存</div>
              <div>{renderAddInput(record)}</div>
            </div>
          </div>
        </div>
        {index + 1 !== length && <Divider />}
      </div>
    );
  };

  const handleSelect = () => {
    if (isEmpty(warehouseId)) {
      //盘点是否选择了仓库
      showToast({
        title: '请选择仓库！',
        icon: 'none',
        duration: 2000,
      });
      return;
    }
    setOnLoadFlag(true);
    checkStore.setMode(mode);
    checkStore.setWarehouseId(warehouseId);
    navigateTo({
      url: '/packages/stocks/check/selected/index',
    });
    setUpdateRecordData([]);
  };

  //搜索条件
  const setInputValue = (param) => {
    setParamData((prevData) => ({
      ...prevData,
      ...param,
    }));
  };

  const renderAddInput = (record: CheckGoodsEntity) => {
    if (!isEmpty(record.checkId) && record.checkAmount != undefined) {
      return (
        <StableStepper
          key={uniqueId('pd_')}
          value={record.checkAmount}
          max={999999}
          min={0}
          disabled={updateLoading}
          onChange={(e) => {
            if (updateLoading) {
              return;
            }
            handleUpdateNumber(record, e);
          }}
          onClick={(e) => {
            e.stopPropagation();
          }}
        />
      );
    } else {
      return (
        <StableStepper key={uniqueId('pd_')} value={''} max={999999} min={0} onChange={(e) => run(record, e)} onClick={(e) => {
          e.stopPropagation();
        }}/>
      );
    }
  };

  const handleSubmit = async () => {
    if (!isEmpty(paramData?.checkId)) {
      //不为空
      const data = await confirmCheckPost({
        id: paramData?.checkId,
        bizBillNo: recordData?.bizBillNo,
        remarks,
      });
      if (data) {
        Taro.navigateBack();
      }
      setIsVisible(false);
    } else {
      setIsVisible(false);
      showToast({
        title: '请先盘点商品信息！',
        icon: 'none',
        duration: 2000,
      });
      return;
    }
  };

  return (
    <div className="h-screen flex flex-col">
      <Dialog id="confirm" />
      <CustomNavBar title={title} />
      <Cell.Group className="px-[28px]">
        <Cell
          title={
            <span className="flex">
              盘点仓库<span className="text-[#F83431] pl-[4px]">*</span>
            </span>
          }
          extra={
            <span
              className="flex"
              onClick={() => {
                if (isEmpty(paramData?.checkId)) {
                  setVisibleWarehouse(true);
                }
              }}
            >
              <span className="pr-[24px]">{warehouseName}</span>
              <ArrowRight color="#B2B2B2" />
            </span>
          }
        />
        <Cell
          title="盘点方式"
          extra={
            <Radio.Group
              direction="horizontal"
              disabled={!isEmpty(paramData?.checkId)}
              value={mode}
              onChange={(value: StockCheckModeEnum) => setMode(value)}
            >
              <Radio
                value={StockCheckModeEnum.MING_PAN}
                style={{
                  '--nut-icon-width': '16px',
                  '--nutui-icon-height': '16px',
                }}
              >
                明盘
              </Radio>
              <Radio
                value={StockCheckModeEnum.AN_PAN}
                style={{
                  '--nut-icon-width': '16px',
                  '--nutui-icon-height': '16px',
                }}
              >
                盲盘
              </Radio>
            </Radio.Group>
          }
        />
      </Cell.Group>
      <div className="mx-[28px] px-[86px] py-[38px] flex justify-between items-center bg-white rounded-xl">
        <PermissionComponent permission="addCheckPart">
          <div className="flex items-center" onClick={handleSelect}>
            <IconFont name={add} style={{ width: '15px' }} />
            <span className="pl-[16px]">添加商品</span>
          </div>
          <Divider direction="vertical" />
          <div
            className="flex items-center"
            onClick={(e) => {
              if (isEmpty(warehouseId)) {
                //盘点是否选择了仓库
                showToast({
                  title: '请选择仓库！',
                  icon: 'none',
                  duration: 2000,
                });
                return;
              }
              setOnLoadFlag(true);
              scanCode().then(result => {
                if (result) {
                  handleAddItem(result);
                }
              })
            }}
          >
            <IconFont name={scan} style={{ width: '15px' }} />
            <span className="pl-[16px]">扫码添加</span>
          </div>
        </PermissionComponent>
      </div>
      <div className="mt-[24px] mx-[28px] bg-white rounded-t-xl p-[28px]">
        <div>
          <div className="text-[32px] text-[#111111] font-medium ">商品列表</div>
          <ConfigProvider
            theme={{
              nutuiSearchbarPadding: '16px 0px 0px 0px',
            }}
          >
            <GoodsSearchBar
              placeholder="商品名称/编码"
              inputValue={setInputValue}
              keyStr="keyword"
            />
          </ConfigProvider>
        </div>
        <div className="pt-[24px]">
          <Checkbox.Group direction="horizontal" onChange={handleFilter}>
            <Checkbox value="1" shape="button">
              未盘点
            </Checkbox>
            <Checkbox value="2" shape="button">
              有差异
            </Checkbox>
          </Checkbox.Group>
        </div>
      </div>
      <div className="flex-1 min-h-0 overflow-y-scroll bg-white mx-[28px] mb-[24px] px-[28px] pb-[24px] rounded-b-xl">
        <FunPagination fetchData={fetchData} params={paramData} renderItem={renderItem} />
      </div>
      <div className="bg-white p-[28px]">
        {!isEmpty(recordData?.bizBillNo) && (
          <div className="bg-[#FFF4F4] -mt-[28px] leading-[56px] text-[24px] mb-[20px] -mx-[28px] px-[28px]">
            盘点单号: {recordData?.bizBillNo}
            <Tag className="ml-[24px]" type="primary">
              {StockCheckStatusNameOptions[recordData.state!]?.text}
            </Tag>
          </div>
        )}
        <PermissionComponent permission={'checkSubmit'}>
          <Button block type="primary" onClick={() => setIsVisible(true)}>
            <span className="text-white">提交盘点</span>
          </Button>
        </PermissionComponent>
        <SafeArea position="bottom" />
      </div>
      <ChooseWarehouse
        onConfirm={([selectOption]) => {
          setVisibleWarehouse(false);
          setWarehouseName(selectOption.text?.toString());
          setWarehouseId(selectOption.value?.toString());
        }}
        handleClose={() => setVisibleWarehouse(false)}
        visible={visibleWarehouse}
      />
      <Popup
        visible={isVisible}
        position="bottom"
        closeOnOverlayClick={false}
        onClose={() => setIsVisible(false)}
        left={<span className="font-medium text-[32px] text-[#111111] pt-[32px]">提交盘点</span>}
        closeable
      >
        <span className="px-[28px] p-[32px]">备注</span>
        <TextArea showCount maxLength={200} placeholder="请输入备注信息" onChange={setRemarks} />
        <div className="bg-white px-[28px] py-[24px]">
          <Button block type="primary" onClick={() => handleSubmit()}>
            <span className="text-white">提交</span>
          </Button>
          <SafeArea position="bottom" />
        </div>
      </Popup>
    </div>
  );
}
