import notPic from '@/assets/not_pic.png';
import Card from '@/components/Card';
import CodeWithDivider from '@/components/CodeWithDivider';
import CustomNavBar from '@/components/CustomNavBar';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import FunPagination from '@/components/Pagination/FunPagination';
import RadioButtonGroup from '@/components/RadioButtonGroup';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import { Button, Dialog, Divider, Popup, SafeArea, TextArea } from '@nutui/nutui-react-taro';
import { Image } from '@tarojs/components';
import { navigateTo, useDidShow, useLoad } from '@tarojs/taro';
import _, { isEmpty } from 'lodash';
import { useState } from 'react';
import { StockCheckModeOptions } from '../list/types/StockCheckModeEnum';
import {
  StockCheckStatusEnum,
  StockCheckStatusNameOptions,
} from '../list/types/StockCheckStatusEnum';
import { CheckDetailRequest, CheckPostCancelRequest } from '../list/types/check.detail.request';
import { CheckPostEntity } from '../list/types/check.post.entity';
import {
  approveCheckPost,
  cancelCheckPost,
  queryCheckByIdOrNo,
  queryCheckPostDetail,
  rejectCheckPost,
} from '../services';
import useCheckStore from '../store';
import { AuditStatus, auditStatusOptions } from './types/AuditStatus';
import { AuditPostRequest } from './types/audit.post.request';
import { CheckDetailPostEntity } from './types/check.detail.post.entity';

export default function Index() {
  const [title, setTitle] = useState<string>('');
  const [recordData, setRecordData] = useState<CheckPostEntity>({});
  const [params, setParams] = useState<any>({});
  const [onLoadFlag, setOnLoadFlag] = useState<boolean>(false);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [remarks, setRemarks] = useState<string>('');
  const [auditStatus, setAuditStatus] = useState<string>(AuditStatus.APPROVE);
  const checkStore = useCheckStore();

  useLoad(({ id }) => {
    loadData(id);
    setParams({ checkId: id });
  });

  useDidShow(() => {
    if (onLoadFlag) {
      loadData(params?.checkId);
      setParams((prevData) => ({ ...prevData, time: _.now() }));
    }
  });

  const loadData = async (id) => {
    const data = await queryCheckByIdOrNo({ id });
    if (data) {
      setRecordData(data);
      setTitle(StockCheckStatusNameOptions[data.state!]?.text);
    }
  };

  const fetchData = (param?: CheckDetailRequest) => {
    if (isEmpty(param?.checkId)) {
      return [];
    }
    return queryCheckPostDetail({
      pageSize: 10,
      ...param,
      showItemExtra: true,
      orderParam: 'updateTime',
      orderType: 1,
    }).then((result) => result?.data ?? []);
  };

  const getDiffAmountDom = (diffAmount) => {
    if (diffAmount > 0) {
      return <span className="text-[#F83431]">+{diffAmount}</span>;
    } else if (diffAmount! < 0) {
      return <span className="text-[#33CC47]">{diffAmount}</span>;
    } else {
      return <span>{diffAmount}</span>;
    }
  };

  const handleAudit = async (values: AuditPostRequest) => {
    if (AuditStatus.APPROVE == values.result) {
      //审核通过
      const data = await approveCheckPost({ id: recordData.id, bizBillNo: recordData.bizBillNo });
      if (data) {
        loadData(recordData.id);
      }
    } else if (AuditStatus.REJECT == values.result) {
      //拒绝
      const data = await rejectCheckPost({
        rejectRemark: values.auditRemark,
        id: recordData.id,
        bizBillNo: recordData.bizBillNo,
      });
      if (data) {
        loadData(recordData.id);
      }
    }
    setIsVisible(false);
  };

  const renderItem = (items: CheckDetailPostEntity, index: number, length: number) => (
    <div>
      <div className={`flex justify-start ${index === 0 ? 'pb-[24px] pt-[8px]' : 'py-[24px]'}`}>
        <div>
          <Image
            src={items.images?.[0] ?? notPic}
            className="rounded bg-[#f5f5f5]"
            style={{ width: '44px', height: '44px' }}
          />
        </div>
        <div className="px-[20px] flex flex-1 flex-col">
          <div className=" text-[32px] font-medium text-[#111111]">{items.itemName}</div>
          <div className=" py-[12px] text-[24px] text-[#666666] flex">
            <ItemsWithDivider
              items={[items?.itemSn, items?.brandName, items?.categoryName, items?.unitName]}
            />
          </div>
          <div className="flex text-[24px] text-[#666666] pt-[8px] justify-between">
            <div className="flex gap-8">
              <div>库存 {items.stockAmount}</div>
              <div className="flex gap-[8px]">
                盘点库存 {items.checkAmount}
                {getDiffAmountDom(items.diffAmount)}
              </div>
            </div>
            <div>
              <CodeWithDivider title="库位" items={items.code?.split(',')} />
            </div>
          </div>
        </div>
      </div>
      {index + 1 !== length && <Divider />}
    </div>
  );
  const handleCheckCancel = async (values: CheckPostCancelRequest) => {
    const data = await cancelCheckPost(values);
    if (data) {
      loadData(values.id);
      Dialog.close('confirm');
    }
  };

  return (
    <div className="h-screen flex flex-col">
      <CustomNavBar title={title} />
      <div className="pt-[32px] pl-[28px] text-[34px] font-medium text-[#111111]">
        {recordData.warehouseName}
      </div>
      <div className="pt-[12px] pl-[28px] text-[28px] text-[#666666]">{recordData.bizBillNo}</div>
      <Card className="text-[28px]">
        <div className="flex py-[12px]">
          <span className=" text-[#666666] w-[140px]">盘点方式：</span>
          <span className="text-[#111111]">{StockCheckModeOptions[recordData.mode!]?.text}</span>
        </div>
        <div className="flex py-[12px]">
          <span className="text-[#666666] w-[140px]">制单时间：</span>
          <span className="text-[#111111]">{recordData.createTime}</span>
        </div>
        <div className="flex py-[12px]">
          <span className="text-[#666666] w-[140px]">制单人：</span>
          <span className="text-[#111111]">{recordData.createDocPerson}</span>
        </div>
        <div className="flex py-[12px] pb-[24px]">
          <span className=" text-[#666666] w-[140px]">备注：</span>
          <span className="text-[#111111]">{recordData.remarks}</span>
        </div>
        {recordData?.profitNum != undefined && (
          <>
            <Divider />
            <div className="flex justify-around pt-[32px]">
              <div className="flex flex-col items-center gap-[8px] w-[170px]">
                <div className="text-[24px] text-[#666666]">盘盈数量</div>
                <div className="text-[32px] text-[#F83431]">{recordData?.profitNum}</div>
              </div>
              <div className="flex flex-col items-center gap-[8px] w-[170px]">
                <div className="text-[24px] text-[#666666]">盘盈金额</div>
                <div className="text-[32px] text-[#F83431] ">{recordData?.profitCostPrice}</div>
              </div>
              <div className="flex flex-col items-center gap-[8px] w-[170px]">
                <div className="text-[24px] text-[#666666]">盘亏数量</div>
                <div className="text-[32px] text-[#33CC47]">{recordData?.lossNum}</div>
              </div>
              <div className="flex flex-col items-center gap-[8px] w-[170px]">
                <div className="text-[24px] text-[#666666]">盘亏金额</div>
                <div className="text-[32px] text-[#33CC47]">{recordData?.lossCostPrice}</div>
              </div>
            </div>
          </>
        )}
      </Card>
      {StockCheckStatusEnum.REJECTED == recordData?.state && (
        <Card className="text-[28px] mt-[0px]">
          <div className="flex py-[12px]">
            <span className=" text-[#666666] w-[140px]">审核人：</span>
            <span className="text-[#111111]">{recordData?.auditPerson}</span>
          </div>
          <div className="flex py-[12px]">
            <span className="text-[#666666] w-[140px]">审核时间：</span>
            <span className="text-[#111111]">{recordData?.auditTime}</span>
          </div>
          <div className="flex py-[12px]">
            <span className="text-[#666666] w-[140px]">审核原因：</span>
            <span className="text-[#111111]">{recordData?.rejectRemark}</span>
          </div>
        </Card>
      )}
      <Card title="商品列表" className="mx-3 mt-[0px] flex-1 flex flex-col min-h-0">
        <div className="flex-1 min-h-0 overflow-y-scroll">
          <FunPagination fetchData={fetchData} params={params} renderItem={renderItem} />
        </div>
      </Card>

      <div className="bg-white px-[28px] py-[24px]">
        <div className="flex justify-end bg-white  gap-[24px]">
          {(StockCheckStatusEnum.INVENTORY_IN_PROGRESS == recordData?.state ||
            StockCheckStatusEnum.REJECTED == recordData?.state) && (
            <>
              <PermissionComponent permission={'deleteWarehouseCheck'}>
                <Button
                  onClick={() => {
                    Dialog.open('confirm', {
                      title: '确认操作',
                      content: '是否确认作废？',
                      onConfirm: () => {
                        handleCheckCancel({
                          id: recordData?.id,
                          bizBillNo: recordData?.bizBillNo,
                        });
                      },
                      onCancel: () => {
                        Dialog.close('confirm');
                      },
                    });
                  }}
                >
                  作废
                </Button>
              </PermissionComponent>
              <PermissionComponent permission={'warehouseContinueCheck'}>
                <Button
                  type="primary"
                  onClick={() => {
                    setOnLoadFlag(true);
                    checkStore.setCheckId(recordData?.id);
                    navigateTo({
                      url: `/packages/stocks/check/operation/index`,
                    });
                  }}
                >
                  继续盘点
                </Button>
              </PermissionComponent>
            </>
          )}
          {StockCheckStatusEnum.PENDING == recordData?.state && (
            <PermissionComponent permission={'warehouseCheckAudit'}>
              <Button type="primary" onClick={() => setIsVisible(true)}>
                审核
              </Button>
            </PermissionComponent>
          )}
        </div>
        <Dialog id="confirm" />
        <Popup
          visible={isVisible}
          position="bottom"
          closeOnOverlayClick={false}
          onClose={() => setIsVisible(false)}
          left={<span className="font-medium text-[32px] text-[#111111] pt-[32px]">审核</span>}
          closeable
        >
          <div className="flex pt-[43px] pl-[28px]">
            <RadioButtonGroup
              value={auditStatus}
              options={auditStatusOptions}
              onChange={(value: AuditStatus) => setAuditStatus(value)}
            />
          </div>
          {auditStatus == AuditStatus.REJECT && (
            <>
              <span className="px-[28px] pt-[32px]">不通过原因</span>
              <TextArea
                showCount
                maxLength={200}
                placeholder="请输入备注信息"
                onChange={setRemarks}
              />
            </>
          )}
          <div className="bg-white px-[28px] py-[24px]">
            <PermissionComponent permission="checkSubmit">
              <Button
                block
                type="primary"
                onClick={() => handleAudit({ result: auditStatus, auditRemark: remarks })}
              >
                提交
              </Button>
            </PermissionComponent>
            <SafeArea position="bottom" />
          </div>
        </Popup>
        <SafeArea position="bottom" />
      </div>
    </div>
  );
}
