import '@/assets/lightPage.scss';
import CustomSearchBar from '@/components/CustomSearchBar';
import MenuWrap from '@/components/MenuWrap';
import FunPagination from '@/components/Pagination/FunPagination';
import usePermissionStore from '@/pages/splash/permissionStore';
import { Divider, Menu, Tag } from '@nutui/nutui-react-taro';
import { navigateTo, useDidShow, useLoad } from '@tarojs/taro';
import _, { isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { warehouseList } from '../../inventory/services';
import { queryCheckPagePost } from '../services';
import useCheckStore from '../store';
import { StockCheckStatusMenu, StockCheckStatusNameOptions } from './types/StockCheckStatusEnum';
import { CheckPostEntity } from './types/check.post.entity';
import { QueryPostListRequest } from './types/query.post.list.request';
import CustomNavBar from "@/components/CustomNavBar";

export default function Index() {
  const { hasPermission } = usePermissionStore(
    useShallow((store) => ({
      hasPermission: store.hasPermission,
    })),
  );
  const [wareHouseOptions, setWareHouseOptions] = useState<any>([]);
  const [warehouseIdList, setWarehouseIdList] = useState<string[] | undefined>(undefined);
  const [stateList, setStateList] = useState<string[] | undefined>(undefined);
  const [params, setParams] = useState<any>({});
  const [onLoadFlag, setOnLoadFlag] = useState<boolean>(false);
  const checkStore = useCheckStore();
  useLoad(async () => {
    const { warehouseSimpleRoList } = await warehouseList({});
    if (!isEmpty(warehouseSimpleRoList)) {
      setWareHouseOptions(
        warehouseSimpleRoList?.map((s) => ({ text: s.warehouseName, value: s.id })),
      );
    }
  });

  useDidShow(() => {
    if (onLoadFlag) {
      setParams((prevData) => ({ ...prevData, time: _.now() }));
    }
  });

  const itemList = [
    { key: 'bizBillNo', name: '盘点单号', scanShow: true },
    { key: 'itemSn', name: '商品信息', scanShow: false },
  ];

  useEffect(() => {
    if (stateList !== undefined || warehouseIdList !== undefined) {
      setParams((prevData) => ({
        ...prevData,
        stateList,
        warehouseIdList,
      }));
    }
  }, [stateList, warehouseIdList]);

  //搜索条件
  const setInputValue = (param) => {
    if (!isEmpty(param)) {
      setParams((prevData) => ({ ...prevData, ...param }));
    }
  };
  const fetchData = (param?: QueryPostListRequest) => {
    return queryCheckPagePost({ pageSize: 10, ...param }).then((result) => result?.data ?? []);
  };

  const renderItem = (record: CheckPostEntity) => (
    <div
      className="mt-[24px] p-[28px] bg-white mx-[28px] rounded-xl"
      onClick={() => {
        setOnLoadFlag(true);
        navigateTo({
          url: '/packages/stocks/check/detail/index?id=' + record.id,
        });
      }}
    >
      <div className="flex justify-between pb-[20px]">
        <div className="text-[32px] font-medium">{record.warehouseName}</div>
        <div>
          <Tag type={StockCheckStatusNameOptions[record.state!]?.status}>
            {StockCheckStatusNameOptions[record.state!]?.text}
          </Tag>
        </div>
      </div>
      <Divider />
      <div className="pt-[20px] text-[28px] text-[#666666] flex">
        <span className="w-[112px]">盘点单号</span>
        <span className="pl-[24px]">{record.bizBillNo}</span>
      </div>
      <div className="pt-[12px] text-[28px] text-[#666666] flex">
        <span className="w-[112px]">制单时间</span>
        <span className="pl-[24px]">{record.createTime}</span>
      </div>
      {((record.profitNum != undefined && record.profitNum > 0) ||
        (record.lossNum != undefined && record.lossNum > 0)) && (
        <div className="pt-[12px] text-[28px] text-[#666666] flex">
          <span className="w-[112px]">盘点结果</span>
          <span className="pl-[24px]">
            盘盈数量 {record.profitNum} | 盘亏数量 {record.lossNum}
          </span>
        </div>
      )}
    </div>
  );

  return (
    <div className="h-screen flex flex-col">
      <CustomNavBar title="盘点列表" showBack={true} />
      <div className="px-[28px] pt-[16px]">
        <CustomSearchBar
          itemList={itemList}
          defaultItem={{
            key: 'bizBillNo',
            name: '盘点单号',
            scanShow: true,
          }}
          inputValue={setInputValue}
          isShowAdd={hasPermission('addWarehouseCheck')}
          addUrl={() => {
            checkStore.reset();
            setOnLoadFlag(true);
            navigateTo({ url: '/packages/stocks/check/operation/index' });
          }}
        />
      </div>
      <div className="custom-menu pt-[24px]">
        <MenuWrap
          menu={
            <Menu>
              <Menu.Item
                title={'盘点仓库'}
                options={[{ text: '全部', value: '' }, ...wareHouseOptions]}
                onChange={(e) => {
                  if (e.value === '') {
                    setWarehouseIdList([]);
                  } else {
                    setWarehouseIdList([e.value]);
                  }
                }}
              ></Menu.Item>
              <Menu.Item
                title={'单据状态'}
                options={StockCheckStatusMenu}
                onChange={(e) => {
                  if (e.value === '') {
                    setStateList([]);
                  } else {
                    setStateList([e.value]);
                  }
                }}
              ></Menu.Item>
            </Menu>
          }
        />
      </div>
      <div className="flex-1 min-h-0">
        <FunPagination fetchData={fetchData} params={params} renderItem={renderItem} />
      </div>
    </div>
  );
}
