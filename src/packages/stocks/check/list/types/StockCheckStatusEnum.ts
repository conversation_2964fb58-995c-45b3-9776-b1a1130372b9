export enum StockCheckStatusEnum {
  CANCEL = 0,
  INVENTORY_IN_PROGRESS = 1,
  PENDING = 2,
  REJECTED = 3,
  COMPLETED = 4,
}

export enum StockCheckStatusName {
  ALL = '全部',
  CANCEL = '已关闭',
  INVENTORY_IN_PROGRESS = '盘点中',
  PENDING = '待审核',
  REJECTED = '已驳回',
  COMPLETED = '已完成',
}

export const StockCheckStatusMenu = [
  { text: StockCheckStatusName.ALL, value: '' },
  { text: StockCheckStatusName.CANCEL, value: StockCheckStatusEnum.CANCEL },
  {
    text: StockCheckStatusName.INVENTORY_IN_PROGRESS,
    value: StockCheckStatusEnum.INVENTORY_IN_PROGRESS,
  },
  { text: StockCheckStatusName.PENDING, value: StockCheckStatusEnum.PENDING },
  { text: StockCheckStatusName.REJECTED, value: StockCheckStatusEnum.REJECTED },
  { text: StockCheckStatusName.COMPLETED, value: StockCheckStatusEnum.COMPLETED },
];

export const StockCheckStatusNameOptions = {
  [StockCheckStatusEnum.CANCEL]: {
    text: StockCheckStatusName.CANCEL,
    status: 'default',
    bg: '#00000008',
  },
  [StockCheckStatusEnum.INVENTORY_IN_PROGRESS]: {
    text: StockCheckStatusName.INVENTORY_IN_PROGRESS,
    status: 'primary',
    bg: '#FFF4F4',
  },
  [StockCheckStatusEnum.PENDING]: {
    text: StockCheckStatusName.PENDING,
    status: 'primary',
    bg: '#FFF4F4',
  },
  [StockCheckStatusEnum.REJECTED]: {
    text: StockCheckStatusName.REJECTED,
    status: 'primary',
    bg: '#FFF4F4',
  },
  [StockCheckStatusEnum.COMPLETED]: {
    text: StockCheckStatusName.COMPLETED,
    status: 'success',
    bg: '#EAF9EC',
  },
};
