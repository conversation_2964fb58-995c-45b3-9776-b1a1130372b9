import {PaginationRequest} from '@/types/PaginationRequest';

export interface QueryPostListRequest extends PaginationRequest {
  /**
   * 盘点单号
   */
  bizBillNo?: string;
  /**
   * 盘点仓库
   */
  warehouseIdList?: string[];
  /**
   * 盘点状态
   */
  stateList?: number[];
  /**
   * 商品名称（模糊）商品编码（精确）
   */
  keyword?: string;

  /**
   * 制单人
   */
  createDocPerson?: string;

  /**
   * 制单人工号
   */
  createDocPersonNo?: string;

  /**
   * 开始创建时间
   */
  startCreateTime?: string;
  /**
   * 开始结束时间
   */
  endCreateTime?: string;
}
