import { PaginationRequest } from '@/types/PaginationRequest';

export interface CheckDetailRequest extends PaginationRequest {
  /**
   * 盘点主单ID
   */
  checkId?: string;
  /**
   * 是否商品其他信息（包含图片等）
   */
  showItemExtra?: boolean;
  /**
   * 商品编码商品名称模糊搜索
   */
  keyword?: string;
  /**
   * 盘点方式1:明盘2:盲盘
   */
  mode?: number;
  /**
   * 仅看有差异-值为1
   */
  onlyDiff?: number;
  /**
   * 仅看未盘点-值为1
   */
  onlyUnInventoried?: number;

  isEdit?: boolean;

  /**
   * 仓库id
   */
  warehouseId?: string;

  orderParam?: string;

  orderType?: number;
}

/**
 * 详情页面主盘点信息请求对象
 */
export interface StockCheckSimpleRequest {
  /**
   * 盘点主单ID
   */
  id?: string;
  /**
   * 盘点仓库仓库id
   */
  warehouseId?: string;
  /**
   * 盘点单号
   */
  bizBillNo?: string;
}

/**
 * 取消请求对象
 */
export interface CheckPostCancelRequest {
  /**
   * 盘点id
   */
  id?: string;

  /**
   * 盘点单号
   */
  bizBillNo?: string;
}
