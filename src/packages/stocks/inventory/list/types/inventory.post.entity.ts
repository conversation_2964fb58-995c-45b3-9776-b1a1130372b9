export interface InventoryPostEntity {
  id?: string;
  /**
   * 可用库存
   */
  avaNum?: number;
  /**
   * 品牌Id
   */
  brandId?: string;
  /**
   * 品牌名称
   */
  brandName?: string;

  images?: string[];
  /**
   * 类目Id
   */
  categoryId?: string;
  /**
   * 类目名称
   */
  categoryName?: string;
  /**
   * 库位
   */
  code?: string;

  locationIdList?: string[];

  /**
   * 成本价
   */
  costPrice?: string;
  /**
   * 账面库存
   */
  inventoryNum?: number;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 冻结库存
   */
  lockedNum?: number;
  /**
   * 安全库存下限
   */
  lowerLimit?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * oe码
   */
  oeNo?: string;
  /**
   * ETC号
   */
  thirdNo?: string;
  /**
   * 在途库存
   */
  transitNum?: number;
  /**
   * 单位id
   */
  unitId?: string;
  /**
   * 单位名称
   */
  unitName?: string;
  /**
   * 安全库存上限
   */
  upperLimit?: number;
  /**
   * 仓库id
   */
  warehouseId?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;

  locationRoList?: LocationRo[];
}

export interface LocationRo {
  key?: string;
  value?: string;
  code?: string;
  id?: string;
}
