import type { PaginationRequest } from '@/types/PaginationRequest';

export interface QueryPostListRequest extends PaginationRequest {
  /**
   * 品牌Id
   */
  brandIdList?: string[];
  /**
   * 类目Id
   */
  categoryIdList?: string[];

  warehouseIdList?: string[];
  /**
   * 成本价权限：0-无，1-有
   */
  haveCostPriceAuthority?: number;
  /**
   * 库存有货状态:0-库存等于0,1-库存大于0,单选
   */
  haveInv?: number;
  /**
   * 库位状态:0-无货位,1-有货位,单选
   */
  haveItemCode?: number;
  /**
   * 库存预警状态:1-低于下限2-高于上限,支持多选筛选
   */
  invLimitStatusList?: number[];
  /**
   * 商品名称（模糊）商品编码（精确）OE（模糊）品牌件号（模糊）ETC号（精确）
   */
  keyword?: string;

  /**
   * 是否仅统计总数,首页统计总数时，传true
   */
  onlyTotalStatistics?: boolean;

  /**
   * 门店ID
   */
  storeId?: string;

  showItemExtra?: boolean;
}
