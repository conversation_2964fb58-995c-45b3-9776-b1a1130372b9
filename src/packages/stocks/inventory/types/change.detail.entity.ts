export interface ChangeDetailEntity {
  /**
   * 单据类型
   */
  billType?: number;
  /**
   * 单据类型描述
   */
  billTypeDesc?: string;
  /**
   * 业务单号
   */
  bizBillNo?: string;
  /**
   * 变更的库存类型
   */
  changeInvType?: string;
  /**
   * 变更的库存类型描述
   */
  changeInvTypeDesc?: string;
  /**
   * 变更数量正数表示增加负数表示减少
   */
  changeNum?: number;
  /**
   * 变更时间==出库时间入库时间
   */
  changeTime?: string;
  /**
   * 库存流水id
   */
  id?: string;
  /**
   * None
   */
  isDelete?: number;

  /**
   * 业务单号
   */
  origBillNo?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 出入库单号
   */
  stockNo?: string;

  /**
   * 仓库id
   */
  warehouseId?: string;
  /**
   * 仓库id
   */
  warehouseName?: string;
}
