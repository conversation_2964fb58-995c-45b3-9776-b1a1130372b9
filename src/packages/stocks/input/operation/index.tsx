import notPic from '@/assets/not_pic.png';
import scan_add from '@/assets/scan_add.svg';
import CodeWithDivider from '@/components/CodeWithDivider';
import CustomNavBar from '@/components/CustomNavBar';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import { Button, Divider, Drag, Image, InputNumber, SafeArea } from '@nutui/nutui-react-taro';
import Taro, {navigateBack, useLoad} from '@tarojs/taro';
import { add, isEmpty } from 'lodash';
import { useState } from 'react';
import { InPutDetailPostEntity } from '../detail/types/input.detail.post.entity';
import { confirmInPutPost, queryInPutDetailPost } from '../services';
import { scanCode } from "@/utils/scanCode";

export default function Index() {
  const [recordData, setRecordData] = useState<InPutDetailPostEntity>({});
  const [newrecordData, setNewRecordData] = useState<InPutDetailPostEntity>({});
  useLoad(async ({ stockInId }) => {
    const data = await queryInPutDetailPost({ stockInId });
    if (data != null) {
      data?.stockInDetailRoList?.forEach((item) => (item.remainAmount = 0));
    }
    setRecordData(data);
    setNewRecordData(data);
  });

  const handelInPut = async () => {
    const sum = newrecordData?.stockInDetailRoList
      ?.map((s) => s.remainAmount as number)
      ?.reduce(
        (accumulator: number, currentValue: number) =>
          add(Number(accumulator), Number(currentValue)),
        0,
      );
    if (sum === 0) {
      Taro.showToast({ title: '请至少入库一个商品！', icon: 'none' });
      return;
    }
    const data = await confirmInPutPost({
      ...newrecordData.stockInRo,
      stockInDetailCmdList: newrecordData?.stockInDetailRoList,
    });
    if (data) {
      navigateBack();
    }
  };

  const handleAddItem = async (itemId) => {
    const filterData = recordData?.stockInDetailRoList?.find((s) => s.itemId == itemId);
    if (!isEmpty(filterData)) {
      let flag = true;
      const newDetatl = recordData?.stockInDetailRoList?.map((s) => {
        if (s.itemId === itemId) {
          if (Number(s.remainAmount ?? 0) + 1 > (s.preAmount ?? 0) - (s.realAmount ?? 0)) {
            Taro.showToast({ title: '已达到最大入库数量', icon: 'none' });
            flag = false;
          }
          return { ...s, remainAmount: Number(s.remainAmount ?? 0) + 1 };
        }
        return s;
      });
      if (flag) {
        setRecordData((reData) => ({ ...reData, stockInDetailRoList: newDetatl }));
        setNewRecordData((reData) => ({ ...reData, stockInDetailRoList: newDetatl }));
      }
    } else {
      Taro.showToast({ title: '该商品不在入库列表中！', icon: 'none' });
      return;
    }
  };

  return (
    <div className="h-screen flex flex-col">
      <div className="flex-1 min-h-0">
        <CustomNavBar title="部分入库" />
        <div className="pt-[16px]">
          <div className="bg-white mx-[28px] rounded-xl">
            {recordData?.stockInDetailRoList?.map((item, index) => (
              <div>
                <div className="flex justify-start p-[24px]">
                  <div>
                    <Image
                      src={item.images?.[0] ?? notPic}
                      className="rounded bg-[#f5f5f5]"
                      width={'44px'}
                      height={'44px'}
                    />
                  </div>
                  <div className="px-[20px] flex flex-col flex-1">
                    <div className="text-[32px] font-medium text-[#111111] flex justify-between">
                      <span>{item.itemName}</span>
                    </div>
                    <div className="py-[12px] text-[24px] text-[#666666] flex">
                      <ItemsWithDivider
                        items={[item?.itemSn, item?.brandName, item?.categoryName, item?.unitName]}
                      />
                    </div>
                    <div className="flex text-[24px] text-[#666666] pt-[8px] justify-between">
                      <div className="flex">
                        <span>计划入库</span>
                        <span>{item.preAmount}</span>
                      </div>
                      <div className="flex">已入库 {item.realAmount}</div>
                      <div>
                        <CodeWithDivider title="库位" items={item.code?.split(',')} />
                      </div>
                    </div>
                    <div className="flex text-[24px] text-[#666666] pt-[24px] justify-between items-center">
                      <div className="text-black/90">本次入库</div>
                      <div>
                        <InputNumber
                          allowEmpty={true}
                          max={(item.preAmount ?? 0) - (item.realAmount ?? 0)}
                          min={0}
                          value={item.remainAmount}
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                          onChange={(e: number) => {
                            let value: number = 0;
                            if (e > (item.preAmount ?? 0) - (item.realAmount ?? 0)) {
                              value = (item.preAmount ?? 0) - (item.realAmount ?? 0);
                            } else if (e < 0) {
                              value = 0;
                            } else {
                              value = e;
                            }
                            item.remainAmount = parseInt(value.toString(), 10);
                            // 更新状态以触发重新渲染
                            setNewRecordData({
                              ...newrecordData,
                              stockInDetailRoList: [...recordData.stockInDetailRoList!],
                            });
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                {index + 1 < recordData?.stockInDetailRoList?.length! && <Divider />}
              </div>
            ))}
          </div>
          <Drag style={{ bottom: '150px', right: '14px' }}>
            <div
              className="shadow shadow-transparent"
              onClick={() => {
                //扫码
                scanCode().then(result => {
                  if (result) {
                    handleAddItem(result);
                  }
                })
              }}
            >
              <Image src={scan_add} style={{ width: '44px', height: '44px' }} />
            </div>
          </Drag>
        </div>
      </div>
      <div className="bg-white px-[28px] py-[24px] mt-[28px]">
        <PermissionComponent permission={'inWarehouse'}>
          <Button block type="primary" onClick={handelInPut}>
            入库
          </Button>
        </PermissionComponent>
        <SafeArea position="bottom" />
      </div>
    </div>
  );
}
