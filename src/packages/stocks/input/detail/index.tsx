import empty from '@/assets/empty.png';
import notPic from '@/assets/not_pic.png';
import Card from '@/components/Card';
import CodeWithDivider from '@/components/CodeWithDivider';
import CustomNavBar from '@/components/CustomNavBar';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import { Button, Dialog, Divider, SafeArea, Tabs, Tag } from '@nutui/nutui-react-taro';
import { Image } from '@tarojs/components';
import Taro, { useLoad } from '@tarojs/taro';
import { isEmpty } from 'lodash';
import { useState } from 'react';
import { inPutStatusOptions } from '../list/types/InPutStatus';
import { InputPostWithdrawRequest } from '../list/types/input.post.withdraw.request';
import { InputStockBatchList } from '../list/types/input.stock.batch.list';
import { queryBatchDetailPost, queryInPutDetailPost, withdrawInputPost } from '../services';
import { InPutDetailPostEntity } from './types/input.detail.post.entity';
import { InPutDetailRequest } from './types/input.detail.request';

export default function Index() {
  useLoad(({ stockInId, warehouseId }) => {
    loadData({ stockInId, warehouseId });
    setParamData({ stockInId, warehouseId });
  });

  const [paramData, setParamData] = useState({});
  const [recordData, setRecordData] = useState<InPutDetailPostEntity>({});
  const [recordInputData, setRecordInputData] = useState<InputStockBatchList>({});
  const [currentTabKey, setCurrentTabKey] = useState<'1' | '2'>('1');

  const loadData = (params: InPutDetailRequest) => {
    handleQueryDetail(params);
    handleBatchDetailQuery(params);
  };

  const handleQueryDetail = async (params: InPutDetailRequest) => {
    const data = await queryInPutDetailPost(params);
    setRecordData(data);
  };
  const handleBatchDetailQuery = async (params: InPutDetailRequest) => {
    const outData = await queryBatchDetailPost(params);
    setRecordInputData(outData);
  };

  //作废
  const handleWithdrawInput = async (values: InputPostWithdrawRequest) => {
    const data = await withdrawInputPost(values);
    if (data) {
      Dialog.close('confirm');
      //刷新
      loadData(paramData);
    }
  };
  return (
    <>
      <CustomNavBar title="入库详情" />
      <div className="tabs">
        <Tabs
          align="left"
          value={currentTabKey}
          onChange={(v) => setCurrentTabKey(v as '1' | '2')}
          style={{
            '--nutui-tabs-tabpane-padding': '0px',
            '--nutui-tabs-titles-background-color': 'none',
          }}
        >
          <Tabs.TabPane title="入库详情" value="1"></Tabs.TabPane>
          <Tabs.TabPane title="入库记录" value="2"></Tabs.TabPane>
        </Tabs>
        {currentTabKey === '1' && (
          <>
            <Card title={recordData?.stockInRo?.warehouseName} className="mt-[12px]">
              <div className="flex items-center pb-[24px]">
                <span className="text-[28px] text-black/60 pr-[16px]">
                  {recordData?.stockInRo?.bizBillNo}
                </span>
                <Tag type={inPutStatusOptions[recordData?.stockInRo?.state!]?.status}>
                  {inPutStatusOptions[recordData?.stockInRo?.state!]?.text}
                </Tag>
              </div>
              <div style={{ marginRight: '-28px', marginLeft: '-28px' }}>
                <Divider />
              </div>
              <div className="pt-[24px] text-[28px]  flex">
                <span className="text-black/60">发货方：</span>
                <span className="text-black/90">{recordData?.stockInRo?.promoter}</span>
              </div>
              <div className="pt-[24px] text-[28px]  flex">
                <span className="text-black/60">业务单号：</span>
                <span className="text-black/90">{recordData?.stockInRo?.origBillNo}</span>
              </div>
              <div className="pt-[24px] text-[28px]  flex">
                <span className="text-black/60">入库类型：</span>
                <span className="text-black/90">{recordData?.stockInRo?.billTypeDesc}</span>
              </div>
              <div className="pt-[24px] text-[28px]  flex">
                <span className="text-black/60">通知入库：</span>
                <span className="text-black/90">{recordData?.stockInRo?.createTime}</span>
              </div>
              <div className="pt-[24px] text-[28px]  flex">
                <span className="text-black/60">完成入库：</span>
                <span className="text-black/90">{recordData?.stockInRo?.realInTime}</span>
              </div>
              <div className="pt-[24px] text-[28px]  flex">
                <span className="text-black/60">入库数量：</span>
                <span className="text-black/90">{recordData?.stockInRo?.totalAmount}</span>
              </div>
            </Card>
            <Card
              title={
                <div className="flex justify-between">
                  <span>商品明细</span>
                  <PermissionComponent permission={'inWarehousePrintTag'}>
                    <Button
                      type={'primary'}
                      size="small"
                      fill={'outline'}
                      onClick={() => {
                        const goods =
                          recordData?.stockInDetailRoList?.map((item) => ({
                            itemSn: item.itemSn,
                            number: item.preAmount,
                          })) ?? [];
                        if (goods.length === 0) {
                          Taro.showToast({ title: '无商品可打印', icon: 'none' });
                          return;
                        }
                        Taro.navigateTo({
                          url: `/packages/print/goods/index`,
                          success(res) {
                            res.eventChannel.emit('goods', {
                              goods,
                            });
                          },
                        });
                      }}
                    >
                      打印标签
                    </Button>
                  </PermissionComponent>
                </div>
              }
            >
              {recordData?.stockInDetailRoList?.map((item, index) => (
                <div>
                  <div
                    className={`flex justify-start ${
                      index == 0 ? 'pb-[24px] pt-[8px]' : 'py-[24px]'
                    }`}
                  >
                    <div>
                      <Image
                        src={item.images?.[0] ?? notPic}
                        className="rounded bg-[#f5f5f5]"
                        style={{ width: '44px', height: '44px' }}
                      />
                    </div>
                    <div className="px-[20px] flex flex-col flex-1">
                      <div className=" text-[32px] font-medium text-[#111111]">{item.itemName}</div>
                      <div className=" py-[12px] text-[24px] text-[#666666] flex">
                        <ItemsWithDivider
                          items={[
                            item?.itemSn,
                            item?.brandName,
                            item?.categoryName,
                            item?.unitName,
                          ]}
                        />
                      </div>
                      <div className="flex grow text-[24px] text-[#666666] pt-[8px] justify-between">
                        <div>计划入库 {item.preAmount}</div>
                        <div className="flex">已入库 {item.realAmount}</div>
                        <div>
                          <CodeWithDivider
                            key={item.id}
                            title="库位"
                            items={item.code?.split(',')}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  {index + 1 < recordData?.stockInDetailRoList?.length! && <Divider />}
                </div>
              ))}
            </Card>
          </>
        )}
        {currentTabKey === '2' && (
          <div className="mt-[12px]">
            {isEmpty(recordInputData?.stockInBatchRoList) ? (
              <div className="flex flex-col items-center justify-center min-h-[60vh]">
                <Image src={empty} style={{ width: '80px', height: '80px' }} />
                <span className="text-[24px] text-black/60">暂无数据......</span>
              </div>
            ) : (
              recordInputData?.stockInBatchRoList?.map((item, index) => (
                <div className="mx-[28px] mb-[24px] ">
                  <div className="bg-white rounded-t-lg  p-[28px] relative">
                    <div className="flex justify-between items-center">
                      <div className="flex gap-[16px]">
                        <Tag type="success">入库</Tag>
                        <span className="text-[28px] text-black/60">
                          {item.stockInTime} {item.stockInPerson}
                        </span>
                      </div>
                      {item.state === 0 ? (
                        <div className="bg-[url('./assets/unsuccessful.png')] w-[140px] h-[140px] bg-cover absolute top-0 right-0 " />
                      ) : (
                        <PermissionComponent permission={'inWarehouseDelete'}>
                          <div
                            className="text-[32px] text-[#F83431]"
                            onClick={() => {
                              Dialog.open('confirm', {
                                title: '确认操作',
                                lockScroll: true,
                                content: '是否确认作废？',
                                onConfirm: () => {
                                  handleWithdrawInput({
                                    id: item.id,
                                    stockInId: item.stockInId,
                                    origBillNo: item.origBillNo,
                                  });
                                },
                                onCancel: () => {
                                  Dialog.close('confirm');
                                },
                              });
                            }}
                          >
                            作废
                          </div>
                        </PermissionComponent>
                      )}
                    </div>
                    {item.cancelTime && (
                      <div className="flex items-center pt-[16px]  gap-[16px]">
                        <Tag type="default">作废</Tag>
                        <span className="text-[28px] text-black/60">
                          {item.cancelTime} {item.cancelPerson}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="mt-[1px] bg-white rounded-b-lg">
                    {item?.stockInBatchDetailRoList?.map((items, index) => (
                      <div className="px-[28px]">
                        <div className="flex justify-start py-[24px]">
                          <div>
                            <Image
                              src={items.images?.[0] ?? notPic}
                              className="rounded bg-[#f5f5f5]"
                              style={{ width: '44px', height: '44px' }}
                            />
                          </div>
                          <div className="pl-[20px] flex flex-col flex-1">
                            <div className=" text-[32px] font-medium text-[#111111]">
                              {items.itemName}
                            </div>
                            <div className=" py-[12px] text-[24px] text-[#666666] flex">
                              <ItemsWithDivider
                                items={[
                                  items?.itemSn,
                                  items?.brandName,
                                  items?.categoryName,
                                  items?.unitName,
                                ]}
                              />
                            </div>
                            <div className="flex grow text-[24px] text-[#666666] pt-[8px] justify-between">
                              <div>本次入库 {items.realAmount}</div>
                              <div>
                                <CodeWithDivider
                                  key={items.id}
                                  title="库位"
                                  items={items.code?.split(',')}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        {index + 1 < item?.stockInBatchDetailRoList?.length! && <Divider />}
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
      <Dialog id="confirm" lockScroll={true} style={{ zIndex: 2000 }} />
      <SafeArea position="bottom" />
    </>
  );
}
