import '@/assets/lightPage.scss';
import { warehouseList } from '@/components/ChooseWarehouse/services';
import CustomSearchBar from '@/components/CustomSearchBar';
import MenuWrap from '@/components/MenuWrap';
import FunPagination from '@/components/Pagination/FunPagination';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import usePermissionStore from '@/pages/splash/permissionStore';
import { Button, Divider, Menu, Tag } from '@nutui/nutui-react-taro';
import { navigateTo, useDidShow, useLoad } from '@tarojs/taro';
import _, { isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { queryInputPutPagePost } from '../services';
import { InPutStatus, inPutStatusMenu, inPutStatusOptions } from './types/InPutStatus';
import { InputBillTypeStatusMenu } from './types/InputBillTypeStatus';
import { InputPostEntity } from './types/input.post.entity';
import { QueryPostListRequest } from './types/query.post.list.request';

export default function Index() {
  const { hasPermission } = usePermissionStore(
    useShallow((store) => ({
      hasPermission: store.hasPermission,
    })),
  );
  const [wareHouseOptions, setWareHouseOptions] = useState<any>([]);
  const itemList = [
    { key: 'origBillNo', name: '业务单号', scanShow: false },
    { key: 'bizBillNo', name: '入库单号', scanShow: false },
    { key: 'keyword', name: '商品信息', scanShow: true, placeholder: '商品名称/编码' },
  ];
  // 通知时间排序
  const [orderType, setOrderType] = useState<(string | number)[]>([1]);
  const [params, setParams] = useState<any>({ orderParam: 'createTime' });
  const [stateList, setStateList] = useState<string[] | undefined>(undefined);
  const [billTypeList, setBillTypeList] = useState<string[] | undefined>(undefined);
  const [warehouseIdList, setWarehouseIdList] = useState<string[] | undefined>(undefined);
  const [onLoadFlag, setOnLoadFlag] = useState<boolean>(false);
  useLoad(async () => {
    console.log('Page loaded.');
    const { warehouseSimpleRoList } = await warehouseList({});
    if (!isEmpty(warehouseSimpleRoList)) {
      setWareHouseOptions(
        warehouseSimpleRoList?.map((s) => ({ text: s.warehouseName, value: s.id })),
      );
    }
  });

  useDidShow(() => {
    if (onLoadFlag) {
      setParams((prevData) => ({ ...prevData, time: _.now() }));
    }
  });

  //搜索条件
  const setInputValue = (param) => {
    if (!isEmpty(param)) {
      setParams((prevData) => ({ ...prevData, ...param }));
    }
  };

  useEffect(() => {
    if (stateList !== undefined || billTypeList !== undefined || warehouseIdList !== undefined) {
      setParams((prevData) => ({
        ...prevData,
        stateList,
        billTypeList,
        warehouseIdList,
      }));
    }
  }, [stateList, billTypeList, warehouseIdList]);

  const fetchData = (param?: QueryPostListRequest) => {
    return queryInputPutPagePost({ pageSize: 10, ...param }).then((result) => result?.data ?? []);
  };

  const renderItem = (record: InputPostEntity, index: number) => (
    <div
      className="mt-[24px] p-[28px] bg-white mx-[28px] rounded-xl"
      onClick={() => {
        if (hasPermission('inWarehouseLog')) {
          navigateTo({
            url:
              '/packages/stocks/input/detail/index?stockInId=' +
              record.id +
              '&warehouseId=' +
              record.warehouseId,
          });
          setOnLoadFlag(true);
        }
      }}
    >
      <div className="flex justify-between pb-[20px]">
        <div className="text-[32px] font-medium">{record.warehouseName}</div>
        <div>
          <Tag type={inPutStatusOptions[record.state!]?.status}>
            {inPutStatusOptions[record.state!]?.text}
          </Tag>
        </div>
      </div>
      <div style={{ marginRight: '-28px', marginLeft: '-28px' }}>
        <Divider />
      </div>
      <div className="pt-[20px] text-[28px] text-[#666666] flex">
        <span className="w-[112px]">发货方</span>
        <span className="pl-[24px]">{record.promoter}</span>
      </div>
      <div className="pt-[12px] text-[28px] text-[#666666] flex justify-between">
        <div className="flex">
          <span className="w-[112px]">入库单号</span>
          <span className="pl-[24px]">{record.bizBillNo}</span>
        </div>
        <div className="text-black/90 flex items-center">
          <span className="text-[24px] pr-[4px]">x</span>
          <span className="text-[32px]">{record.totalAmount}</span>
        </div>
      </div>
      <div className="pt-[12px] text-[28px] text-[#666666] flex">
        <span className="w-[112px]">业务单号</span>
        <span className="pl-[24px]">{record.origBillNo}</span>
      </div>
      <div
        className={`pt-[12px] text-[28px] text-[#666666] flex ${
          (InPutStatus.AUDITING == record.state || InPutStatus.TO_ARRIVAL == record.state) &&
          'justify-between items-center'
        }`}
      >
        <div className="flex">
          <span className="w-[112px]">通知时间</span>
          <span className="pl-[24px]">{record.createTime}</span>
        </div>
        {(InPutStatus.AUDITING == record.state || InPutStatus.TO_ARRIVAL == record.state) && (
          <div className="text-black/90">
            <PermissionComponent permission={'inWarehouse'}>
              <Button
                onClick={(e) => {
                  navigateTo({
                    url: '/packages/stocks/input/operation/index?stockInId=' + record.id,
                  });
                  setOnLoadFlag(true);
                  e.stopPropagation();
                }}
              >
                入库
              </Button>
            </PermissionComponent>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="h-screen flex flex-col">
      <div className="px-[28px] pt-[16px]">
        <CustomSearchBar
          itemList={itemList}
          defaultItem={{
            key: 'origBillNo',
            name: '业务单号',
            scanShow: false,
          }}
          inputValue={setInputValue}
          isShowAdd={false}
        />
      </div>
      <div className="custom-menu pt-[24px]">
        <MenuWrap
          checkbox={{
            options: [
              {
                label: '通知时间',
                value: 1,
              },
            ],
            value: orderType,
            onChange: (e) => {
              setOrderType(e);
              setParams((prevData) => ({
                ...prevData,
                orderType: e?.[0] ?? '2',
              }));
            },
          }}
          menu={
            <Menu>
              <Menu.Item
                title="入库状态"
                options={inPutStatusMenu}
                defaultValue={''}
                onChange={(e) => {
                  if (e.value === '') {
                    setStateList([]);
                  } else {
                    setStateList([e.value]);
                  }
                }}
              ></Menu.Item>
              <Menu.Item
                title="入库类型"
                options={InputBillTypeStatusMenu}
                defaultValue={''}
                onChange={(e) => {
                  if (e.value === '') {
                    setBillTypeList([]);
                  } else {
                    setBillTypeList([e.value]);
                  }
                }}
              ></Menu.Item>
              <Menu.Item
                title="入库仓库"
                defaultValue={''}
                options={[{ text: '全部', value: '' }, ...wareHouseOptions]}
                onChange={(e) => {
                  if (e.value === '') {
                    setWarehouseIdList([]);
                  } else {
                    setWarehouseIdList([e.value]);
                  }
                }}
              ></Menu.Item>
            </Menu>
          }
        />
      </div>
      <div className="flex-1 min-h-0">
        <FunPagination fetchData={fetchData} params={params} renderItem={renderItem} />
      </div>
    </div>
  );
}
