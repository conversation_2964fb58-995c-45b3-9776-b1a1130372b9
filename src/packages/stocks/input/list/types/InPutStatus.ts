export enum InPutStatus {
  DRAFT = 0,
  AUDITING = 1,
  CONFIRM = 2,
  TO_ARRIVAL = 3,
}

export enum InPutStatusName {
  ALL = '全部',
  DRAFT = '已取消',
  AUDITING = '待入库',
  CONFIRM = '已入库',
  TO_ARRIVAL = '部分入库',
}
export const inPutStatusOptions = {
  [InPutStatus.DRAFT]: { text: InPutStatusName.DRAFT, status: 'default', bg: '#00000008' },
  [InPutStatus.AUDITING]: { text: InPutStatusName.AUDITING, status: 'primary', bg: '#FFF4F4' },
  [InPutStatus.CONFIRM]: { text: InPutStatusName.CONFIRM, status: 'success', bg: '#EAF9EC' },
  [InPutStatus.TO_ARRIVAL]: { text: InPutStatusName.TO_ARRIVAL, status: 'primary', bg: '#FFF4F4' },
};

export const inPutStatusMenu = [
  { text: InPutStatusName.ALL, value: '' },
  { text: InPutStatusName.DRAFT, value: InPutStatus.DRAFT },
  { text: InPutStatusName.AUDITING, value: InPutStatus.AUDITING },
  { text: InPutStatusName.CONFIRM, value: InPutStatus.CONFIRM },
  { text: InPutStatusName.TO_ARRIVAL, value: InPutStatus.TO_ARRIVAL },
];
