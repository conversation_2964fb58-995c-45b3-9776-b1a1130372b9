import notPic from '@/assets/not_pic.png';
import scan_add from '@/assets/scan_add.svg';
import CodeWithDivider from '@/components/CodeWithDivider';
import CustomNavBar from '@/components/CustomNavBar';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import RadioButtonGroup from '@/components/RadioButtonGroup';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import {
  Button,
  Divider,
  Drag,
  Image,
  Input,
  InputNumber,
  SafeArea,
} from '@nutui/nutui-react-taro';
import Taro, { navigateBack, useLoad } from '@tarojs/taro';
import { add, isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { OutPutDetailPostEntity } from '../detail/types/output.detail.post.entity';
import { confirmOutPutPost, queryOutPutDetailPost } from '../services';
import { DistributionModeStatus, distributionModeStatusMenu } from './types/DistributionModeStatus';
import { scanCode } from '@/utils/scanCode';

export default function Index() {
  const [recordData, setRecordData] = useState<OutPutDetailPostEntity>({});
  const [newrecordData, setNewRecordData] = useState<OutPutDetailPostEntity>({});
  const [distributionMode, setDistributionMode] = useState<DistributionModeStatus>();
  const [logisticsCompanyName, setLogisticsCompanyName] = useState<string>();
  const [logisticsNo, setlogisticsNo] = useState<string>();
  useLoad(async ({ stockOutId }) => {
    const data = await queryOutPutDetailPost({ stockOutId });
    if (data != null) {
      data?.stockOutDetailRoList?.forEach((item) => (item.remainAmount = 0));
    }
    setRecordData(data);
    setNewRecordData(data);
    setDistributionMode(data?.stockOutRo?.distributionMode);
    setLogisticsCompanyName(data?.stockOutRo?.logisticsCompanyName);
    setlogisticsNo(data?.stockOutRo?.logisticsNo);
  });

  useEffect(() => {
    setNewRecordData({
      ...newrecordData,
      stockOutRo: {
        ...recordData?.stockOutRo,
        distributionMode,
        logisticsCompanyName,
        logisticsNo,
      },
    });
  }, [distributionMode, logisticsCompanyName, logisticsNo]);

  const handelOutPut = async () => {
    const sum = newrecordData?.stockOutDetailRoList
      ?.map((s) => s.remainAmount as number)
      ?.reduce(
        (accumulator: number, currentValue: number) =>
          add(Number(accumulator), Number(currentValue)),
        0,
      );
    if (sum === 0) {
      Taro.showToast({ title: '请至少出库一个商品！', icon: 'none' });
      return;
    }
    const data = await confirmOutPutPost({
      ...newrecordData.stockOutRo,
      stockOutDetailCmdList: newrecordData?.stockOutDetailRoList,
    });
    if (data) {
      navigateBack();
    }
  };

  const handleAddItem = async (itemId) => {
    const filterData = recordData?.stockOutDetailRoList?.find((s) => s.itemId == itemId);
    if (!isEmpty(filterData)) {
      let flag = true;
      const newDetatl = recordData?.stockOutDetailRoList?.map((s) => {
        if (s.itemId === itemId) {
          if (Number(s.remainAmount ?? 0) + 1 > (s.preAmount ?? 0) - (s.realAmount ?? 0)) {
            Taro.showToast({ title: '已达到最大出库数量', icon: 'none' });
            flag = false;
          }
          return { ...s, remainAmount: Number(s.remainAmount ?? 0) + 1 };
        }
        return s;
      });
      if (flag) {
        setRecordData((reData) => ({ ...reData, stockOutDetailRoList: newDetatl }));
        setNewRecordData((reData) => ({ ...reData, stockOutDetailRoList: newDetatl }));
      }
    } else {
      Taro.showToast({ title: '该商品不在出库列表中！', icon: 'none' });
      return;
    }
  };
  return (
    <div className="h-screen flex flex-col">
      <div className="flex-1 min-h-0 overflow-y-scroll">
        <CustomNavBar title="部分出库" />
        <div className="pt-[16px]">
          <div className="bg-white mx-[28px] rounded-xl">
            {recordData?.stockOutDetailRoList?.map((item, index) => (
              <div>
                <div className="flex justify-start p-[24px]">
                  <div>
                    <Image
                      src={item.images?.[0] ?? notPic}
                      className="rounded bg-[#f5f5f5]"
                      width={'44px'}
                      height={'44px'}
                    />
                  </div>
                  <div className="px-[20px] flex flex-col flex-1">
                    <div className="text-[32px] font-medium text-[#111111] flex justify-between">
                      <span>{item.itemName}</span>
                    </div>
                    <div className="py-[12px] text-[24px] text-[#666666] flex">
                      <ItemsWithDivider
                        items={[item?.itemSn, item?.brandName, item?.categoryName, item?.unitName]}
                      />
                    </div>
                    <div className="flex flex-1 justify-between text-[24px] text-[#666666] pt-[8px] ">
                      <div className="flex">
                        <span>计划出库</span>
                        <span>{item.preAmount}</span>
                      </div>
                      <div className="flex">已出库 {item.realAmount}</div>
                      <div>
                        <CodeWithDivider title="库位" items={item.code?.split(',')} />
                      </div>
                    </div>
                    <div className="flex text-[24px] text-[#666666] pt-[24px] justify-between items-center">
                      <div className="text-black/90">本次出库</div>
                      <div>
                        <InputNumber
                          allowEmpty={true}
                          max={(item.preAmount ?? 0) - (item.realAmount ?? 0)}
                          min={0}
                          value={item.remainAmount}
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                          onChange={(e: number) => {
                            let value: number = 0;
                            if (e > (item.preAmount ?? 0) - (item.realAmount ?? 0)) {
                              value = (item.preAmount ?? 0) - (item.realAmount ?? 0);
                            } else if (e < 0) {
                              value = 0;
                            } else {
                              value = e;
                            }
                            console.log(e);

                            item.remainAmount = parseInt(value.toString(), 10);
                            // 更新状态以触发重新渲染
                            setNewRecordData({
                              ...newrecordData,
                              stockOutDetailRoList: [...recordData.stockOutDetailRoList!],
                            });
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                {index + 1 < recordData?.stockOutDetailRoList?.length! && <Divider />}
              </div>
            ))}
          </div>
          {recordData?.stockOutRo?.distributionMode && (
            <div className="rounded-xl  bg-white p-[28px] my-[24px] text-black/90 text-[32px] mx-[28px]">
              <div className=" font-medium text-[#111111] pb-[8px]">配送信息</div>
              <div className="pt-[24px] ">
                <div className="text-[28px] text-black/45">配送地址</div>
                <div className="pt-[12px]">{recordData?.stockOutRo.deliveryAddress}</div>
              </div>
              <div className="py-[24px]">
                <RadioButtonGroup
                  options={distributionModeStatusMenu}
                  value={distributionMode}
                  onChange={(e: DistributionModeStatus) => {
                    setDistributionMode(e);
                  }}
                ></RadioButtonGroup>
              </div>
              <div className="text-[32px]">
                <div className="flex justify-between items-center">
                  <span>物流名称</span>
                  <Input
                    placeholder="请输入"
                    name="logisticsCompanyName"
                    align="right"
                    value={logisticsCompanyName}
                    onChange={setLogisticsCompanyName}
                  />
                </div>
                <Divider />
                <div className="flex justify-between items-center">
                  <span>物流单号</span>
                  <Input
                    placeholder="请输入"
                    name="logisticsNo"
                    align="right"
                    value={logisticsNo}
                    onChange={setlogisticsNo}
                  />
                </div>
              </div>
            </div>
          )}
          <Drag style={{ bottom: '150px', right: '14px' }}>
            <div
              className="shadow shadow-transparent"
              onClick={() => {
                //扫码
                scanCode().then(result => {
                  if (result) {
                    handleAddItem(result);
                  }
                })
              }}
            >
              <Image src={scan_add} style={{ width: '44px', height: '44px' }} />
            </div>
          </Drag>
        </div>
      </div>

      <div className="bg-white px-[28px] py-[24px]">
        <PermissionComponent permission={'outWarehouse'}>
          <Button block type="primary" onClick={handelOutPut}>
            出库
          </Button>
        </PermissionComponent>
        <SafeArea position="bottom" />
      </div>
    </div>
  );
}
