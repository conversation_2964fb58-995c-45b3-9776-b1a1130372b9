import empty from '@/assets/empty.png';
import notPic from '@/assets/not_pic.png';
import Card from '@/components/Card';
import CodeWithDivider from '@/components/CodeWithDivider';
import CustomNavBar from '@/components/CustomNavBar';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import { Dialog, Divider, Ellipsis, SafeArea, Tabs, Tag } from '@nutui/nutui-react-taro';
import { Image } from '@tarojs/components';
import { useLoad } from '@tarojs/taro';
import { isEmpty } from 'lodash';
import { useState } from 'react';
import { outPutStatusOptions } from '../list/types/OutPutStatus';
import { OutputPostWithdrawRequest } from '../list/types/output.post.withdraw.request';
import { OutputStockBatchList } from '../list/types/output.stock.batch.list';
import { queryBatchDetailPost, queryOutPutDetailPost, withdrawOutputPost } from '../services';
import { OutPutDetailPostEntity } from './types/output.detail.post.entity';
import { OutPutDetailRequest } from './types/output.detail.request';

export default function Index() {
  useLoad(({ stockOutId, warehouseId }) => {
    loadData({ stockOutId, warehouseId });
    setParamData({ stockOutId, warehouseId });
  });
  const [paramData, setParamData] = useState({});
  const [recordData, setRecordData] = useState<OutPutDetailPostEntity>({});
  const [recordOutData, setRecordOutData] = useState<OutputStockBatchList>({});

  const [currentTabKey, setCurrentTabKey] = useState<'1' | '2'>('1');

  const loadData = (params: OutPutDetailRequest) => {
    handleQueryDetail(params);
    handleBatchDetailQuery(params);
  };
  const handleQueryDetail = async (params: OutPutDetailRequest) => {
    const data = await queryOutPutDetailPost(params);
    setRecordData(data);
  };
  const handleBatchDetailQuery = async (params: OutPutDetailRequest) => {
    const outData = await queryBatchDetailPost(params);
    setRecordOutData(outData);
  };
  //作废
  const handleWithdrawOutput = async (values: OutputPostWithdrawRequest) => {
    const data = await withdrawOutputPost(values);
    if (data) {
      Dialog.close('confirm');
      //刷新
      loadData(paramData);
    }
  };
  return (
    <div>
      <CustomNavBar title="出库详情" />
      <div className="tabs">
        <Tabs
          align="left"
          value={currentTabKey}
          onChange={(v) => setCurrentTabKey(v as '1' | '2')}
          style={{
            '--nutui-tabs-tabpane-padding': '0px',
            '--nutui-tabs-titles-background-color': 'none',
          }}
          autoHeight
        >
          <Tabs.TabPane title="出库详情" value="1"></Tabs.TabPane>
          <Tabs.TabPane title="出库记录" value="2"></Tabs.TabPane>
        </Tabs>

        {currentTabKey === '1' && (
          <>
            <Card title={recordData?.stockOutRo?.warehouseName} className="mt-[12px]">
              <div className="flex items-center pb-[24px]">
                <span className="text-[28px] text-black/60 pr-[16px]">
                  {recordData?.stockOutRo?.bizBillNo}
                </span>
                <Tag type={outPutStatusOptions[recordData?.stockOutRo?.state!]?.status}>
                  {outPutStatusOptions[recordData?.stockOutRo?.state!]?.text}
                </Tag>
              </div>
              <div style={{ marginRight: '-28px', marginLeft: '-28px' }}>
                <Divider />
              </div>
              <div className="pt-[24px] text-[28px]  flex">
                <span className="text-black/60">收货方：</span>
                <span className="text-black/90">{recordData?.stockOutRo?.customer}</span>
              </div>
              <div className="pt-[24px] text-[28px]  flex">
                <span className="text-black/60">业务单号：</span>
                <span className="text-black/90">{recordData?.stockOutRo?.origBillNo}</span>
              </div>
              <div className="pt-[24px] text-[28px]  flex">
                <span className="text-black/60">出库类型：</span>
                <span className="text-black/90">{recordData?.stockOutRo?.billTypeDesc}</span>
              </div>
              <div className="pt-[24px] text-[28px]  flex">
                <span className="text-black/60">通知出库：</span>
                <span className="text-black/90">{recordData?.stockOutRo?.createTime}</span>
              </div>
              <div className="pt-[24px] text-[28px]  flex">
                <span className="text-black/60">完成出库：</span>
                <span className="text-black/90">{recordData?.stockOutRo?.realOutTime}</span>
              </div>
              <div className="pt-[24px] text-[28px]  flex">
                <span className="text-black/60">出库数量：</span>
                <span className="text-black/90">{recordData?.stockOutRo?.totalAmount}</span>
              </div>
            </Card>
            <Card title="商品明细" className="mx-[0px]">
              {recordData?.stockOutDetailRoList?.map((item, index) => (
                <div>
                  <div
                    className={`flex justify-start ${
                      index == 0 ? 'pb-[24px] pt-[8px]' : 'py-[24px]'
                    }`}
                  >
                    <div>
                      <Image
                        src={item.images?.[0] ?? notPic}
                        className="rounded bg-[#f5f5f5]"
                        style={{ width: '44px', height: '44px' }}
                      />
                    </div>
                    <div className="px-[20px] flex flex-col flex-1">
                      <div className=" text-[32px] font-medium text-[#111111]">{item.itemName}</div>
                      <div className=" py-[12px] text-[24px] text-[#666666] flex">
                        <ItemsWithDivider
                          items={[
                            item?.itemSn,
                            item?.brandName,
                            item?.categoryName,
                            item?.unitName,
                          ]}
                        />
                      </div>
                      <div className="flex grow text-[24px] text-[#666666] pt-[8px] justify-between">
                        <div className="flex">
                          <span>计划出库</span>
                          <span>{item.preAmount}</span>
                        </div>
                        <div className="flex">已出库 {item.realAmount}</div>
                        <div>
                          <CodeWithDivider title="库位" items={item.code?.split(',')} />
                        </div>
                      </div>
                    </div>
                  </div>
                  {index + 1 < recordData?.stockOutDetailRoList?.length! && <Divider />}
                </div>
              ))}
            </Card>
            <Card title="配送信息" className="mx-[0px] text-[32px]">
              <div className="pt-[8px] flex items-center">
                <span>{recordData?.stockOutRo?.distributionModeDesc}</span>
                {recordData?.stockOutRo?.logisticsCompanyName && <Divider direction="vertical" />}
                <span>{recordData?.stockOutRo?.logisticsCompanyName}</span>
              </div>
              <div className="pt-[24px] ">
                <div className="text-[28px] text-black/45">物流单号</div>
                <div className="pt-[12px]">{recordData?.stockOutRo?.logisticsNo}</div>
              </div>
              <div className="pt-[24px] ">
                <div className="text-[28px] text-black/45">配送地址</div>
                <div className="pt-[12px]">{recordData?.stockOutRo?.deliveryAddress}</div>
              </div>
            </Card>
          </>
        )}

        {currentTabKey === '2' && (
          <>
            <div className="mt-[12px]">
              {isEmpty(recordOutData?.stockOutBatchRoList) ? (
                <div className="flex flex-col items-center justify-center min-h-[60vh]">
                  <Image src={empty} style={{ width: '80px', height: '80px' }} />
                  <span className="text-[24px] text-black/60">暂无数据......</span>
                </div>
              ) : (
                recordOutData?.stockOutBatchRoList?.map((item, index) => (
                  <div className="mx-[28px] mb-[24px]">
                    <div className="bg-white rounded-t-lg  p-[28px] relative">
                      <div className="flex justify-between items-center">
                        <div className="flex gap-[16px]">
                          <Tag type="success">出库</Tag>
                          <span className="text-[28px] text-black/60">
                            {item.stockOutTime} {item.stockOutPerson}
                          </span>
                        </div>
                        {item.state === 0 ? (
                          <div className="bg-[url('./assets/unsuccessful.png')] w-[140px] h-[140px] bg-cover absolute top-0 right-0 " />
                        ) : (
                          <PermissionComponent permission={'outWarehouseDelete'}>
                            <div
                              className="text-[32px] text-[#F83431]"
                              onClick={() => {
                                Dialog.open('confirm', {
                                  title: '确认操作',
                                  content: '是否确认作废？',
                                  onConfirm: () => {
                                    handleWithdrawOutput({
                                      id: item.id,
                                      stockOutId: item.stockOutId,
                                      origBillNo: item.origBillNo,
                                    });
                                  },
                                  onCancel: () => {
                                    Dialog.close('confirm');
                                  },
                                });
                              }}
                            >
                              作废
                            </div>
                          </PermissionComponent>
                        )}
                      </div>
                      {item.cancelTime && (
                        <div className="flex items-center pt-[16px]  gap-[16px]">
                          <Tag type="default">作废</Tag>
                          <span className="text-[28px] text-black/60">
                            {item.cancelTime} {item.cancelPerson}
                          </span>
                        </div>
                      )}
                      <div className="pt-[24px] text-[24px] text-black/45 flex flex-1">
                        <div>配送信息：</div>
                        <div className="flex">
                          <ItemsWithDivider
                            items={[
                              item?.distributionModeDesc,
                              item?.logisticsCompanyName,
                              item?.logisticsNo,
                            ]}
                          />
                        </div>
                      </div>
                      <div className="pt-[8px] text-[24px] text-black/45 flex flex-1">
                        <div>配送地址：</div>
                        <div>
                          <Ellipsis content={item.deliveryAddress} />
                        </div>
                      </div>
                    </div>
                    <div className="mt-[1px] rounded-b-lg bg-white">
                      {item?.stockOutBatchDetailRoList?.map((items, index) => (
                        <div className="px-[28px]">
                          <div className="flex justify-start py-[24px]">
                            <div>
                              <Image
                                src={items.images?.[0] ?? notPic}
                                className="rounded bg-[#f5f5f5]"
                                style={{ width: '44px', height: '44px' }}
                              />
                            </div>
                            <div className="pl-[20px] flex flex-col flex-1">
                              <div className=" text-[32px] font-medium text-[#111111]">
                                {items.itemName}
                              </div>
                              <div className=" py-[12px] text-[24px] text-[#666666] flex">
                                <ItemsWithDivider
                                  items={[
                                    items?.itemSn,
                                    items?.brandName,
                                    items?.categoryName,
                                    items?.unitName,
                                  ]}
                                />
                              </div>
                              <div className="flex grow text-[24px] text-[#666666] pt-[8px] justify-between">
                                <div>本次出库 {items.realAmount}</div>
                                <div>
                                  <CodeWithDivider title="库位" items={items.code?.split(',')} />
                                </div>
                              </div>
                            </div>
                          </div>
                          {index + 1 < item?.stockOutBatchDetailRoList?.length! && <Divider />}
                        </div>
                      ))}
                    </div>
                  </div>
                ))
              )}
            </div>
          </>
        )}
      </div>
      <Dialog id="confirm" />
      <SafeArea position="bottom" />
    </div>
  );
}
