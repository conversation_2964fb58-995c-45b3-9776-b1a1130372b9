/**
 * 退款方式-现款&挂账
 */
export enum RefundTypeEnum {
  /** 现款 */
  Cash = 0,
  /** 挂账 */
  Account = 10,
}
export const RefundTypOptions = [
  { label: '现款', value: RefundTypeEnum.Cash },
  { label: '挂账', value: RefundTypeEnum.Account },
  // { label: '原路退', value: 20 },
];

export interface ReturnsModifyRefund {
  refundType: RefundTypeEnum;
  /** 订单ID */
  orderId?: string;
  /** 业务单号 */
  orderNo?: string;
  refundDetails?: ReturnsModifyRefundDetail[];
  remark?: string;
}
export interface ReturnsModifyRefundDetail {
  key?: string;
  /**
   * 退款金额(单位元)
   */
  refundAmount?: number;
  /**
   * 退款账户名称
   */
  accountName?: string;
  /**
   * 退款账户
   */
  accountId?: string;
}
