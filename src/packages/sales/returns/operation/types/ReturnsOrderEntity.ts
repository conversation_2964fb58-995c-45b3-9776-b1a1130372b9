import { ItemType } from "./ReturnsGoodsEntity";

export interface ReturnsOrderEntity extends ItemType {
  /** ID */
  returnsId: string;
  /** ID */
  id: string;
  /** 销售单ID */
  orderId?: string;
  /** 销售门店 */
  storeId?: number;
  /** 销售单号 */
  orderNo?: string;
  /** 销售时间 */
  orderFinishTime?: string;
  /** 商品ID */
  itemId: string;
  /** 商品名称 */
  itemName: string;
  /** 商品编码 */
  itemSn: string;
  /** 门店名称 */
  storeName?: string;
  /** 销售数量 */
  saleNum?: number;
  /** skuId */
  skuId?: string;
  /** oe号 */
  oeNos?: string[];
  /** 品牌Id */
  brandId?: string;
  /** 品牌名称 */
  brandName?: string;
  /** 品牌件号 */
  brandPartNos?: string[];
  /** 商品图片 */
  images?: string[];
  /** skuName */
  skuName?: string;
  /** 商品ID */
  unitId?: string;
  /** 商品单位 */
  unitName?: string;
  /** 已退数量 */
  hasRefundNum?: number;
  /** 可退数量 */
  refundableNum: number;
  /** 商品实际销售单价（商品实收总额/购买商品的数量） */
  unitAmount: string | number; // 使用字符串来表示 BigDecimal
  /** 三级品类名称 */
  categoryName?: string;
  /** 三级品类Id */
  categoryId?: string;
  /** 发货仓库 */
  warehouseId?: number;
  /** 发货仓库名称 */
  warehouseName?: string;

  /** 退货原因 */
  cause?: string;

  /** 支付方式 */
  payKindName: string;

  /** 支付方式 */
  payKind: number;

  /**
   * 销售单退货
   */
  /** 退货金额 */
  refundAmount?: number;
  /** 退货数量 */
  refundNum?: number;
  costAmount?: number;

  /**
   * 商品退货
   */
  /** 退货价格 */
  price?: number;
  /** 退货数量 */
  number?: number;
}
