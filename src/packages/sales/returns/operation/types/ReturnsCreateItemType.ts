import { RefundTypeEnum } from './RefundTypeEnum';

export interface ReturnsCreateItemType {
  refund: {
    refundType: RefundTypeEnum;
  };
  main: ReturnsCreateMain;
  /** 商品信息 */
  items: ReturnsCreateItem[];
  orderId?: string;
  orderNo?: string;
  extRemark?: string;
}
export interface ReturnsCreateMain {
  /** 退货入库仓ID */
  backWarehouseId: string;
  /** 门店ID */
  storeId: string;
  /** 客户ID */
  cstId: string;
  /** 备注 */
  remark?: string;
}

export interface ReturnsModifyItem {
  id?: string;
  refundNum?: number;
  unitAmount?: string | number;
  cause?: string;
  orderId?: string;
  orderNo?: string;
}
export interface ReturnsAddItem {
  items: ReturnsCreateItem[];
  orderId: string;
  orderNo: string;
}
export interface ReturnsCreateItem {
  /** ID */
  id?: string;
  /** 商品ID */
  itemId?: string;
  /** 退货数量 */
  refundNum?: number;
  /** 退货价格 */
  unitAmount?: string | number;
  /** 原始销售单成本价（单位元） (下面为销售单退货独有) */
  costAmount?: number;
  /** 原始销售数量 */
  saleNum?: number;
  /** 原始业务单 */
  orgOrderNo?: string;
  // 退货原因
  cause?: string;
  payKind?: number;
}

export interface ReturnsItemModifyRefund {
  refundType?: RefundTypeEnum;
  /** 订单ID */
  orderId?: string;
  /** 业务单号 */
  orderNo?: string;
  refundDetails?: ReturnsItemModifyRefundDetail[];
  remark?: string;
}
/**
 * 现款结算时选择的支付信息
 */
export interface ReturnsItemModifyRefundDetail {
  /**
   * 退款金额(单位元)
   */
  refundAmount: number;
  /**
   * 退款账户名称
   */
  accountName: string;
  /**
   * 退款账户
   */
  accountId: string;
}

// 修改订单信息（支持备注，支付方式，返回仓库的修改）
export interface ReturnsItemModifyType {
  refund?: ReturnsItemModifyRefund;
  main?: ReturnsCreateMain;
  /** 商品信息 */
  items?: ReturnsCreateItem[];
  orderId: string;
  orderNo: string;
  remark?: string;
}
