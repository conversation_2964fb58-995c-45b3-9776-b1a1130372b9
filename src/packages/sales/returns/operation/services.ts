import { post } from '@/utils/request';
import { PageResponseDataType } from '../../../../../types/PageResponseDataType';
import { QueryStoreGoodsPageRequest } from '../returnsItemSelect/types/query.store.goods.page.request';
import { AfterSaleOrderRo } from './types/ReturnsAfterSaleDetailEntity';
import { ReturnsAddItem, ReturnsCreateItemType, ReturnsItemModifyType, ReturnsModifyItem } from './types/ReturnsCreateItemType';
import { ReturnsGoodsEntity } from './types/ReturnsGoodsEntity';
import { ReturnsOrderEntity } from './types/ReturnsOrderEntity';
import { ReturnsOrderPageParamsType } from './types/ReturnsOrderPageParamsType';
import { ReturnsOrderType } from './types/ReturnsOrderType';


/**
 * 新建退货单
 * @param params
 * @returns
 */
export const createOrder = async (params: ReturnsCreateItemType) => {
  return post<ReturnsOrderType>('/ipmsaftersale/AfterSaleCmdFacade/createOrder', {
    data: params,
  });
};
/**
 * 修改退货单
 * @param params
 * @returns
 */
export const modifyOrder = async (params: ReturnsItemModifyType) => {
  return post<ReturnsOrderType>('/ipmsaftersale/AfterSaleCmdFacade/modifyOrder', {
    data: params,
  });
};
/**
 * 撤回
 * @param params
 * @returns
 */
export const drawOrder = async (params: ReturnsOrderType) => {
  return post<boolean>('/ipmsaftersale/AfterSaleCmdFacade/drawOrder', {
    data: params,
  });
};
/**
 * 作废
 * @param params
 * @returns
 */
export const cancelOrder = async (params: ReturnsOrderType) => {
  return post<boolean>('/ipmsaftersale/AfterSaleCmdFacade/cancelOrder', {
    data: params,
  });
};
/**
 * 提交
 * @param params
 * @returns
 */
export const submitOrder = async (params: ReturnsOrderType) => {
  return post<boolean>('/ipmsaftersale/AfterSaleCmdFacade/submitOrder', {
    data: params,
  }, true);
};
/**
 * 一键入库
 * @param params
 * @returns
 */
export const directIn = async (params: ReturnsOrderType) => {
  return post<boolean>('/ipmsaftersale/AfterSaleCmdFacade/directIn', {
    data: params,
  });
};
/**
 * 确认退款/确认结算
 * @param params
 * @returns
 */
export const confirmRefund = async (params: ReturnsOrderType) => {
  return post<boolean>('/ipmsaftersale/AfterSaleCmdFacade/confirmRefund', {
    data: params,
  });
};
/**
 * 售后确认
 * @param params
 * @returns
 */
export const confirmOrder = async (params: ReturnsOrderType) => {
  return post<boolean>('/ipmsaftersale/AfterSaleCmdFacade/confirmOrder', {
    data: params,
  });
};
/**
 * 向销售单退货记录添加商品
 * @param params
 * @returns
 */
export const addItem = async (params: ReturnsAddItem) => {
  return post<boolean>('/ipmsaftersale/AfterSaleCmdFacade/addItem', {
    data: params,
  });
};
/**
 * 修改售后单商品数量或价格
 * @param params ReturnsModifyItem
 * @returns
 */
export const modifyItem = async (params: ReturnsModifyItem) => {
  return post<boolean>('/ipmsaftersale/AfterSaleCmdFacade/modifyItem', {
    data: params,
  });
};
/**
 * 删除退货商品
 * @param params ReturnsAddItem
 * @returns
 */
export const deleteItem = async (params: { ids: string[]; orderId: string; orderNo: string }) => {
  return post<boolean>('/ipmsaftersale/AfterSaleCmdFacade/deleteItem', {
    data: params,
  });
};
/**
 * 售后详情
 * @param params
 * @returns
 */
export const getAfterSaleDetail = async (params: ReturnsOrderType) => {
  return post<AfterSaleOrderRo>('/ipmsaftersale/AfterSaleQryFacade/getAfterSaleDetail', {
    data: params,
  });
};

/**
 * 查询门店商品-商品退货
 * @param params
 * @returns
 */
export const queryReturnsGoods = (params: QueryStoreGoodsPageRequest) => {
  return post<PageResponseDataType<ReturnsGoodsEntity>>(`/ipmsconsole/goods/member/query`, {
    data: params,
  });
};

/**
 *  
 * 查询销售单-销售单退货
 * @param params
 * @returns
 */
export const getRefundablePaged = async (params: ReturnsOrderPageParamsType) => {
  return post<PageResponseDataType<ReturnsOrderEntity>>('/ipmsaftersale/AfterSaleQryFacade/getRefundablePaged', {
    data: params,
  });
};
