page, html {
    // background: linear-gradient(180deg, #FFF0F0 0%, #F7F7F7 400px);

    // padding-top: 88px;
    // padding-bottom: 320px;

    .buy-count {
        &::before {
            content: "x";
        }
    }

    .boder-bottom-1 {
        border-bottom: 1px solid #0000001A;
    }

    .boder-bottom-0 {
        border-bottom: none;
    }

    .nut-textarea-textarea {
        height: 160px;

        .textarea-placeholder {
            color: #0000004D
        }
    }

    #returnList {
        .listItem {
            &:not(:last-child) {
                padding-bottom: 24px;
                margin-bottom: 24px;
                border-bottom: solid 1px #0000001A;
            }
        }
    }

}
