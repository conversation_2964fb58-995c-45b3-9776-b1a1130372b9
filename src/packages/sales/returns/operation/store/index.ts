import { uniqueId } from 'lodash';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { ReturnsListItem } from '../../returnsItemSelect/types/ReturnsListItem';
import { getAfterSaleDetail } from '../services';
import { RefundTypeEnum, ReturnsModifyRefundDetail } from '../types/RefundTypeEnum';
import { ReturnsTypeEnum } from '../types/ReturnsTypeEnum';
import { SubmitCheckboxEnum } from '../types/SubmitCheckboxEnum';
import { SubmitCheckboxOptionType } from '../types/SubmitCheckboxOptionType';

export const SubmitCheckboxOptions: SubmitCheckboxOptionType[] = [
  {
    label: '确认结算',
    value: SubmitCheckboxEnum.CONFIRM_REFUND,
    checked: false,
    permission: "orderReturnSettlement"
  },
  {
    label: '一键入库',
    value: SubmitCheckboxEnum.DIRECT_IN,
    checked: false,
    permission: "saleReturnInWareHouse"
  },
];

/**定义状态 */
interface ReturnsOperationState {
  goods: ReturnsListItem[],
  loading: boolean;
  visibleAccount: boolean;
  backWarehouseId: string;
  storeId: string;
  cstId: string;
  orderNo: string;
  orderId: string;
  returnsType: ReturnsTypeEnum;
  refundType: RefundTypeEnum;
  isSupportCredit: boolean;
  refundDetails: ReturnsModifyRefundDetail[];
  remark: string;
  orderStatusName: string;
  totalAmount: number;
  usedAmount: number;
  availableAmount: number;
  accountIndex: number;
  checkboxValues: SubmitCheckboxEnum[];
}

/**定义行为 */
interface ReturnsOperationActions {
  addRefundDetail: () => void;
  updateRefundDetail: (index: number, refundDetail: ReturnsModifyRefundDetail) => void;
  removeRefundDetail: (index: number) => void;
  updateChecked: (value: SubmitCheckboxEnum) => void;
  getAfterSaleDetail: () => Promise<void>;
  setState: <K extends keyof ReturnsOperationState>(key: K, value: ReturnsOperationState[K]) => void;
  reset: () => boolean;
}

/**定义初始值 */
const initialState: ReturnsOperationState = {
  loading: false,
  isSupportCredit: false,
  visibleAccount: false,
  backWarehouseId: '',
  storeId: '',
  cstId: '',
  orderId: '',
  orderNo: '',
  refundType: RefundTypeEnum.Cash,
  returnsType: ReturnsTypeEnum.ORDER,
  refundDetails: [],
  remark: '',
  totalAmount: 0,
  usedAmount: 0,
  availableAmount: 0,
  accountIndex: 0,
  orderStatusName: '',
  checkboxValues: [],
  goods: [],
}

// 销售退货
const useSalesReturnsStore = create<ReturnsOperationState & ReturnsOperationActions>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,
    addRefundDetail: () => {
      set((state) => ({ refundDetails: [...state.refundDetails, { key: uniqueId() }] }));
    },
    updateRefundDetail: (index, newDetail) => {
      set((state) => {
        const refundDetails = [...state.refundDetails];
        const pre = refundDetails[index]
        refundDetails.splice(index, 1, { ...pre, ...newDetail });
        return { refundDetails };
      });
    },
    removeRefundDetail: (index) => {
      set((state) => {
        const refundDetails = [...state.refundDetails];
        refundDetails.splice(index, 1);
        return { refundDetails };
      });
    },
    updateChecked: (value) => {
      set((state) => {
        const checkboxValues = [...state.checkboxValues];
        const index = checkboxValues.indexOf(value);
        if (index >= 0) {
          delete checkboxValues[index];
        } else {
          checkboxValues.push(value);
        }
        return { checkboxValues };
      });
    },
    getAfterSaleDetail: async () => {
      const orderId = get().orderId;
      const orderNo = get().orderNo;
      if (orderId && orderNo) {
        const result = await getAfterSaleDetail({ orderId, orderNo });
        if (result) {
          let finishTime;
          if (result?.main) {
            const { orderAmount: totalAmount, tagIds, cstId, backWarehouseId, storeId, remark, orderFinishTime } = result.main;
            let returnsType = ReturnsTypeEnum.ORDER;
            if (tagIds.includes(1)) {
              returnsType = ReturnsTypeEnum.ORDER;
            } else if (tagIds.includes(2)) {
              returnsType = ReturnsTypeEnum.GOODS;
            }
            set({ cstId, storeId, backWarehouseId, totalAmount, remark, returnsType })
            finishTime = orderFinishTime;
          }
          if (result?.refunds) {
            const refundType = result.refunds[0].refundType;
            set({ refundType });
            if (refundType == RefundTypeEnum.Cash) {
              if (result?.refundDetails) {
                set({
                  refundDetails: result.refundDetails.map((t) => ({
                    accountId: t.accountId,
                    refundAmount: t.refundAmount,
                    accountName: t.accountName
                  }))
                });
              }
            }
          }
          if (result?.goods) {
            set({
              goods: result.goods.map(t => ({
                itemName: t.itemName,
                itemSn: t.itemSn,
                brandName: t.brandName,
                categoryName: t.categoryName,
                unitName: t.unitName,
                orgOrderNo: t.orgOrderNo,
                orderFinishTime: finishTime,
                imageUrl: t?.images?.[0],
                payKindName: t?.payKindName,
                unitAmount: t.unitAmount,
                refundNum: t.refundNum,
              }))
            });
          }
          if (result?.status) {
            const { orderStatusName } = result.status;
            set({ orderStatusName });
          }
        }
      }
    },
    setState: (key, value) => { set({ [key]: value }); },
    reset: () => {
      set(initialState);
      return true;
    }
  }))
);
export default useSalesReturnsStore;
