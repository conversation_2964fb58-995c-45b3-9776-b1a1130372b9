import CustTag from '@/components/CustTag';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import { ConfigProvider, Price } from '@nutui/nutui-react-taro';
import { defaultTo, multiply } from 'lodash';
import { ReturnsListItem } from '../../../returnsItemSelect/types/ReturnsListItem';
import { ReturnsTypeEnum } from '../../types/ReturnsTypeEnum';

export default (props: { record: ReturnsListItem; returnsType: ReturnsTypeEnum }) => {
  const { record, returnsType } = props;
  const unitAmount = defaultTo(record.unitAmount, 0);
  const refundNum = defaultTo(record.refundNum, 0);
  const total = multiply(Number(unitAmount), refundNum);

  return (
    <div className="listItem">
      {returnsType == ReturnsTypeEnum.ORDER && (
        <div className="flex justify-between items-center pb-[24px] shadow-sm mb-[24px]">
          <span className="text-main  text-[28px]">{record?.orgOrderNo}</span>
          <span className="text-thirdary text-[24px]">{record?.orderFinishTime}</span>
        </div>
      )}
      <div className="flex">
        <img src={record?.imageUrl} className="rounded mr-[20px] bg-[#f5f5f5] w-[88px] h-[88px]" />
        <div className="flex-1">
          <div>
            <div className="flex justify-start items-center gap-[10px]">
              <span className="text-[32px] text-main">{record.itemName}</span>
              {ReturnsTypeEnum.ORDER === returnsType && <CustTag text={record?.payKindName} />}
            </div>
            <div className="flex text-[24px] text-thirdary mt-[12px] mb-[24px]">
              <ItemsWithDivider
                items={[record?.itemSn, record?.brandName, record?.categoryName, record?.unitName]}
              />
            </div>
          </div>
          <span className="mt-[8px] flex items-center justify-between">
            <span className="flex gap-[40px] text-thirdary text-[32px]">
              <ConfigProvider
                theme={{
                  nutuiColorPrimary: '#00000073',
                  nutuiPriceSymbolMediumSize: '12px',
                  nutuiPriceIntegerMediumSize: '16px',
                  nutuiPriceDecimalMediumSize: '12px',
                  nutuiPriceSymbolPaddingRight: '0px',
                }}
              >
                <Price price={unitAmount} size="normal" thousands />
              </ConfigProvider>
              <span className="text-[32px] buy-count">{refundNum}</span>
            </span>
            <ConfigProvider
              theme={{
                nutuiColorPrimary: '#000000E6',
                nutuiPriceSymbolMediumSize: '12px',
                nutuiPriceIntegerMediumSize: '16px',
                nutuiPriceDecimalMediumSize: '12px',
                nutuiPriceSymbolPaddingRight: '0px',
              }}
            >
              <Price price={total} size="normal" thousands />
            </ConfigProvider>
          </span>
        </div>
      </div>
    </div>
  );
};
