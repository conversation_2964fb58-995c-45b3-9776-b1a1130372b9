import iconCheckboxDef from '@/assets/icons/icon_checkbox_def.png';
import iconCheckboxSel from '@/assets/icons/icon_checkbox_sel.png';
import iconEdit from '@/assets/icons/icon_edit.svg';
import iconRemove from '@/assets/icons/icon_remove.png';
import iconReturnsGoods from '@/assets/icons/icon_returns_goods.svg';
import iconReturnsGoodsDisabled from '@/assets/icons/icon_returns_goods_disabled.svg';
import iconReturnsOrder from '@/assets/icons/icon_returns_order.svg';
import iconReturnsOrderDisabled from '@/assets/icons/icon_returns_order_disabled.svg';
import { queryMemberAccountPage } from '@/components/AccountSelectModal/services';
import Card from '@/components/Card';
import ChooseStoreAndWarehouse from '@/components/ChooseStoreAndWarehouse';
import CstCard from '@/components/CstCard';
import CustomNavBar from '@/components/CustomNavBar';
import CustomPicker from '@/components/CustomPicker';
import { SubmitCheckboxEnum } from '@/packages/sales/returns/operation/types/SubmitCheckboxEnum';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import usePermissionStore from '@/pages/splash/permissionStore';
import RouterUtils from '@/utils/RouterUtils';
import { Add, ArrowDown } from '@nutui/icons-react-taro';
import { Button, ConfigProvider, Image, Input, Price, TextArea } from '@nutui/nutui-react-taro';
import Taro, { useDidShow, useLoad, useRouter, useUnload } from '@tarojs/taro';
import { useAsyncEffect } from 'ahooks';
import classnames from 'classnames';
import { defaultTo, find, includes, isEmpty, isEqual, isUndefined, multiply, sumBy } from 'lodash';
import { stringify } from 'qs';
import { useEffect, useState } from 'react';
import { useShallow } from 'zustand/react/shallow';
import ReturnsDetailItem from './components/ReturnsDetailItem';
import './index.scss';
import { confirmRefund, directIn, modifyOrder, submitOrder } from './services';
import useSalesReturnsStore, { SubmitCheckboxOptions } from './store';
import { DefautlOptionType } from './types/DefaultOptionType';
import { RefundTypeEnum, ReturnsModifyRefundDetail } from './types/RefundTypeEnum';
import { ReturnsTypeEnum } from './types/ReturnsTypeEnum';

const GOODS_ICONS_MAP: Record<ReturnsTypeEnum, string> = {
  goods: iconReturnsGoods,
  order: iconReturnsGoodsDisabled,
};
const ORDER_ICONS_MAP: Record<ReturnsTypeEnum, string> = {
  order: iconReturnsOrder,
  goods: iconReturnsOrderDisabled,
};
export default function Index() {
  const router = useRouter();
  const SalesReturnsStore = useSalesReturnsStore();
  const { hasPermission } = usePermissionStore(
    useShallow((store) => ({
      hasPermission: store.hasPermission,
    })),
  );
  useLoad(() => {
    const { orderNo, orderId } = router.params;
    if (orderId && orderNo) {
      SalesReturnsStore.setState('orderId', orderId);
      SalesReturnsStore.setState('orderNo', orderNo);
    }
  });

  const [defaultAccount, setDefaultAccount] = useState<DefautlOptionType>();
  useDidShow(() => {
    SalesReturnsStore.getAfterSaleDetail();
  });
  useUnload(() => {
    // 清空状态
    console.log('reset', SalesReturnsStore.reset());
  });

  const disabled = !isEmpty(SalesReturnsStore.orderId);
  const [accountList, setAccountList] = useState<DefautlOptionType[]>([]);
  useAsyncEffect(async () => {
    if (SalesReturnsStore.storeId) {
      const result = await queryMemberAccountPage({
        belongToStore: [SalesReturnsStore.storeId],
      });
      if (result?.data) {
        const accList = result.data.map((item) => ({
          title: item.memberAccountName,
          value: item.id,
        }));
        setAccountList(accList);
        setDefaultAccount(accList[0]);
      }
    }
  }, [SalesReturnsStore.storeId]);
  // 获取所有状态
  const refundType = SalesReturnsStore.refundType;
  const returnsType = SalesReturnsStore.returnsType;

  // 结算方式默认样式
  const defaultSettletypeClass = 'px-[71px] py-[16px] rounded-[8px]';
  const defaultSelectedClass = 'bg-[#FFF4F4FF] text-primary';
  const defaultUnSelectedClass = 'bg-[#********] text-main';
  // 现款
  const cashClass = classnames(defaultSettletypeClass, {
    [defaultSelectedClass]: refundType === RefundTypeEnum.Cash,
    [defaultUnSelectedClass]: refundType === RefundTypeEnum.Account,
  });
  // 挂账
  const accountClass = classnames(defaultSettletypeClass, {
    [defaultSelectedClass]: refundType === RefundTypeEnum.Account,
    [defaultUnSelectedClass]: refundType === RefundTypeEnum.Cash,
  });

  // 退货类型
  const defaultReundtypeClass = 'text-[32px]';
  const defaultReundtypeSelectedClass = 'text-main';
  const defaultReundtypeUnSelectedClass = 'text-secondary';
  // 销售单退货
  const orderClass = classnames(defaultReundtypeClass, {
    [defaultReundtypeSelectedClass]: returnsType === ReturnsTypeEnum.ORDER,
    [defaultReundtypeUnSelectedClass]: returnsType === ReturnsTypeEnum.GOODS || disabled,
  });
  // 商品退货
  const goodsClass = classnames(defaultReundtypeClass, {
    [defaultReundtypeSelectedClass]: returnsType === ReturnsTypeEnum.GOODS,
    [defaultReundtypeUnSelectedClass]: returnsType === ReturnsTypeEnum.ORDER || disabled,
  });

  const selectedItems = SalesReturnsStore.goods;
  /**
   * 计算总价
   */
  const totalAmount = sumBy(
    selectedItems,
    (t) => multiply(Number(t?.unitAmount ?? 0) * 100, t.refundNum ?? 1) / 100,
  );

  /**
   * 计算总数
   */
  const totalCount = sumBy(selectedItems, 'refundNum');

  // 提交订单
  const onSubmit = async () => {
    const params = { orderId: SalesReturnsStore.orderId, orderNo: SalesReturnsStore.orderNo };
    const result = await submitOrder(params);
    if (result) {
      const checkedList = SalesReturnsStore.checkboxValues;
      // 确认退款
      if (includes(checkedList, SubmitCheckboxEnum.CONFIRM_REFUND)) {
        await confirmRefund(params);
      }
      // 直接入库
      if (includes(checkedList, SubmitCheckboxEnum.DIRECT_IN)) {
        await directIn(params);
      }
      Taro.showToast({ title: '订单提交成功！' });
      RouterUtils.navigateTo({
        url: '/packages/sales/returns/detail/index',
        params: {
          orderId: SalesReturnsStore.orderId,
          orderNo: SalesReturnsStore.orderNo,
        },
      });
    }
  };
  const checkParams = () => {
    if (isEmpty(SalesReturnsStore.cstId)) {
      Taro.showToast({ icon: 'none', title: '请选择客户！' });
      return false;
    } else if (isEmpty(SalesReturnsStore.storeId) || isEmpty(SalesReturnsStore.backWarehouseId)) {
      Taro.showToast({ icon: 'none', title: '请选择门店与仓库！' });
      return false;
    }
    return true;
  };

  const onRefundTypeChange = async (refundType: RefundTypeEnum) => {
    SalesReturnsStore.setState('refundType', refundType);
    let array: ReturnsModifyRefundDetail[] = [];
    if (refundType == RefundTypeEnum.Account) {
      array = [];
    } else if (refundType == RefundTypeEnum.Cash) {
      // 现款填入默认账户
      array = [
        {
          accountId: defaultAccount?.value,
          refundAmount: totalAmount,
          accountName: defaultAccount?.title,
        },
      ];
    }
    SalesReturnsStore.setState('refundDetails', array);
  };

  const getSelectedAccountId = () => {
    const selectedIdArray: string[] = [];
    const accountIndex = SalesReturnsStore.accountIndex;
    const accountIdArray = SalesReturnsStore.refundDetails.map((t) => t.accountId);
    const selectId = accountIdArray[accountIndex];
    if (selectId) {
      selectedIdArray.push(selectId);
    }
    return selectedIdArray;
  };

  useEffect(
    () =>
      useSalesReturnsStore.subscribe(
        (state) => ({
          refundDetails: state.refundDetails,
          orderId: state.orderId,
          orderNo: state.orderNo,
        }),
        ({ refundDetails, orderId, orderNo }) => {
          console.log('&&&&&&&&&&&&&&&&&', orderId, orderNo, refundDetails);
          const validArray: ReturnsModifyRefundDetail[] = [];
          const inValidArray: ReturnsModifyRefundDetail[] = [];

          const array = refundDetails;
          for (let i = 0; i < array.length; i++) {
            const item = array[i];
            if (
              !isUndefined(item) &&
              isUndefined(item?.accountId) &&
              isUndefined(item?.refundAmount)
            ) {
              continue;
            }
            if (isEmpty(item) || isEmpty(item?.accountId) || (item.refundAmount ?? 0) <= 0) {
              inValidArray.push(item);
            } else {
              validArray.push(item);
            }
          }

          if (!isEmpty(inValidArray) || isEmpty(validArray) || isEmpty(orderId)) return;
          const reqParams = {
            orderId,
            orderNo,
            refund: {
              refundType,
              refundDetails: validArray.map((t) => ({
                accountId: t.accountId!,
                refundAmount: t.refundAmount!,
                accountName: t.accountName!,
              })),
            },
          };
          modifyOrder(reqParams);
        },
        {
          equalityFn: isEqual,
          fireImmediately: false,
        },
      ),
    [],
  );
  return (
    <div className="pb-[300px]">
      <CustomNavBar title="销售退货" />
      <div className={`pt-[16px] pb-[24px] px-[28px]`}>
        <ChooseStoreAndWarehouse
          disabled={disabled}
          value={
            SalesReturnsStore.storeId && SalesReturnsStore.backWarehouseId
              ? [SalesReturnsStore.storeId, SalesReturnsStore.backWarehouseId]
              : []
          }
          disabledColumns={[]}
          onConfirm={(options, value) => {
            if (options && value) {
              const [storeId, backWarehouseId] = value;
              SalesReturnsStore.setState('storeId', storeId);
              SalesReturnsStore.setState('backWarehouseId', backWarehouseId);
            }
          }}
        />
      </div>
      {/* 客户选择 */}
      <div className="px-[28px]">
        <CstCard
          onConfirm={(customerDetail) => {
            const credit = customerDetail?.settle?.credit;
            const usedAmount = customerDetail?.settle?.usedAmount ?? 0;
            const availableAmount = customerDetail?.settle?.availableAmount ?? 0;
            SalesReturnsStore.setState('cstId', customerDetail.base.id);
            if (credit) {
              SalesReturnsStore.setState('isSupportCredit', true);
              SalesReturnsStore.setState('usedAmount', usedAmount);
              SalesReturnsStore.setState('availableAmount', availableAmount);
              SalesReturnsStore.setState('refundType', RefundTypeEnum.Account);
            } else {
              SalesReturnsStore.setState('isSupportCredit', false);
              SalesReturnsStore.setState('refundType', RefundTypeEnum.Cash);
            }
          }}
          disabled={disabled}
          cstId={SalesReturnsStore.cstId}
        />
      </div>

      {/* 销售单退货/商品退货 */}
      <div className="px-[28px]">
        <div className="flex justify-evenly items-center bg-white rounded-[16px] py-[40px] mt-[24px]  ">
          <div
            className="flex justify-start items-center gap-[8px]"
            onClick={() => {
              if (!checkParams()) return;
              if (!disabled) {
                SalesReturnsStore.setState('returnsType', ReturnsTypeEnum.ORDER);
              }
              if (disabled && SalesReturnsStore.returnsType !== ReturnsTypeEnum.ORDER) {
                return;
              }
              const params = stringify({
                returnsType: ReturnsTypeEnum.ORDER,
                backWarehouseId: SalesReturnsStore.backWarehouseId,
                cstId: SalesReturnsStore.cstId,
                storeId: SalesReturnsStore.storeId,
              });
              Taro.navigateTo({
                url: `/packages/sales/returns/returnsItemSelect/index?${params}`,
              });
            }}
          >
            <Image src={ORDER_ICONS_MAP[returnsType]} width="16px" height="16px"></Image>
            <span className={orderClass}>销售单退货</span>
          </div>
          <span className="h-[32px] w-[1px] bg-[#00000026]" />
          <div
            className="flex justify-start items-center gap-[8px]"
            onClick={() => {
              if (!checkParams()) return;
              // 如果创基了订单则不能切换
              if (!disabled) {
                SalesReturnsStore.setState('returnsType', ReturnsTypeEnum.GOODS);
              }
              if (disabled && SalesReturnsStore.returnsType !== ReturnsTypeEnum.GOODS) {
                return;
              }
              const params = stringify({
                pageSize: 10,
                returnsType: ReturnsTypeEnum.GOODS,
                warehouseId: SalesReturnsStore.backWarehouseId,
              });
              Taro.navigateTo({
                url: `/packages/sales/returns/returnsItemSelect/index?${params}`,
              });
            }}
          >
            <Image src={GOODS_ICONS_MAP[returnsType]} width="16px" height="16px"></Image>
            <span className={goodsClass}>商品退货</span>
          </div>
        </div>
      </div>
      {!isEmpty(SalesReturnsStore.orderNo) && !isEmpty(SalesReturnsStore.goods) && (
        <>
          {/* 退货明细 */}
          <Card
            title={
              <div className="flex justify-between">
                <span>退货明细</span>
                <Image
                  src={iconEdit}
                  width="16px"
                  height="16px"
                  onClick={() => {
                    const params = stringify({
                      returnsType: SalesReturnsStore.returnsType,
                    });
                    Taro.navigateTo({
                      url: `/packages/sales/returns/selectedPage/index?${params}`,
                    });
                  }}
                />
              </div>
            }
          >
            <div id="returnList">
              {SalesReturnsStore.goods &&
                SalesReturnsStore.goods.map((record) => {
                  return <ReturnsDetailItem returnsType={returnsType} record={record} />;
                })}
            </div>
          </Card>
          {/* 结算方式 */}
          <Card title="结算方式" className="mx-0">
            <div className="mt-[24px] flex justify-start gap-[22px] text-[28px]">
              {/* 是否支持挂账 */}
              {SalesReturnsStore.isSupportCredit && (
                <span
                  className={accountClass}
                  onClick={() => {
                    onRefundTypeChange(RefundTypeEnum.Account);
                  }}
                >
                  挂账
                </span>
              )}
              <span
                className={cashClass}
                onClick={() => {
                  onRefundTypeChange(RefundTypeEnum.Cash);
                }}
              >
                现款
              </span>
            </div>
            {/* 挂账 */}
            {SalesReturnsStore.isSupportCredit && refundType === RefundTypeEnum.Account && (
              <div className="mt-[24px] flex">
                <span className="text-secondary text-[28px]">
                  已用{SalesReturnsStore.usedAmount ?? '-'}/可用
                  {SalesReturnsStore.availableAmount ?? '-'}
                </span>
              </div>
            )}
            {/* 现款 */}
            {refundType === RefundTypeEnum.Cash && (
              <div className="mt-[24px] flex flex-col">
                {SalesReturnsStore.refundDetails?.map((t, index) => {
                  return (
                    <div
                      key={t.key}
                      className={`flex items-center boder-bottom-${index ^ 1} py-[28px]`}
                    >
                      <div
                        className="py-[16px] flex justify-between items-center gap-[18px] flex-1"
                        onClick={() => {
                          SalesReturnsStore.setState('visibleAccount', true);
                          SalesReturnsStore.setState('accountIndex', index);
                        }}
                      >
                        <span
                          className={`text-[28px]  max-w-[280px] truncate ${
                            isEmpty(t.accountName) ? 'text-disabled' : ''
                          }`}
                        >
                          {defaultTo(t.accountName, '请选择账户')}
                        </span>
                        <ArrowDown color="#0000004D" width={28} height={16}></ArrowDown>
                      </div>
                      <div className="flex flex-row items-center justify-between flex-1">
                        <span className="w-[260px]">
                          <Input
                            type="digit"
                            placeholder="0.00"
                            align="center"
                            maxLength={12}
                            style={{
                              '--nutui-input-color': '#000000E6',
                              '--nutui-input-padding': '3px 0px',
                              '--nutuiInputFontSize': '16px',
                              '--nutuiInputBorderRadius': '2px',
                            }}
                            defaultValue={`${t?.refundAmount ?? 0}`}
                            onBlur={(value) => {
                              SalesReturnsStore.updateRefundDetail(index, {
                                ...t,
                                refundAmount: Number(value),
                              });
                            }}
                          />
                        </span>
                        <span className="h-[32px] w-[32px]">
                          {(SalesReturnsStore.refundDetails?.length ?? 0) > 1 && (
                            <Image
                              src={iconRemove}
                              height="16px"
                              width="16px"
                              onClick={() => {
                                SalesReturnsStore.removeRefundDetail(index);
                              }}
                            />
                          )}
                        </span>
                      </div>
                    </div>
                  );
                })}
                {(SalesReturnsStore.refundDetails?.length ?? 0) < 2 && (
                  <div
                    className="mt-[16px] flex justify-center items-center gap-[16px]"
                    onClick={() => {
                      SalesReturnsStore.addRefundDetail();
                    }}
                  >
                    <Add color="#F49C1FFF" height="14px" width="14px" />
                    <span className="text-primary text-[28px]">新增结算方式</span>
                  </div>
                )}
              </div>
            )}
          </Card>
          {/* 备注 */}
          <Card title="备注" className="mx-0">
            <ConfigProvider
              theme={{
                nutuiTextareaPadding: '0px 0px',
              }}
            >
              <TextArea
                cursorSpacing={32}
                autoSize
                maxLength={100}
                placeholder="请输入备注信息"
                onBlur={(e: any) => {
                  modifyOrder({
                    orderId: SalesReturnsStore.orderId,
                    orderNo: SalesReturnsStore.orderNo,
                    remark: e?.detail?.value,
                  });
                }}
                onChange={(remark) => {
                  SalesReturnsStore.setState('remark', remark);
                }}
                value={SalesReturnsStore.remark}
              />
            </ConfigProvider>
          </Card>
          <div
            id="SalesOperationBottom"
            className="fixed bottom-0 left-0 z-50 right-0 pb-[28px] bg-white flex flex-col"
          >
            <div className="bg-[#FFF4F4] text-[24px] text-main px-[28px] py-[12px] flex items-center gap-[16px]">
              <span>退货单号：{SalesReturnsStore.orderNo}</span>
              <span className="px-[12px] py-[6px] bg-[#FFF4F4] text-primary text-[20px] rounded-[4px] border-[#FCAEADFF] border-solid border-[1px]">
                草稿
              </span>
            </div>
            {/* 底部复选框 确认结算/一键入库 */}
            <div className="py-[24px] px-[28px] shadow-sm">
              <div className="flex justify-start items-center gap-[80px]">
                {SubmitCheckboxOptions.filter((t) => {
                  return hasPermission(t.permission);
                }).map((option) => {
                  const checked = includes(SalesReturnsStore.checkboxValues, option.value);
                  return (
                    <div
                      className="flex justify-start items-center gap-[16px]"
                      onClick={() => {
                        SalesReturnsStore.updateChecked(option.value);
                      }}
                    >
                      <Image
                        src={checked ? iconCheckboxSel : iconCheckboxDef}
                        width="35px"
                        height="35px"
                      ></Image>
                      <span className="text-main text-[28px]">{option.label}</span>
                    </div>
                  );
                })}
              </div>
              <div className="flex justify-between items-center py-[26px]  px-[28px]">
                <div className="flex items-center text-[24px] text-secondary leading-[30px]">
                  <span>共{totalCount}件</span>
                  <span>退款</span>
                  <ConfigProvider
                    theme={{
                      nutuiColorPrimary: '#F49C1F',
                      nutuiPriceSymbolMediumSize: '12px',
                      nutuiPriceIntegerMediumSize: '24px',
                      nutuiPriceDecimalMediumSize: '12px',
                      nutuiPriceSymbolPaddingRight: '0px',
                    }}
                  >
                    <Price price={totalAmount} size="normal" thousands></Price>
                  </ConfigProvider>
                </div>
                <ConfigProvider
                  theme={{
                    nutuiButtonBorderRadius: '4px',
                    nutuiButtonDefaultHeight: '36px',
                    nutuiButtonNormalPadding: '0px 44px',
                  }}
                >
                  <PermissionComponent permission="salesSubmit">
                    <Button type="primary" onClick={onSubmit} loading={SalesReturnsStore.loading}>
                      提交
                    </Button>
                  </PermissionComponent>
                </ConfigProvider>
              </div>
            </div>
            {/* 选择结算账户 */}
          </div>
        </>
      )}
      <CustomPicker
        items={accountList}
        title="选择账户"
        visible={SalesReturnsStore.visibleAccount}
        selected={getSelectedAccountId()}
        onClose={() => {
          SalesReturnsStore.setState('visibleAccount', false);
        }}
        multiple={false}
        onConfirm={(items) => {
          if (isEmpty(items)) return;
          const selectOption = find(accountList, { value: items[0] });
          if (isEmpty(selectOption)) return;
          SalesReturnsStore.updateRefundDetail(SalesReturnsStore.accountIndex, {
            accountId: selectOption.value,
            accountName: selectOption.title,
          });
          SalesReturnsStore.setState('visibleAccount', true);
        }}
      />
    </div>
  );
}
