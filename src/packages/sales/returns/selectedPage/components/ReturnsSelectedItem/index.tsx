import iconRemove from '@/assets/icons/icon_remove.png';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import { ConfigProvider, Image, Input, InputNumber } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import { defaultTo } from 'lodash';
import { useState } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { deleteItem, modifyItem } from '../../../operation/services';
import useSalesReturnsStore from '../../../operation/store';
import returnsItemSelectStore from '../../../returnsItemSelect/store';
import { ReturnsListItem } from '../../types/ReturnsListItem';
import { ReturnsTypeEnum } from '../../types/ReturnsTypeEnum';
export interface SelectedItemProps {
  record: ReturnsListItem;
  returnsType: string;
  index: number;
}

export default (props: SelectedItemProps) => {
  const [loading, setLoading] = useState<boolean>(false);
  const { record, returnsType = ReturnsTypeEnum.GOODS, index } = props;
  const { orderId, orderNo } = useSalesReturnsStore(
    useShallow((store) => ({
      orderId: store.orderId,
      orderNo: store.orderNo,
    })),
  );
  const ReturnsItemSelectStore = returnsItemSelectStore();
  const unitAmount = defaultTo(record?.unitAmount, 0);
  const refundNum = defaultTo(record?.refundNum, 0);
  const handleItemRemove = async (id?: string) => {
    if (id) {
      const result = await deleteItem({ ids: [id], orderId, orderNo });
      if (result) {
        ReturnsItemSelectStore.remove(index);
      }
    }
  };
  return (
    <>
      <div className="bg-white rounded-[16px] py-[28px]">
        {returnsType == ReturnsTypeEnum.ORDER && (
          <div className="shadow-sm">
            <div className="px-[28px] mb-[24px] flex justify-between items-center pb-[24px] ">
              <span className="text-main  text-[28px]">{record?.orgOrderNo}</span>
              <Image
                src={iconRemove}
                height="18px"
                width="16px"
                onClick={() => handleItemRemove(record.id)}
              ></Image>
            </div>
          </div>
        )}
        <div className="flex px-[28px]">
          <img
            src={record?.images?.[0]}
            className="rounded mr-[20px] bg-[#f5f5f5] w-[88px] h-[88px]"
          />
          <div className="flex-1">
            <div className="flex justify-between items-center">
              <span className="text-[32px] text-main">{record.itemName}</span>
              {returnsType == ReturnsTypeEnum.GOODS && (
                <Image
                  src={iconRemove}
                  height="18px"
                  width="16px"
                  onClick={() => handleItemRemove(record.id)}
                ></Image>
              )}
            </div>
            <div className="flex text-[24px] text-thirdary mt-[12px]">
              <ItemsWithDivider
                items={[record?.itemSn, record?.brandName, record?.categoryName, record?.unitName]}
              />
            </div>
          </div>
        </div>
        <div className="flex justify-between items-center mt-[28px] px-[28px]">
          <span className="border-[#00000026] border-[1px] border-solid w-[200px]">
            <Input
              type="digit"
              align="center"
              maxLength={12}
              style={{
                '--nutui-input-color': '#000000E6',
                '--nutui-input-padding': '3px 0px',
                '--nutuiInputFontSize': '16px',
                '--nutuiInputBorderRadius': '2px',
              }}
              defaultValue={`${unitAmount}`}
              placeholder="0.01"
              onBlur={async (value: string) => {
                const changeUnitAmount = Number(value);
                if (isNaN(changeUnitAmount) || changeUnitAmount <= 0) {
                  Taro.showToast({ icon: 'none', title: '退款金额必须大于0' });
                  return;
                }
                const result = await modifyItem({
                  id: record?.id,
                  orderId,
                  orderNo,
                  unitAmount: changeUnitAmount,
                });
                if (result) {
                  ReturnsItemSelectStore.saveOrUpdate({
                    ...record,
                    unitAmount: changeUnitAmount,
                  });
                }
              }}
            />
          </span>
          <div className="text-[#0000004D] text-[32px]">X</div>

          {/* 没有售价不显示 */}
          <ConfigProvider
            theme={{
              nutuiInputnumberIconSize: '24px',
              nutuiInputnumberInputBorder: '1px',
              nutuiInputnumberInputWidth: '44px',
            }}
          >
            <span className="w-[200px]">
              <InputNumber
                async={true}
                allowEmpty={false}
                min={1}
                disabled={loading}
                max={999999}
                value={refundNum}
                onClick={(e) => {
                  e.stopPropagation();
                }}
                onChange={async (value: number) => {
                  setLoading(true)
                  try {
                    const result = await modifyItem({
                      id: record?.id,
                      orderId,
                      orderNo,
                      refundNum: value,
                    });
                    if (result) {
                      ReturnsItemSelectStore.saveOrUpdate({
                        ...record,
                        refundNum: value,
                      });
                    }
                  } catch (error) {

                  }
                  finally {
                    setLoading(false)
                  }
                }}
              />
            </span>
          </ConfigProvider>
          {ReturnsTypeEnum.ORDER === returnsType && record?.refundableNum && (
            <span className="text-[24px] text-secondary">可退数量 {record?.refundableNum}</span>
          )}
        </div>
      </div>
    </>
  );
};
