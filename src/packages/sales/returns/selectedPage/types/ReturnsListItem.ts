export interface ReturnsListItem {
    id?: string;
    itemId?: string;
    returnsId?: string;
    itemName?: string;
    imageUrl?: string;
    /** 商品编码 */
    itemSn?: string;
    /** etcNo */
    etcNo?: string;
    /** OE号数组 */
    oeNos?: string[];
    /**  品牌件号数组 */
    brandPartNos?: string[];
    images?: string[];
    /** 品牌名称 */
    brandName?: string;
    /** 三级品类名称 */
    categoryName?: string;
    /** 商品单位 */
    unitName?: string;
    payKindName?: string;
    orderNo?: string;
    orgOrderNo?: string;
    orderId?: string;
    orderFinishTime?: string;
    /** 退货数量 */
    refundNum?: number;
    /** 实付单价 */
    unitAmount?: string | number;
    /** 可退数量 */
    refundableNum?: number;
    /**退货原因 */
    cause?: string;
    costAmount?: number;
    /** 原始销售数量 */
    saleNum?: number;
    originUnitAmount?: number;

}