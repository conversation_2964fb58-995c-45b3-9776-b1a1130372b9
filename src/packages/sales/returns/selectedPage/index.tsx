import '@/assets/lightPage.scss';
import { <PERSON><PERSON>, Config<PERSON><PERSON>ider, Price, SafeArea } from '@nutui/nutui-react-taro';
import { getCurrentInstance } from '@tarojs/runtime';
import Taro, { useDidShow } from '@tarojs/taro';
import { isEmpty, multiply, sumBy } from 'lodash';
import { useShallow } from 'zustand/react/shallow';
import useSalesReturnsStore from '../operation/store';
import returnsItemSelectStore from '../returnsItemSelect/store';
import ReturnsSelectedItem from './components/ReturnsSelectedItem';

export default () => {
  const { returnsType } = getCurrentInstance().router?.params as Record<string, string>;
  // const returnsType = ReturnsTypeEnum.ORDER;
  const SalesReturnsStore = useSalesReturnsStore();
  const { chooseItems, reloadChooseItems } = returnsItemSelectStore(
    useShallow((store) => ({
      clear: store.clear,
      chooseItems: store.chooseItems,
      reloadChooseItems: store.reloadChooseItems,
    })),
  );

  useDidShow(() => {
    reloadChooseItems({ orderId: SalesReturnsStore.orderId, orderNo: SalesReturnsStore.orderNo });
  });

  /**
   * 计算总价
   */
  const getTotalPrice = () => {
    return sumBy(
      chooseItems,
      (t) => multiply(Number(t?.unitAmount ?? 0) * 100, t.refundNum ?? 1) / 100,
    );
  };

  /**
   * 计算总数
   */
  const getTotalNumber = () => {
    return sumBy(chooseItems, t => Number(t.refundNum));
  };
  /**
   * 提交数据
   */
  const handleSubmit = () => {
    Taro.navigateBack({
      success: function () { },
    });
  };

  return (
    <div className="flex flex-col h-screen">
      <div className="mt-[16px]">
        {!isEmpty(chooseItems) && (
          <span className="px-[28px]">
            {chooseItems.map((t, index) => (
              <ReturnsSelectedItem
                index={index}
                returnsType={returnsType}
                record={t}
              ></ReturnsSelectedItem>
            ))}
          </span>
        )}
      </div>
      <div className="fixed bottom-0 left-0 z-50 right-0 bg-white">
        {/* 底部复选框 确认结算/一键入库 */}
        <div className="flex justify-between items-center p-[28px]">
          <div className="flex items-center text-[24px] text-[#777777] leading-[30px]">
            <span className="mr-[16px]">共{getTotalNumber()}件</span>
            <span>退款</span>
            <ConfigProvider
              theme={{
                nutuiColorPrimary: '#F49C1FFF',
                nutuiPriceSymbolMediumSize: '12px',
                nutuiPriceIntegerMediumSize: '24px',
                nutuiPriceDecimalMediumSize: '12px',
                nutuiPriceSymbolPaddingRight: '0px',
              }}
            >
              <Price price={getTotalPrice()} size="normal" thousands></Price>
            </ConfigProvider>
          </div>
          <ConfigProvider
            theme={{
              nutuiButtonBorderRadius: '3px',
              nutuiButtonDefaultHeight: '36px',
              nutuiButtonNormalPadding: '0px 44px',
            }}
          >
            <Button type="primary" onClick={handleSubmit} loading={SalesReturnsStore.loading}>
              确定
            </Button>
          </ConfigProvider>
        </div>
        <SafeArea position="bottom"></SafeArea>
      </div>
    </div>
  );
};
