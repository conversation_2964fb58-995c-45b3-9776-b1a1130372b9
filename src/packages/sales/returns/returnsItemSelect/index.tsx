import '@/assets/lightPage.scss';
import CalendarRangeCardChoose from '@/components/CalendarRangeCardChoose';
import CustomSearchBar from '@/components/CustomSearchBar';
import GoodsSearchBar from '@/components/GoodsSearchBar';
import MenuWrap from '@/components/MenuWrap';
import FunPagination from '@/components/Pagination/FunPagination';
import { IconFont } from '@nutui/icons-react-taro';
import { Badge, Button, Menu, Price, SafeArea } from '@nutui/nutui-react-taro';
import Taro, { useDidShow, useLoad, useRouter, useUnload } from '@tarojs/taro';
import dayjs from 'dayjs';
import { isEmpty, multiply, sumBy } from 'lodash';
import { useRef, useState } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { getRefundablePaged, queryReturnsGoods } from '../operation/services';
import useSalesReturnsStore from '../operation/store';
import ReturnsItem from './components/ReturnsItem';
import ReturnsItemModal from './components/ReturnsItemModal';
import packageIcon from './imgs/package.svg';
import returnsItemSelectStore from './store';
import { ReturnsListItem } from './types/ReturnsListItem';
import { ReturnsTypeEnum } from './types/ReturnsTypeEnum';
const PAGE_TITLE = {
  [ReturnsTypeEnum.GOODS]: '选择商品',
  [ReturnsTypeEnum.ORDER]: '选择销售单',
};
const itemList = [
  { key: 'keyword', name: '商品', scanShow: true, placeholder: '商品名称/编码/OE码' },
  { key: 'orderNo', name: '销售单号', scanShow: false, placeholder: '销售单号' },
];
export default () => {
  const { params: routerParams } = useRouter();
  const { returnsType, $taroTimestamp, ...rest } = routerParams;
  const orderTimeRef = useRef<any>();
  const SalesReturnsStore = useSalesReturnsStore();
  const { clear, chooseItems, reloadChooseItems, visible } = returnsItemSelectStore(
    useShallow((store) => ({
      clear: store.clear,
      chooseItems: store.chooseItems,
      visible: store.visible,
      reloadChooseItems: store.reloadChooseItems,
    })),
  );

  useDidShow(() => {
    reloadChooseItems({ orderId: SalesReturnsStore.orderId, orderNo: SalesReturnsStore.orderNo });
  });
  // 已选的商品
  const [params, setParams] = useState<any>(rest);
  const setInputValue = (param) => {
    if (!isEmpty(param)) {
      setParams((prevData) => ({ ...prevData, ...param }));
    }
  };
  useLoad(() => {
    Taro.setNavigationBarTitle({ title: PAGE_TITLE[returnsType!] });
  });
  useUnload(() => {
    clear();
  });

  /**
   * 列表查询
   * @param page
   */
  const fetchData = async (param): Promise<ReturnsListItem[]> => {
    let result: ReturnsListItem[] = [];
    // 商品退货
    if (returnsType == ReturnsTypeEnum.GOODS) {
      const { data: goods } = await queryReturnsGoods({
        ...param,
        itemStatus: "1",
        isFetchLocalInventory: true,
        isFetchWarehouseCostPrice: true,
        isFetchLocation: true,
        sortType: 1,
        isFetchLastSalePrice: true,
      });
      result = goods.map((good) => {
        return {
          disabled: false,
          returnsId: good.itemId,
          itemId: good.itemId,
          itemSn: good.itemSn,
          oeNos: good.oeNos,
          brandPartNos: good.brandPartNos,
          brandName: good.brandName,
          categoryName: good.categoryName,
          unitName: good.unitName,
          etcNo: good.etcNo,
          refundNum: 0, //默认退货数量为0
          unitAmount: good?.suggestPrice ?? 0, //默认退货价格等于建议售价
          itemName: good.itemName,
          imageUrl: good.images?.[0],
        };
      });
    } else if (returnsType == ReturnsTypeEnum.ORDER) {
      // 销售单退货
      const { data: orders } = await getRefundablePaged(param);
      result = orders.map((order) => {
        return {
          disabled: order.refundableNum <= 0,
          returnsId: order.orderNo + order.itemId,
          itemId: order.itemId,
          itemSn: order.itemSn,
          itemName: order.itemName,
          payKindName: order.payKindName,
          payKind: order.payKind,
          oeNos: order.oeNos,
          brandPartNos: order.brandPartNos,
          brandName: order.brandName,
          categoryName: order.categoryName,
          unitName: order.unitName,
          refundNum: 0, // 退货数量默认0
          unitAmount: order.unitAmount, // 退款金额
          costAmount: order.costAmount,
          saleNum: order.saleNum,
          orgOrderNo: order.orderNo,
          orderFinishTime: order.orderFinishTime,
          refundableNum: order.refundableNum,
          imageUrl: order.images?.[0],
        };
      });
    }
    return result;
  };

  /**
   * 计算总价
   */
  const getTotalPrice = () => {
    return sumBy(
      chooseItems,
      (t) => multiply(Number(t?.unitAmount ?? 0) * 100, t.refundNum ?? 1) / 100,
    );
  };

  /**
   * 计算总数
   */
  const getTotalNumber = () => {
    return sumBy(chooseItems, 'refundNum');
  };
  /**
   * 提交数据
   */
  const handleSubmit = () => {
    if (isEmpty(chooseItems)) {
      Taro.showToast({ icon: 'none', title: '请至少选择一条记录！' });
    } else {
      Taro.navigateBack();
    }
  };

  return (
    <div className="flex flex-col h-screen">
      <div className="px-[28px] pt-[16px] mb-[24px]">
        {returnsType == ReturnsTypeEnum.ORDER && (
          <CustomSearchBar
            itemList={itemList}
            defaultItem={itemList[0]}
            inputValue={setInputValue}
            isShowAdd={false}
          />
        )}
      </div>
      {returnsType == ReturnsTypeEnum.ORDER && (
        <MenuWrap
          menu={
            <Menu>
              <Menu.Item title="销售时间" ref={orderTimeRef}>
                <CalendarRangeCardChoose
                  value={
                    params.beginTime
                      ? [dayjs(params.beginTime).toDate(), dayjs(params.endTime).toDate()]
                      : []
                  }
                  onChange={(value) => {
                    if (value) {
                      setParams({
                        ...params,
                        beginTime: value[0]
                          ? dayjs(value[0]).format('YYYY-MM-DD 00:00:00')
                          : undefined,
                        endTime: value[1]
                          ? dayjs(value[1]).format('YYYY-MM-DD 23:59:59')
                          : undefined,
                      });
                      orderTimeRef.current?.toggle(false);
                    }
                  }}
                />
              </Menu.Item>
            </Menu>
          }
        />
      )}
      {returnsType == ReturnsTypeEnum.GOODS && (
        <GoodsSearchBar
          inputValue={(query) => {
            setParams({ ...params, ...query });
          }}
          keyStr="queryKeyWord"
        />
      )}

      <FunPagination<ReturnsListItem>
        params={params}
        fetchData={fetchData}
        popupNode={<ReturnsItemModal returnsType={returnsType!} />}
        renderItem={(record) => {
          return <ReturnsItem returnsType={returnsType!} record={record} />;
        }}
      />
      {!visible && <div className="bg-white p-[28px]">
        <div className="flex justify-between ">
          <div className="flex">
            <Badge value={getTotalNumber()}>
              <IconFont name={packageIcon} style={{ height: '30px' }} />
            </Badge>
            <Price price={getTotalPrice()} className="ml-2" />
          </div>
          <Button type="primary" onClick={handleSubmit}>
            选好了
          </Button>
        </div>
        <SafeArea position="bottom" />
      </div>}
    </div>
  );
};
