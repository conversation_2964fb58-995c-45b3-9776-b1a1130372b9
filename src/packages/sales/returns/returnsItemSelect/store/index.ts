import { findIndex, isEmpty } from 'lodash';
import { create } from 'zustand';
import { getAfterSaleDetail } from '../../operation/services';
import { ReturnsOrderType } from '../../operation/types/ReturnsOrderType';
import { ReturnsListItem } from '../types/ReturnsListItem';


// 定义状态的类型
interface ReturnsState {
    visible: boolean;
    setVisible: (visible: boolean) => void;
    currentItem: ReturnsListItem,
    setCurrentItem: (currentItem: ReturnsListItem) => void,
    // 选择结算方式为现款时的账户配置
    chooseItems: ReturnsListItem[];
    // 退货列表-新增或修改记录
    saveOrUpdate: (chooseItem: ReturnsListItem) => void;
    // 清空选项
    clear: () => void;
    remove: (index: number) => void;
    reloadChooseItems: (result: ReturnsOrderType) => Promise<void>
}
const initialState = {
    currentItem: {},
    visible: false,
    chooseItems: [],
}

// 销售退货选择页
const returnsItemSelectStore = create<ReturnsState>()((set, get) => ({
    ...initialState,
    setVisible: (visible) => {
        set({ visible })
    },
    setCurrentItem: (currentItem: ReturnsListItem) => {
        set({ currentItem })
    },
    clear: () => {
        set(initialState)
    },
    remove: (index) => set((state) => {
        const chooseItems = [...state.chooseItems];
        chooseItems.splice(index, 1);
        return { chooseItems };
    }),
    saveOrUpdate: (chooseItem: ReturnsListItem) => {

        set((state) => {
            const chooseItems = [...state.chooseItems];
            console.log(chooseItem, chooseItems);
            const updateIndex = findIndex(chooseItems, { returnsId: chooseItem.returnsId })
            // 新增
            if (updateIndex < 0) {
                chooseItems.splice(0, 0, chooseItem);
            } else {
                // 修改
                chooseItems.splice(updateIndex, 1, chooseItem);
            }
            return { chooseItems };
        });
    },

    reloadChooseItems: async (params) => {
        if (isEmpty(params.orderId)) {
            set({ chooseItems: [] })
        } else {
            const result = await getAfterSaleDetail(params);
            if (result?.goods) {
                set({
                    chooseItems: result?.goods.map((t) => ({
                        ...t,
                        returnsId: (t?.orgOrderNo ?? '') + t.itemId,
                    }))
                })
            }
        }
    }


}));

export default returnsItemSelectStore;
