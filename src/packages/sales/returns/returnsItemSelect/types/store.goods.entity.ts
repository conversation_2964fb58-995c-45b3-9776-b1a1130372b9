export interface StoreGoodsEntity {
  /**
   * 适用车型
   */
  adaptModel?: string;
  /**
   * etc适配车型id列表
   */
  adaptModelIds?: string[];
  /**
   * 适配车系
   */
  adaptSeries?: string;
  /**
   * 适配车系code
   */
  adaptSeriesCode?: string;
  origPriceYuan?: string;
  /**
   * 可用库存
   */
  avaNum?: number;
  /**
   * 商品条形码
   */
  barCode?: string;
  /**
   * 品牌ID
   */
  brandId?: string;
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 品牌件号列表
   */
  brandPartNos?: string[];
  /**
   * 类目ID
   */
  categoryId?: string;
  /**
   * 三级类目名称
   */
  categoryName?: string;
  /**
   * 商品成本价
   */
  costPrice?: number;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * ETC号
   */
  etcNo?: string;
  /**
   * 图片列表
   */
  images?: string[];
  /**
   * 总库存，账面库存
   */
  inventoryNum?: number;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 是否套装：0-否1-是
   */
  isSuit?: number;
  /**
   * 商品ID
   */
  itemId: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn: string;
  /**
   * 状态:0-禁用1-启用
   */
  itemStatus?: number;
  /**
   * 1-标准2-非标
   */
  itemType?: number;
  /**
   * 客户上次售价
   */
  lastSalePrice?: number;
  /**
   * 商品库位
   */
  locationCode?: string;
  /**
   * 总结库存
   */
  lockedNum?: number;
  /**
   * 最低限价
   */
  lowPrice?: number;
  /**
   * 助记码
   */
  memCode?: string;
  /**
   * 最小起订量
   */
  minOrderNum?: number;
  /**
   * 最小包装量
   */
  minPackNum?: number;
  /**
   * oe号列表
   */
  oeNos?: string[];
  /**
   * 产地id
   */
  originRegionId?: string;
  /**
   * 产地名称
   */
  originRegionName?: string;
  /**
   * 采购价
   */
  purchasePrice?: number;
  /**
   * 采购状态:0-否1-是
   */
  purchaseStatus?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 销售状态:0-否1-是
   */
  saleStatus?: number;
  /**
   * skuId
   */
  skuId?: string;
  /**
   * SKU商品名称
   */
  skuName?: string;
  /**
   * 商品备注
   */
  skuRemark?: string;
  /**
   * 三方数据编码
   */
  sourceCode?: string;
  /**
   * 商品规格
   */
  spec?: string;
  /**
   * 建议售价
   */
  suggestPrice?: number;
  /**
   * 三方商品号
   */
  thirdNo?: string;
  /**
   * 在途库存
   */
  transitNum?: number;
  /**
   * 单位id
   */
  unitId?: string;
  /**
   * 单位名称
   */
  unitName?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
  /**
   * 仓库ID
   */
  warehouseId?: number;
}
