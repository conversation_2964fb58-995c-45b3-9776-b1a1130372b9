import iconPlus from '@/assets/icons/icon_plus.svg';
import CustTag from '@/components/CustTag';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import { ConfigProvider, Image, InputNumber, Price } from '@nutui/nutui-react-taro';
import { defaultTo, find, isUndefined } from 'lodash';
import { useShallow } from 'zustand/react/shallow';
import { modifyItem } from '../../../operation/services';
import useSalesReturnsStore from '../../../operation/store';
import returnsItemSelectStore from '../../store';
import { ReturnsListItem } from '../../types/ReturnsListItem';
import { ReturnsTypeEnum } from '../../types/ReturnsTypeEnum';

export interface GoodItemProps {
  record: ReturnsListItem;
  returnsType: string;
}

export default (props: GoodItemProps) => {
  const { record, returnsType = ReturnsTypeEnum.GOODS } = props;
  console.log(record);
  const { orderId, orderNo } = useSalesReturnsStore(
    useShallow((store) => ({
      orderId: store.orderId,
      orderNo: store.orderNo,
    })),
  );
  const ReturnsItemSelectStore = returnsItemSelectStore();
  const currentItem = find(ReturnsItemSelectStore.chooseItems, { returnsId: record?.returnsId });
  let OperatorButton: React.ReactNode = null;
  const refundableNum = record?.refundableNum ?? 0;
  const unitAmount = defaultTo(currentItem?.unitAmount, record.unitAmount) ?? '0';
  const refundNum = defaultTo(currentItem?.refundNum, record.refundNum) ?? 0;
  const openModal = () => {
    if (isUndefined(OperatorButton)) return;
    ReturnsItemSelectStore.setCurrentItem({
      ...record,
      unitAmount,
      refundNum,
      originUnitAmount: Number(unitAmount),
      id: currentItem?.id,
      cause: currentItem?.cause,
    });
    ReturnsItemSelectStore.setVisible(true);
  };
  if (
    !(
      (returnsType == ReturnsTypeEnum.ORDER && refundableNum <= 0) ||
      (returnsType == ReturnsTypeEnum.GOODS && Number(unitAmount) <= 0)
    )
  ) {
    if (refundNum > 0) {
      OperatorButton = (
        <ConfigProvider
          theme={{
            nutuiInputnumberIconSize: '24px',
            nutuiInputnumberInputBorder: '1px',
          }}
        >
          <InputNumber
            async={true}
            allowEmpty={false}
            min={1}
            disabled={record.disabled}
            max={999999}
            value={refundNum}
            onClick={(e) => {
              e.stopPropagation();
            }}
            onChange={async (value: number) => {
              const result = await modifyItem({
                id: currentItem?.id,
                orderId,
                orderNo,
                refundNum: value,
              });
              if (result) {
                ReturnsItemSelectStore.saveOrUpdate({
                  ...currentItem,
                  refundNum: value,
                });
              }
            }}
          />
        </ConfigProvider>
      );
    } else {
      OperatorButton = (
        <span className="border-[1px] border-solid border-[#00000026] p-[8px]" onClick={openModal} >
          <Image width="12px" height="12px" src={iconPlus} />
        </span>
      );
    }
  }

  return (
    <>
      <div className="bg-white rounded my-[24px] mx-[28px] py-[24px] px-[28px]">
        {returnsType == ReturnsTypeEnum.ORDER && (
          <div className="flex justify-between items-center pb-[24px] shadow-sm mb-[24px]">
            <span className="text-main  text-[28px]">{record?.orgOrderNo}</span>
            <span className="text-thirdary text-[24px]">{record?.orderFinishTime}</span>
          </div>
        )}
        <div className="flex">
          <img
            src={record?.imageUrl}
            className="rounded mr-[20px] bg-[#f5f5f5] w-[88px] h-[88px]"
          />
          <div className="flex-1">
            <div onClick={openModal}>
              <div className="flex justify-start items-center gap-[10px]">
                <span className="text-[32px] text-main">{record.itemName}</span>
                {ReturnsTypeEnum.ORDER === returnsType && <CustTag text={record?.payKindName} />}
              </div>
              <div className="flex text-[24px] text-thirdary mt-[12px] mb-[24px]">
                <ItemsWithDivider
                  items={[
                    record?.itemSn,
                    record?.brandName,
                    record?.categoryName,
                    record?.unitName,
                  ]}
                />
              </div>
            </div>
            <div className="flex justify-between items-center">
              {Number(unitAmount) > 0 && <Price price={unitAmount} size="normal" />}
              {ReturnsTypeEnum.ORDER === returnsType && (
                <span className="text-[24px] text-secondary">可退数量 {record.refundableNum}</span>
              )}
              {/* 没有售价不显示 */}
              {OperatorButton}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
