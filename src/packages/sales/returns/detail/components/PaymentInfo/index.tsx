import Card from '@/components/Card';
import { Price, Step, Steps } from '@nutui/nutui-react-taro';
import { AfterSaleRefundDetailRo } from '../../../operation/types/ReturnsAfterSaleDetailEntity';

export interface PaymentInfoProps {
  detail: AfterSaleRefundDetailRo[];
}

const PaymentInfo = (props: PaymentInfoProps) => {
  const { detail } = props;
  return (
    <Card title="结算记录">
      <Steps direction="vertical" dot value={-1}>
        {detail.map((item) => (
          <Step
            title={
              <div className="flex items-center gap-[12px] text-main text-[32px]">
                <span>{item.accountName}</span>
                <span>支付</span>
                <Price className="!text-main" size="normal" price={item.refundAmount} />
              </div>
            }
            description={<div className="text-thirdary">{item.refundTime}</div>}
          />
        ))}
      </Steps>
    </Card>
  );
};

export default PaymentInfo;
