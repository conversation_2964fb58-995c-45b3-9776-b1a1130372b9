import ItemsWithDivider from '@/components/ItemsWithDivider';
import { Price, Space } from '@nutui/nutui-react-taro';
import classNames from 'classnames';
import _ from 'lodash';
import { AfterSaleOrderGoodsRo } from '../../../operation/types/ReturnsAfterSaleDetailEntity';

export interface GoodsItemProps {
  goods: AfterSaleOrderGoodsRo[];
}

const GoodsItemForOrder = (props: GoodsItemProps) => {
  const { goods = [] } = props;

  return goods.map((item, index) => {
    const borderClass = classNames('flex border-0 pb-[24px]', {
      'border-t border-solid border-t-[#0000001A] pt-[24px]': index > 0,
    });
    return (
      <div className={borderClass}>
        <img
          src={item.images?.[0]}
          className="rounded-[8px] mr-[20px] bg-[#f5f5f5] w-[88px] h-[88px]"
        />
        <div className="flex-1">
          <div>{item.itemName}</div>
          <div className="flex text-[24px] text-thirdary my-[12px]">
            <ItemsWithDivider
              items={[item?.itemSn, item?.brandName, item?.categoryName, item?.unitName]}
            />
          </div>
          <div className="flex justify-between mt-[24px]">
            <Space className="text-secondary">
              <Price className="!text-secondary" price={item.unitAmount} size="normal" />
              <span className="flex ml-[28px]">
                <span>x</span>
                {item.refundNum}
              </span>
            </Space>
            <Price
              size="normal"
              className="!text-main"
              price={_.multiply(Number(item.unitAmount), item.refundNum)}
            />
          </div>
        </div>
      </div>
    );
  });
};

export default GoodsItemForOrder;
