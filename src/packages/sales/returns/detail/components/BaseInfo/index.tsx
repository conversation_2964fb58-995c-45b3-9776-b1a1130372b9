import Card from '@/components/Card';
import DetailList from '@/components/DetailList';
import { Price } from '@nutui/nutui-react-taro';
import { defaultTo } from 'lodash';
import { AfterSaleOrderRo } from '../../../operation/types/ReturnsAfterSaleDetailEntity';

export interface BaseInfoProps {
  detail: AfterSaleOrderRo;
}

const BaseInfo = (props: BaseInfoProps) => {
  const {
    detail: { main, status },
  } = props;

  const data = [
    {
      label: '退款金额',
      value: <Price className="!text-gray-900" size="normal" price={main.orderAmount} />,
    },
    {
      label: '结算方式',
      value: status.refundStatusName,
    },
    {
      label: '退货门店',
      value: main.storeName,
    },
    {
      label: '收货仓库',
      value: main.backWarehouseName,
    },
    {
      label: '下单时间',
      value: main.orderCreateTime,
    },

    {
      label: '制单人',
      value: main.salesmanName,
    },
    {
      label: '备注',
      value: defaultTo(main.remark, '-'),
    },
  ];

  return (
    <Card>
      <DetailList className="leading-[64px]" dataSource={data} colon={true} />
    </Card>
  );
};

export default BaseInfo;
