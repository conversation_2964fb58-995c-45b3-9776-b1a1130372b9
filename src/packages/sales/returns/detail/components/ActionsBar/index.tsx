import usePermissionStore from '@/pages/splash/permissionStore';
import RouterUtils from '@/utils/RouterUtils';
import { Button, DialogProps, SafeArea, Space } from '@nutui/nutui-react-taro';
import { useState } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { OrderStatusEnum, RefundStatusEnum } from '../../../list/components/OrderTags';
import { cancelOrder, directIn, drawOrder } from '../../../operation/services';
import { AfterSaleOrderRo } from '../../../operation/types/ReturnsAfterSaleDetailEntity';
import ConfirmDialog from '../ConfirmDialog';
import SettleDialog from '../SettleDialog';

export interface ActionsBarProps {
  detail: AfterSaleOrderRo;
  onRefresh: () => void;
}
enum OperTypeEnum {
  /**撤回 */
  CANCEL = '0',
  /**作废 */
  DROP = '1',
  /**入库 */
  ENTER_STOCK = '2',
}

const ActionsBar = (props: ActionsBarProps) => {
  const { detail, onRefresh } = props;
  const { hasPermission } = usePermissionStore(
    useShallow((store) => ({
      hasPermission: store.hasPermission,
    })),
  );
  const [confirmDialogProps, setConfirmDialogProps] = useState<
    Partial<DialogProps> & { operType: OperTypeEnum }
  >({
    visible: false,
    operType: OperTypeEnum.CANCEL,
  });
  const [showSettle, setShowSettle] = useState(false);
  const { status, main } = detail;
  const { id: orderId, orderNo } = main;
  const params = { orderId: orderId!, orderNo: orderNo! };
  const onCancel = () => {
    setConfirmDialogProps((pre) => ({ ...pre, visible: false }));
  };
  /**刷新数据 */
  const reload = () => {
    onRefresh();
    onCancel();
  };
  /**
   * 确认【作废】
   */
  const onCancelOrder = async () => {
    const result = await cancelOrder(params);
    if (result) {
      reload();
    }
  };
  /**
   * 确认【撤回】
   */
  const onDrawOrder = async () => {
    const result = await drawOrder(params);
    if (result) {
      reload();
    }
  };
  /**
   * 确认【一键入库】
   */
  const onDirectIn = async () => {
    const result = await directIn(params);
    if (result) {
      reload();
    }
  };

  const onConfirm = () => {
    switch (confirmDialogProps.operType) {
      case OperTypeEnum.CANCEL:
        onDrawOrder();
        break;
      case OperTypeEnum.DROP:
        onCancelOrder();
        break;
      case OperTypeEnum.ENTER_STOCK:
        onDirectIn();
        break;
      default:
        break;
    }
  };

  return (
    <div className="fixed left-0 right-0 bottom-0 bg-white z-20">
      <Space className="px-[28px] py-[24px] empty:hidden justify-end">
        {hasPermission('editSaleReturn') &&
          [OrderStatusEnum.DRAFT].includes(status?.orderStatus) && (
            <Button
              onClick={() => {
                console.log(params);
                RouterUtils.navigateTo({
                  url: '/packages/sales/returns/operation/index',
                  params: {
                    orderId,
                    orderNo,
                  },
                });
              }}
            >
              编辑
            </Button>
          )}
        {hasPermission('withdrawSaleReturn') &&
          [OrderStatusEnum.TO_IN].includes(status.orderStatus!) && (
            <Button
              onClick={() => {
                setConfirmDialogProps((pre) => ({
                  ...pre,
                  visible: true,
                  title: '是否确定撤回订单',
                  confirmText: '撤回',
                  operType: OperTypeEnum.CANCEL,
                }));
              }}
            >
              撤回
            </Button>
          )}
        {hasPermission('deleteSaleReturn') &&
          [OrderStatusEnum.TO_IN, OrderStatusEnum.DRAFT].includes(status.orderStatus!) && (
            <Button
              onClick={() => {
                setConfirmDialogProps((pre) => ({
                  ...pre,
                  visible: true,
                  title: '是否确定作废订单',
                  confirmText: '作废',
                  operType: OperTypeEnum.DROP,
                }));
              }}
            >
              作废
            </Button>
          )}
        {hasPermission('saleReturnInWareHouse') &&
          [OrderStatusEnum.TO_IN].includes(status.orderStatus!) && (
            <Button
              type="primary"
              onClick={() => {
                setConfirmDialogProps((pre) => ({
                  ...pre,
                  visible: true,
                  title: '是否确定一键入库',
                  confirmText: '入库',
                  operType: OperTypeEnum.ENTER_STOCK,
                }));
              }}
            >
              一键入库
            </Button>
          )}
        {hasPermission('saleReturnInWareHouse') &&
          [RefundStatusEnum.NOT_SETTLED].includes(status.refundStatus) &&
          [OrderStatusEnum.HAS_IN, OrderStatusEnum.TO_IN].includes(status.orderStatus) && (
            <Button type="primary" onClick={() => setShowSettle(true)}>
              确认结算
            </Button>
          )}
      </Space>
      <SettleDialog
        orderId={orderId!}
        orderNo={orderNo!}
        visible={showSettle}
        storeId={detail.main.storeId}
        totalAmount={detail.main.orderAmount}
        cstId={detail.main.cstId}
        defaultRefundType={detail.refunds?.[0].refundType}
        defaultRefundDetails={detail.refundDetails}
        onSuccess={onRefresh}
        onClose={() => {
          setShowSettle(false);
        }}
      />
      <SafeArea position="bottom" />
      <ConfirmDialog
        onCancel={onCancel}
        onConfirm={onConfirm}
        {...confirmDialogProps}
      ></ConfirmDialog>
    </div>
  );
};

export default ActionsBar;
