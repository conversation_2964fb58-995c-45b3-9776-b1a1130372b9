import { ConfigProvider, Dialog, DialogProps } from '@nutui/nutui-react-taro';
export default (props: Partial<DialogProps>) => {
  const { visible, onConfirm, onCancel, title, cancelText = '取消', confirmText } = props;
  const footer = (
    <div className="flex flex-row justify-evenly items-center px-[16px]">
      <span
        className="text-[32px] leading-[44px] text-[#111111]  p-[16px]"
        onClick={() => onCancel?.()}
      >
        {cancelText}
      </span>
      <span className="w-[1px] bg-[#0000001A] h-[112px]"></span>
      <span
        className=" text-[32px] leading-[44px] text-[#F49C1FFF] p-[16px]"
        onClick={() => onConfirm?.()}
      >
        {confirmText}
      </span>
    </div>
  );
  return (
    <ConfigProvider
      theme={{
        nutuiDialogPadding: '0px 0px',
        nutuiDialogContentMargin: '0px 0px',
      }}
    >
      <Dialog
        visible={visible}
        onConfirm={onConfirm}
        onCancel={onCancel}
        title={null}
        footer={null}
      >
        <div className="flex justify-center pt-[64px] pb-[72px] shadow-sm">
          <span className="text-[32px] leading-[44px] text-[#111111] font-semibold">{title}</span>
        </div>
        {footer}
      </Dialog>
    </ConfigProvider>
  );
};
