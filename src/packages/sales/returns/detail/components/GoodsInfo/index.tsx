import GoodsCard from '@/components/GoodsCard';
import { Divider, Price, Space } from '@nutui/nutui-react-taro';
import { multiply, sumBy } from 'lodash';
import { AfterSaleOrderGoodsRo } from '../../../operation/types/ReturnsAfterSaleDetailEntity';
import GoodsItemForOrder from '../GoodsItemForOrder';

export interface GoodsInfoProps {
  detail: AfterSaleOrderGoodsRo[];
}

const GoodsInfo = (props: GoodsInfoProps) => {
  const { detail } = props;

  /**
   * 计算总价
   */
  const getTotalPrice = () => {
    return sumBy(detail, (t) => multiply((t?.unitAmount ?? 0) * 100, t.refundNum ?? 1) / 100);
  };

  /**
   * 计算总数
   */
  const getTotalNumber = () => {
    return sumBy(detail, 'refundNum');
  };

  return (
    <GoodsCard title={<span className="px-[28px]">商品明细</span>}>
      <div className="px-[28px]">
        <GoodsItemForOrder goods={detail} />
      </div>
      <Divider />
      <div className="pt-[24px] px-[28px]">
        <Space className="flex items-center justify-end gap-[24px]">
          <span className="text-main">共{getTotalNumber()}件</span>
          <span className="flex items-center">
            <span className="text-main">退款</span>
            <Price className="!text-main" size="normal" price={getTotalPrice()} />
          </span>
        </Space>
      </div>
    </GoodsCard>
  );
};

export default GoodsInfo;
