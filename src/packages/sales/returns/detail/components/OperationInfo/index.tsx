import Card from '@/components/Card';
import { Step, Steps } from '@nutui/nutui-react-taro';
import { AfterSaleOrderTimeRo } from '../../../operation/types/ReturnsAfterSaleDetailEntity';

export interface DeliveryInfoProps {
  detail: AfterSaleOrderTimeRo[];
}

const OperationInfo = (props: DeliveryInfoProps) => {
  const { detail } = props;
  return (
    <Card title="操作记录">
      <Steps direction="vertical" dot value={-1}>
        {detail.map((item) => (
          <Step
            title={item.timeTypeName}
            description={
              <div className="flex justify-between text-thirdary">
                <span>{item.time}</span>
                <span>操作人: {item.operatorName}</span>
              </div>
            }
          />
        ))}
      </Steps>
    </Card>
  );
};

export default OperationInfo;
