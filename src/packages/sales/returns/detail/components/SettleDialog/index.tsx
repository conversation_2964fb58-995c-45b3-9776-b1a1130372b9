import iconRemove from '@/assets/icons/icon_remove.png';
import { queryMemberAccountPage } from '@/components/AccountSelectModal/services';
import { getCstDetail } from '@/components/ChooseCstModal/services';
import CustomPicker from '@/components/CustomPicker';
import { SettleProps } from '@/packages/sales/order/edit/components/Settle';
import { Add, ArrowDown } from '@nutui/icons-react-taro';
import {
  Button,
  ConfigProvider,
  Image,
  Input,
  Popup,
  Price,
  SafeArea,
} from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import { useAsyncEffect } from 'ahooks';
import classnames from 'classnames';
import { defaultTo, find, isEmpty, set, uniqueId } from 'lodash';
import { useEffect, useState } from 'react';
import { confirmRefund, modifyOrder } from '../../../operation/services';
import { DefautlOptionType } from '../../../operation/types/DefaultOptionType';
import { RefundTypeEnum, ReturnsModifyRefundDetail } from '../../../operation/types/RefundTypeEnum';
import { ReturnsItemModifyType } from '../../../operation/types/ReturnsCreateItemType';

export interface SettleDialogProps extends SettleProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  orderId: string;
  orderNo: string;
  // 门店ID
  storeId: string;
  // 客户ID
  cstId: string;
  // 默认结算方式
  defaultRefundType: RefundTypeEnum;
  // 结算金额
  totalAmount: number;
  // 默认配置的现款账户信息
  defaultRefundDetails: ReturnsModifyRefundDetail[];
}

export default (props: SettleDialogProps) => {
  const {
    orderId,
    orderNo,
    visible,
    storeId,
    totalAmount,
    cstId,
    defaultRefundType,
    defaultRefundDetails,
    onClose,
    onSuccess,
  } = props;
  const [refundType, setRefundType] = useState<RefundTypeEnum>(RefundTypeEnum.Cash);
  const [refundDetails, setRefundDetails] = useState<ReturnsModifyRefundDetail[]>([]);
  // 设置默认值
  useEffect(() => {
    if (visible) {
      // 设置默认结算方式
      if (defaultRefundType) {
        setRefundType(defaultRefundType);
      }
      // 如果现款结算填入默认的结算信息
      if (defaultRefundType == RefundTypeEnum.Cash) {
        setRefundDetails(defaultRefundDetails);
      }
    } else {
      setRefundDetails([]);
      setAccountList([]);
      setDefaultAccount(undefined);
      setVisibleAccount(false);
      setIsSupportCredit(false);
      setRefundType(RefundTypeEnum.Cash);
      setAccountIndex(0);
      setCreditInfo({
        usedAmount: '-',
        availableAmount: '-',
      });
    }
  }, [visible, defaultRefundType, defaultRefundDetails]);

  // 标记编辑的是那条账户信息
  const [accountIndex, setAccountIndex] = useState<number>(0);
  // 加载账户列账户列表
  const [accountList, setAccountList] = useState<DefautlOptionType[]>([]);
  // 设置默认支付账户
  const [defaultAccount, setDefaultAccount] = useState<DefautlOptionType>();
  const [visibleAccount, setVisibleAccount] = useState<boolean>(false);
  useAsyncEffect(async () => {
    if (visible && storeId) {
      const result = await queryMemberAccountPage({
        belongToStore: [storeId],
      });
      if (result?.data) {
        const accList = result.data.map((item) => ({
          title: item.memberAccountName,
          value: item.id,
        }));
        setAccountList(accList);
        setDefaultAccount(accList[0]);
      }
    }
  }, [visible, storeId]);
  /**客户是否支持挂账 */
  const [isSupportCredit, setIsSupportCredit] = useState<boolean>(false);
  // 客户挂账额度
  const [creditInfo, setCreditInfo] = useState<{
    usedAmount: number | '-';
    availableAmount: number | '-';
  }>({
    usedAmount: '-',
    availableAmount: '-',
  });
  useAsyncEffect(async () => {
    if (visible && cstId) {
      const customerDetail = await getCstDetail({
        cstId,
      });
      const credit = customerDetail?.settle?.credit;
      const usedAmount = customerDetail?.settle?.usedAmount ?? 0;
      const availableAmount = customerDetail?.settle?.availableAmount ?? 0;
      if (credit) {
        setIsSupportCredit(true);
        setCreditInfo({
          usedAmount,
          availableAmount,
        });
      } else {
        setIsSupportCredit(false);
      }
    }
  }, [visible, cstId]);

  // 现款
  const defaultSelectedClass = 'bg-[#FFF4F4FF] text-primary';
  const defaultUnSelectedClass = 'bg-[#********] text-main';
  const defaultSettletypeClass = 'px-[71px] py-[16px] rounded-[8px]';
  const cashClass = classnames(defaultSettletypeClass, {
    [defaultSelectedClass]: refundType === RefundTypeEnum.Cash,
    [defaultUnSelectedClass]: refundType === RefundTypeEnum.Account,
  });
  // 挂账
  const accountClass = classnames(defaultSettletypeClass, {
    [defaultSelectedClass]: refundType === RefundTypeEnum.Account,
    [defaultUnSelectedClass]: refundType === RefundTypeEnum.Cash,
  });

  /**
   * 切换结算方式
   */
  const onRefundTypeChange = async (refundType: RefundTypeEnum) => {
    setRefundType(refundType);
    let array: ReturnsModifyRefundDetail[] = [];
    if (refundType == RefundTypeEnum.Account) {
      array = [];
    } else if (refundType == RefundTypeEnum.Cash) {
      // 现款填入默认账户
      array.push({
        accountId: defaultAccount?.value,
        refundAmount: totalAmount,
        accountName: defaultAccount?.title,
      });
    }
    setRefundDetails(array);
  };

  /**新增结算信息*/
  const addRefundDetail = () => {
    setRefundDetails((pre) => [...pre, { key: uniqueId() }]);
  };

  /**修改结算信息*/
  const updateRefundDetail = (index, newDetail) => {
    const array = [...refundDetails];
    array.splice(index, 1, newDetail);
    setRefundDetails(array);
  };
  /** 删除结算信息*/
  const removeRefundDetail = (index) => {
    const array = [...refundDetails];
    array.splice(index, 1);
    setRefundDetails(array);
  };
  const getSelectedAccountId = () => {
    const selectedIdArray: string[] = [];
    const accountIdArray = refundDetails.map((t) => t.accountId);
    const selectId = accountIdArray[accountIndex];
    if (selectId) {
      selectedIdArray.push(selectId);
    }
    return selectedIdArray;
  };

  const checkParams = () => {
    const array = refundDetails;
    const [first, second] = array
    let res = true;
    // 现款结算
    if (refundType == RefundTypeEnum.Cash) {
      if ((first && !first.accountId) || (second && !second.accountId)) {
        Taro.showToast({ title: '请选择账户', icon: 'none' });
        res = false;
      } else if ((first?.accountId && second?.accountId) && first.accountId == second.accountId) {
        Taro.showToast({ title: '账户重复，请重新选择', icon: 'none' });
        res = false;
      } else if ((first && !first?.refundAmount) || (second && !second?.refundAmount)) {
        Taro.showToast({ title: '请输入合法的金额', icon: 'none' });
        res = false;
      }
    }
    return res;
  };
  /** 确定 */
  const handleSubmit = async () => {
    if (!checkParams()) return;
    const reqParams: ReturnsItemModifyType = {
      orderId,
      orderNo,
      refund: {
        refundType,
      },
    };
    let array: ReturnsModifyRefundDetail[] = [];
    if (refundType == RefundTypeEnum.Account) {
      array = [];
    } else if (refundType == RefundTypeEnum.Cash) {
      array = refundDetails.map((t) => ({
        accountId: t.accountId,
        refundAmount: t.refundAmount,
      }));
    }
    set(reqParams, 'refund.refundDetails', array);
    const result = await modifyOrder(reqParams);
    if (result) {
      const res = await confirmRefund({ orderId, orderNo });
      if (res) {
        onClose();
        onSuccess();
      }
    }
  };
  return (
    <Popup
      lockScroll
      visible={visible}
      title="结算方式"
      onClose={onClose}
      position="bottom"
      closeable={true}
    >
      <div className="p-[28px]">
        <div className="flex">
          <span className="mr-[24px] text-secondary flex items-center">结算金额</span>
          <ConfigProvider
            theme={{
              nutuiColorPrimary: '#000000E6',
              nutuiPriceSymbolMediumSize: '24px',
              nutuiPriceIntegerMediumSize: '24px',
              nutuiPriceDecimalMediumSize: '24px',
              nutuiPriceSymbolPaddingRight: '0px',
            }}
          >
            <Price symbol="" price={totalAmount} size="normal" thousands></Price>
          </ConfigProvider>
        </div>
        <div className="mt-[64px] flex justify-start gap-[22px] text-[28px]">
          {/* 是否支持挂账 */}
          {isSupportCredit && (
            <span
              className={accountClass}
              onClick={() => {
                onRefundTypeChange(RefundTypeEnum.Account);
              }}
            >
              挂账
            </span>
          )}
          <span
            className={cashClass}
            onClick={() => {
              onRefundTypeChange(RefundTypeEnum.Cash);
            }}
          >
            现款
          </span>
        </div>
        {/* 挂账 */}
        {isSupportCredit && refundType === RefundTypeEnum.Account && (
          <div className="mt-[24px] flex">
            <span className="text-secondary text-[28px]">
              已用 {creditInfo.usedAmount}/可用 {creditInfo.availableAmount}
            </span>
          </div>
        )}
        {/* 现款 */}
        {refundType === RefundTypeEnum.Cash && (
          <div className="mt-[24px] flex flex-col">
            {refundDetails?.map((t, index) => {
              return (
                <div
                  key={t.key}
                  className={`flex items-center boder-bottom-${index ^ 1} py-[16px]`}
                >
                  <div
                    className="py-[16px] flex justify-between items-center gap-[18px] flex-1"
                    onClick={() => {
                      setAccountIndex(index);
                      setVisibleAccount(true);
                    }}
                  >
                    <span
                      className={`text-[28px]  max-w-[280px] truncate ${isEmpty(t.accountName) ? 'text-disabled' : ''
                        }`}
                    >
                      {defaultTo(t.accountName, '请选择账户')}
                    </span>
                    <ArrowDown color="#0000004D" width={28} height={16}></ArrowDown>
                  </div>
                  <div className="flex flex-row items-center justify-between flex-1">
                    <span className="w-[260px]">
                      <Input
                        type="digit"
                        placeholder="0.00"
                        align="center"
                        maxLength={12}
                        style={{
                          '--nutui-input-color': '#000000E6',
                          '--nutui-input-padding': '3px 0px',
                          '--nutuiInputFontSize': '16px',
                          '--nutuiInputBorderRadius': '2px',
                        }}
                        value={`${t?.refundAmount ?? '0'}`}
                        onChange={(value) => {
                          updateRefundDetail(index, {
                            ...t,
                            refundAmount: value,
                          });
                        }}
                      />
                    </span>
                    <span className="h-[32px] w-[32px]">
                      {(refundDetails?.length ?? 0) > 1 && (
                        <Image
                          src={iconRemove}
                          height="16px"
                          width="16px"
                          onClick={() => {
                            removeRefundDetail(index);
                          }}
                        />
                      )}
                    </span>
                  </div>
                </div>
              );
            })}
            {(refundDetails?.length ?? 0) < 2 && (
              <div
                className="my-[30px] flex justify-center items-center gap-[16px]"
                onClick={() => {
                  addRefundDetail();
                }}
              >
                <Add color="#F49C1FFF" height="14px" width="14px" />
                <span className="text-primary text-[28px]">新增结算方式</span>
              </div>
            )}
          </div>
        )}
        <div className="mt-[80px]">
          <div className="mx-[-28px] mb-[25px] h-[1px]  bg-[#0000001A]"></div>
          <Button block type={'primary'} onClick={handleSubmit}>
            确认
          </Button>
        </div>
      </div>
      <CustomPicker
        items={accountList}
        title="选择账户"
        visible={visibleAccount}
        selected={getSelectedAccountId()}
        onClose={() => {
          setVisibleAccount(false);
        }}
        multiple={false}
        onConfirm={(items) => {
          if (isEmpty(items)) return;
          const selectOption = find(accountList, { value: items[0] });
          if (isEmpty(selectOption)) return;
          updateRefundDetail(accountIndex, {
            accountId: selectOption.value,
            accountName: selectOption.title,
          });
          setVisibleAccount(false);
        }}
      />
      <SafeArea position="bottom" />
    </Popup>
  );
};
