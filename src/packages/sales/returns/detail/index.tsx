import CustomNavBar from '@/components/CustomNavBar';
import { SafeArea, Tabs } from '@nutui/nutui-react-taro';
import { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import OrderTags from '../list/components/OrderTags';
import { getAfterSaleDetail } from '../operation/services';
import { AfterSaleOrderRo } from '../operation/types/ReturnsAfterSaleDetailEntity';
import ActionsBar from './components/ActionsBar';
import BaseInfo from './components/BaseInfo';
import GoodsInfo from './components/GoodsInfo';
import OperationInfo from './components/OperationInfo';
import PaymentInfo from './components/PaymentInfo';

export default function Index() {
  const [detail, setDetail] = useState<AfterSaleOrderRo>();
  const router = useRouter();
  const { orderNo, orderId } = router.params;
  const [saleTrendIndex, setSaleTrendIndex] = useState<Number>(0);
  // const orderId = '10000539';
  // const orderNo = 'TH24061317440011';
  /** 查询退货详情*/
  const queryDetail = async () => {
    if (orderId && orderNo) {
      const result = await getAfterSaleDetail({ orderId, orderNo });
      setDetail(result ?? {});
    }
  };

  useEffect(() => {
    queryDetail();
  }, [orderNo]);

  if (!detail) {
    return null;
  }

  const detailStatusInfo = {
    refundStatus: detail.status.refundStatus,
    orderStatus: detail.status.orderStatus,
    orderStatusName: detail.status.orderStatusName,
    refundStatusName: detail.status.refundStatusName,
  };

  return (
    <div className="pb-[120px]">
      <CustomNavBar title={detail?.status?.orderStatusName} />
      <div className="mx-[52px] mt-[28px] mb-[32px]">
        <div className="text-[34px]">{detail.main?.cstName}</div>
        <div className="text-[28px] mt-[12px] text-secondary flex items-center">
          <span className="mr-[12px]">{detail.main?.orderNo}</span>
          <OrderTags detail={detailStatusInfo} source={'detail'} />
        </div>
      </div>
      <BaseInfo detail={detail} />
      <Tabs
        className="px-[28px]"
        style={{
          '--nutui-tabs-titles-background-color': 'white',
          '--nutui-tabs-titles-item-active-font-weight': 'Medium',
          '--nutui-tabs-titles-item-active-color': 'rgba(0,0,0,0.9)',
          '--nutui-tabs-tab-line-width': '64px',
          '--nutui-tabs-tab-line-color':
            'linear-gradient( 270deg, rgba(252,100,95,0) 0%, #F83431 100%)',
          '--nutui-tabs-tab-line-height': '3px',
          '--nutui-tabs-titles-font-size': '16px',
          '--nutui-tabs-titles-item-color': 'rgba(0,0,0,0.6)',
        }}
        onChange={(value) => {
          setSaleTrendIndex(Number(value));
        }}
        align="left"
      >
        <Tabs.TabPane title="商品明细" />
        <Tabs.TabPane title="结算记录" />
        <Tabs.TabPane title="操作记录" />
      </Tabs>
      {saleTrendIndex == 0 && (
        <div className="px-[28px] mt-[24px]">
          <GoodsInfo detail={detail.goods} />
        </div>
      )}
      {saleTrendIndex == 1 && <PaymentInfo detail={detail.refundDetails} />}
      {saleTrendIndex == 2 && <OperationInfo detail={detail.times} />}
      <ActionsBar detail={detail} onRefresh={queryDetail} />
      <SafeArea position="bottom" />
    </div>
  );
}
