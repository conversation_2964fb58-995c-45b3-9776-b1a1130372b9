import '@/assets/lightPage.scss';
import CalendarRangeCardChoose from '@/components/CalendarRangeCardChoose';
import { queryStore, queryWarehouse } from '@/components/ChooseStoreAndWarehouseModal/services';
import { StoreEntity } from '@/components/ChooseStoreAndWarehouseModal/types/store.entity';
import { WarehouseEntity } from '@/components/ChooseStoreAndWarehouseModal/types/warehouse.entity';
import CustomMultipleChoose from '@/components/CustomMultipleChoose';
import CustomSearchBar from '@/components/CustomSearchBar';
import FilterCustomerPicker from '@/components/FilterCustomerPicker';
import MenuWrap from '@/components/MenuWrap';
import FunPagination from '@/components/Pagination/FunPagination';
import usePermissionStore from '@/pages/splash/permissionStore';
import { Menu, SafeArea } from '@nutui/nutui-react-taro';
import { navigateTo } from '@tarojs/taro';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { useShallow } from 'zustand/react/shallow';
import OrderItem from './components/OderItem';
import { getAfterSaleOrderPaged } from './services';

export default function Index() {
  const [params, setParams] = useState<any>({});
  const cstMenuRef = useRef<any>();
  const storeRef = useRef<any>();
  const orderStatusRef = useRef<any>();
  const orderTimeRef = useRef<any>();
  const [storeIds, setStoreIds] = useState<StoreEntity[]>([]);
  const [backWarehouseIds, setBackWarehouseIds] = useState<WarehouseEntity[]>([]);
  const { hasPermission } = usePermissionStore(
    useShallow((store) => ({
      hasPermission: store.hasPermission,
    })),
  );
  const itemList = [
    { key: 'keyword', name: '商品', scanShow: true, placeholder: '商品名称/编码/OE码' },
    { key: 'orderNo', name: '退货单号', scanShow: false, placeholder: '退货单号' },
  ];

  const storeAndWarehouseFilteredItems = [
    {
      label: '门店',
      keyStr: 'storeIds',
      multiple: true,
      item: storeIds.map((item) => ({ text: item.name, value: item.id })),
    },
    {
      label: '仓库',
      keyStr: 'backWarehouseIds',
      multiple: true,
      item: backWarehouseIds.map((item) => ({ text: item.warehouseName, value: item.warehouseId })),
    },
  ];

  const orderStatusFilteredItems = [
    {
      label: '单据状态',
      keyStr: 'orderStatusList',
      multiple: true,
      item: [
        { text: '草稿', value: 0 },
        { text: '待入库', value: 10 },
        { text: '已入库', value: 20 },
        { text: '已作废', value: 90 },
        { text: '已完成', value: 100 },
      ],
    },
    {
      label: '结算状态',
      keyStr: 'refundStatusList',
      multiple: true,
      item: [
        { text: '未结算', value: 0 },
        { text: '已结算', value: 100 },
      ],
    },
  ];

  useEffect(() => {
    queryStore({}).then((result) => setStoreIds(result));
    queryWarehouse({}).then((result) => setBackWarehouseIds(result));
  }, []);

  const fetchData = (params) => {
    return getAfterSaleOrderPaged({ ...params, pageSize: 10 }).then((result) => result?.data ?? []);
  };

  const setInputValue = (param) => {
    if (!isEmpty(param)) {
      setParams((prevData) => ({ ...prevData, ...param }));
    }
  };

  return (
    <div className="flex flex-col h-screen">
      <div className="px-[28px] pt-[16px] mb-[24px]">
        <CustomSearchBar
          itemList={itemList}
          defaultItem={itemList[0]}
          inputValue={setInputValue}
          isShowAdd={hasPermission('addSaleReturn')}
          addUrl={() => navigateTo({ url: '/packages/sales/returns/operation/index' })}
        />
      </div>
      <MenuWrap
        menu={
          <Menu>
            <Menu.Item title="单据状态" ref={orderStatusRef}>
              <CustomMultipleChoose
                key="orderStatusList"
                onClose={() => {
                  orderStatusRef.current?.toggle(false);
                }}
                selected={{
                  orderStatusList: params.orderStatusList,
                  backWarehouseIds: params.backWarehouseIds,
                }}
                onConfirm={(e) => {
                  setParams({ ...params, ...e });
                }}
                items={orderStatusFilteredItems}
              />
            </Menu.Item>
            <Menu.Item title="客户" ref={cstMenuRef}>
              <FilterCustomerPicker
                cstId={params.cstId}
                onChange={(cstId) => {
                  setParams({ ...params, cstId, cstIds: cstId ? [cstId] : undefined });
                  cstMenuRef.current?.toggle(false);
                }}
              />
            </Menu.Item>
            <Menu.Item title="门店仓库" ref={storeRef}>
              <CustomMultipleChoose
                key={'storeAndWarehouse'}
                onClose={() => {
                  storeRef.current?.toggle(false);
                }}
                selected={{
                  storeIds: params.storeIds,
                  backWarehouseIds: params.backWarehouseIds,
                }}
                onConfirm={(e) => {
                  setParams({ ...params, ...e });
                }}
                items={storeAndWarehouseFilteredItems}
              />
            </Menu.Item>
            <Menu.Item title="下单时间" ref={orderTimeRef}>
              <CalendarRangeCardChoose
                value={
                  params.beginTime
                    ? [dayjs(params.beginTime).toDate(), dayjs(params.endTime).toDate()]
                    : []
                }
                onChange={(value) => {
                  if (value) {
                    setParams({
                      ...params,
                      beginTime: value[0]
                        ? dayjs(value[0]).format('YYYY-MM-DD 00:00:00')
                        : undefined,
                      endTime: value[1] ? dayjs(value[1]).format('YYYY-MM-DD 23:59:59') : undefined,
                    });
                    orderTimeRef.current?.toggle(false);
                  }
                }}
              />
            </Menu.Item>
          </Menu>
        }
      />
      <div className="flex-1 min-h-0 overflow-scroll mt-[24px]">
        <FunPagination
          fetchData={fetchData}
          params={params}
          renderItem={(record, index) => <OrderItem record={record} index={index} />}
        />
      </div>
      <SafeArea position={'bottom'} />
    </div>
  );
}
