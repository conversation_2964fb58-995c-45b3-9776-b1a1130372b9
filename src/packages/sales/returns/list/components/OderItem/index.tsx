import DetailList from '@/components/DetailList';
import RouterUtils from '@/utils/RouterUtils';
import { Divider, Price } from '@nutui/nutui-react-taro';
import classNames from 'classnames';
import { ReturnsTableEntity } from '../../types/ReturnsTableEntity';
import OrderTags from '../OrderTags';

export interface OrderItemProps {
  record: ReturnsTableEntity;
  index: number;
}

const OrderItem = (props: OrderItemProps) => {
  const { record, index } = props;
  const detail = {
    refundStatus: record.refundStatus,
    orderStatus: record.orderStatus,
    orderStatusName: record.orderStatusName,
    refundStatusName: record.refundStatusName,
  };
  return (
    <div
      className={classNames('bg-white rounded-[16px] mt-[24px] mx-[28px] px-[28px] py-[20px]', {
        '!mt-[0]': index === 0,
      })}
      onClick={() => {
        RouterUtils.navigateTo({
          url: '/packages/sales/returns/detail/index',
          params: {
            orderId: record.orderId,
            orderNo: record.orderNo,
          },
        });
      }}
    >
      <div className="flex justify-between pb-[16px]">
        <span>{record.cstName}</span>
        <span className="flex-shrink-0">
          <OrderTags source="list" detail={detail} />
        </span>
      </div>
      <div className="-mx-[28px]">
        <Divider />
      </div>
      <div className="flex mt-[24px]">
        <div className="flex-1 text-secondary text-[28px] leading-6">
          <DetailList
            className="leading-[38px] text-secondary"
            labelWidth="70px"
            dataSource={[
              { label: '退货单号', value: record.orderNo },
              { label: '下单时间', value: record.orderCreateTime },
              { label: '退货门店', value: record.storeName },
              { label: '收货仓库', value: record.backWarehouseName },
            ]}
          />
        </div>
        <div className="flex flex-col justify-center items-end">
          <div>
            <Price className="!text-gray-900" price={record.orderAmount} size="normal" />
          </div>
          <div className="text-[24px] mt-[12px] text-thirdary">{record?.refundTypeName}</div>
        </div>
      </div>
    </div>
  );
};

export default OrderItem;
