import classNames from 'classnames';
import { includes } from 'lodash';
export interface OrderTagsDetailType {
  orderStatus: number;
  orderStatusName: string;
  refundStatus: number;
  refundStatusName: string;
}
export interface OrderTagsProps {
  detail: OrderTagsDetailType;
  source?: 'detail' | 'list';
}
/** 结算状态 */
export enum RefundStatusEnum {
  /** 未结算 */
  NOT_SETTLED = 0,
  /** 已结算 */
  SETTLED = 100,
}
/** 单据状态 */
export enum OrderStatusEnum {
  /**草稿 */
  DRAFT = 0,
  /**待入库 */
  TO_IN = 10,
  /**已入库 */
  HAS_IN = 20,
  /**已作废 */
  CANCEL = 90,
  /**已完成 */
  FINISH = 100,
}
const OrderTags = (props: OrderTagsProps) => {
  const { detail, source } = props;
  const defaultTextClass = 'rounded-[4px] px-[10px] py-1 text-[20px] border-solid border-[1px]';
  const greenClass = 'bg-[#EAF9ECFF] border-[#ADEBB5FF] text-[#33CC47FF]';
  const pinkClass = 'bg-[#FFF4F4] border-[#FCAEADFF] text-[#F49C1F]';
  const greyClass = 'bg-[#00000014] border-[#00000014] text-[#00000073]';
  const orderStatus = detail.orderStatus;
  const refundStatus = detail.refundStatus;

  // 单据状态
  const orderStatusClassname = classNames(defaultTextClass, {
    // 绿色
    [greenClass]: includes([OrderStatusEnum.FINISH, OrderStatusEnum.HAS_IN], orderStatus),
    // 灰色
    [greyClass]: orderStatus == OrderStatusEnum.CANCEL,
    // 红色
    [pinkClass]: includes([OrderStatusEnum.DRAFT, OrderStatusEnum.TO_IN], orderStatus),
  });
  // 草稿或者已作废只展示单据状态不展示结算状态
  const hiddenRefundStatus = !includes(
    [OrderStatusEnum.DRAFT, OrderStatusEnum.CANCEL],
    orderStatus,
  );
  // 结算状态
  const refundStatusClassname = classNames(defaultTextClass, {
    // 绿色
    [greenClass]: refundStatus == RefundStatusEnum.SETTLED,
    // 红色
    [pinkClass]: refundStatus == RefundStatusEnum.NOT_SETTLED,
  });
  return (
    <span className="flex items-center gap-[12px]">
      {source == 'list' && <span className={orderStatusClassname}>{detail.orderStatusName}</span>}
      {hiddenRefundStatus && (
        <span className={refundStatusClassname}>{detail.refundStatusName}</span>
      )}
    </span>
  );
};

export default OrderTags;
