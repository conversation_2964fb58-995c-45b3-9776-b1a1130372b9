import { OrderStatus } from './OrderStatus';
import { PayStatus } from './PayStatus';
import { PaymentStatus } from './PaymentStatus';

export interface OrderListItemEntity {
  /**
   * 订单收货地址
   */
  orderFixedAddressList?: OrderFixedAddressList[];
  /**
   * 订单折扣优惠
   */
  orderFixedDiscountList?: OrderFixedDiscountList[];
  /**
   * 订单配送
   */
  orderFixedDistributionList?: OrderFixedDistributionList[];
  /**
   * 订单发票
   */
  orderFixedReceiptList?: OrderFixedReceiptList[];
  /**
   * 订单商品明细信息
   */
  orderGoodsROList?: OrderGoodsROList[];
  /**
   * 订单id
   */
  orderId: string;
  /**
   * 订单备注
   */
  orderNoteList?: OrderNoteList[];
  /**
   * 订单支付流水
   */
  orderPayDetailList?: OrderPayDetailList[];
  /**
   * 订单支付
   */
  orderPayList?: OrderPayList[];
  /**
   * 订单金额
   */
  orderPrice?: OrderPrice;
  /**
   * 订单信息
   */
  orders?: Orders;
  /**
   * 订单状态
   */
  orderStatus?: OrderStatusObj;
  /**
   * 订单关键时间节点信息
   */
  orderTimeNodeROList?: OrderTimeNodeROList[];
}

export interface OrderFixedAddressList {
  /**
   * 客户地址id
   */
  addressId?: string;
  /**
   * 市编码
   */
  consigneeCityCode?: string;
  /**
   * 市名称
   */
  consigneeCityName?: string;
  /**
   * 详细地址
   */
  consigneeDetail?: string;
  /**
   * 收货人姓名
   */
  consigneeName?: string;
  /**
   * 收货人电话
   */
  consigneePhone?: string;
  /**
   * 区县名称编码
   */
  consigneePrefectureCode?: string;
  /**
   * 区县名称
   */
  consigneePrefectureName?: string;
  /**
   * 省编码
   */
  consigneeProvinceCode?: string;
  /**
   * 省名称
   */
  consigneeProvinceName?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 用户id
   */
  cstId?: string;
  /**
   * 订单地址id
   */
  id?: string;
  /**
   * 地址状态（0：有效、1：作废）
   */
  isDelete?: number;
  /**
   * 订单id
   */
  orderId?: string;
  /**
   * None
   */
  orderNo?: string;
  /**
   * 修改人
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
}

export interface OrderFixedDiscountList {
  /**
   * None
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * None
   */
  cstId?: string;
  /**
   * 优惠起始时间（如果是优惠券存生效时间，如果是活动存活动开始时间）
   */
  discountBeginTime?: string;
  /**
   * 优惠描述（存储优惠券或活动的描述信息）
   */
  discountDesc?: string;
  discountDescYuan?: number;
  /**
   * 优惠起用金额（如果是优惠券为优惠券的起用金额，如果是满减满赠则为达到满减满赠条件金额）
   */
  discountEnableAmount?: number;
  /**
   * 优惠结束时间（如果是优惠券存失效时间，如果是活动存活动结束时间）
   */
  discountEndTime?: string;
  /**
   * 优惠项目id（如果是优惠券存优惠券id，如果是满减满赠活动存活动id）
   */
  discountItemId?: string;
  /**
   * 优惠项目名称（如果是优惠券存优惠券名称，如果是满减满赠活动存活动名称）
   */
  discountItemName?: string;
  /**
   * 优惠方式（1：现金优惠、2：实物优惠），优惠券和满减为现金优惠、满赠为实物优惠
   */
  discountModel?: number;
  /**
   * 优惠方式（1：现金优惠、2：实物优惠），优惠券和满减为现金优惠、满赠为实物优惠
   */
  discountModelName?: string;
  /**
   * 优惠面值（优惠券存优惠券的面值，活动时存活动优惠值，以分为单位）
   */
  discountMoney?: number;
  discountMoneyYuan?: number;
  /**
   * 优惠发放方（1：平台优惠、2：门店优惠）
   */
  discountProvider?: number;
  /**
   * 优惠发放方（1：平台优惠、2：门店优惠）
   */
  discountProviderName?: string;
  /**
   * 实际优惠金额（如果是优惠券为优惠券的使用金额，如果是满减则为满减时扣减的金额，满赠时存0）
   */
  discountRealCost?: number;
  /**
   * 优惠类型（0无折扣1整单折2整单减）
   */
  discountType?: number;
  /**
   *
   * 优惠类型（1：优惠券、2：满减活动、3：满赠活动、4：普通活动，5.满折，6.促销活动，7.虚拟货币，8.结清优惠，9.收银优惠，10.积分抵扣,11:整单打折，12:整单减，13:返利冲减）
   */
  discountTypeName?: string;
  /**
   * None
   */
  id?: string;
  /**
   * None
   */
  orderId?: string;
  /**
   * None
   */
  orderNo?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}

export interface OrderFixedDistributionList {
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * None
   */
  cstId?: string;
  /**
   * 配送方式（1客户自提2商家配送3快递物流）
   */
  distributionMode?: number;
  /**
   * 配送方式（1客户自提2商家配送3快递物流）
   */
  distributionModeName?: string;
  /**
   * 预计送达时间
   */
  estimatedDeliveryTime?: string;
  /**
   * None
   */
  id?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 物流公司编码|id
   */
  logisticsCompanyCode?: string;
  /**
   * 物流公司名称
   */
  logisticsCompanyName?: string;
  /**
   * 物流单号
   */
  logisticsNo?: string;
  /**
   * None
   */
  orderId?: string;
  /**
   * None
   */
  orderNo?: string;
  /**
   * 出库完成时间
   */
  outboundFinishTime?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
  /**
   * 仓库id，存在前端商品已经带有仓属性
   */
  warehouseId?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
}

export interface OrderFixedReceiptList {
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * None
   */
  cstId?: string;
  /**
   * None
   */
  id?: string;
  /**
   * 开票状态（1：未开票、2：已开票）
   */
  makeReceiptStatus?: number;
  /**
   * 开票状态（1：未开票、2：已开票）
   */
  makeReceiptStatusName?: string;
  /**
   * None
   */
  orderId?: string;
  /**
   * None
   */
  orderNo?: string;
  /**
   * 发票id
   */
  receiptId?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
}

export interface OrderGoodsROList {
  /**
   * 商品实收总额(优惠后)（单位：分）购买商品的数量商品单价-优惠分摊总金额
   */
  actualSellingTotalAmount?: number;
  /**
   * 商品实际销售单价（单位：分）（商品实收总额购买商品的数量：先算付款总额，再算实际销售单价，主要用于展示最近几次售价）
   */
  actualSellingUnitPrice?: number;
  /**
   * 商品品牌件号
   */
  avaNum?: number;
  brandPartNo?: string;
  brandName?: string;
  originRegionName?: string;
  spec?: string;
  adaptModel?: string;
  locationCode?: string;
  actualSellingTotalAmountYuan?: number;
  /**
   * 商品类目编码
   */
  categoryId?: string;
  /**
   * 商品类目名称
   */
  categoryName?: string;
  /**
   * 商品成本价（单位：分）
   */
  costPrice?: number;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 客户id
   */
  cstId?: string;
  /**
   * 优惠金额
   */
  discountSum?: number;
  /**
   * None
   */
  id: string;
  /**
   * 是否已删除，0:未删除，1:已删除
   */
  isDelete?: number;
  /**
   * 是否为赠品
   */
  isGift?: number;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  images?: string[];
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * oem码
   */
  oeNo?: string;
  /**
   * None
   */
  orderId?: string;
  /**
   * None
   */
  orderNo?: string;
  /**
   * 商品单价(建议价)，原售价（单位：分）
   */
  origPrice?: number;
  /**
   * 已退数量
   */
  retNum?: number;
  /**
   * 购买数量
   */
  saleNum?: number;
  /**
   * sku
   */
  skuId?: string;
  /**
   * 商品单位
   */
  unitName?: string;
  /**
   * 商品销售价（单位：分）
   */
  unitPrice?: number;
  costPriceYuan?: number;
  unitPriceYuan?: number;
  /**
   * 修改人
   */
  updatePerson?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}

export interface OrderNoteList {
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * None
   */
  cstId?: string;
  /**
   * None
   */
  id?: string;
  /**
   * 备注详情
   */
  noteDetail?: string;
  /**
   * 备注时间
   */
  noteTime?: string;
  /**
   * 备注方（1：客户备注、2：门店对客备注、3：门店对内备注、4：其他5：订单取消原因）
   */
  noteType?: number;
  /**
   * None
   */
  orderId?: string;
  /**
   * None
   */
  orderNo?: string;
  /**
   * 更新人
   */
  updatePerson?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}

export interface OrderPayDetailList {
  /**
   * 第三方支付流水号（支付宝或微信的支付流水号）
   */
  billNo?: string;
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * None
   */
  cstId?: string;
  /**
   * None
   */
  id?: string;
  /**
   * 是否已删除，0:未删除，1:已删除
   */
  isDelete?: number;
  /**
   * None
   */
  orderId?: string;
  /**
   * None
   */
  orderNo?: string;
  /**
   * 支付账号
   */
  payAccount?: string;
  /**
   * 支付通道
   */
  payAisle?: string;
  /**
   * 支付金额
   */
  payAmount?: number;
  payAmountYuan?: number;
  /**
   * 支付渠道0：额度帐期1:现金
   */
  payChannel?: number;
  /**
   * 支付渠道0：额度帐期1:现金
   */
  payChannelName?: string;
  /**
   * 收款账号
   */
  payeeAccount?: string;
  payeeAccountName?: string;
  /**
   * 支付方式(1：现款2挂帐)
   */
  payKind?: number;
  /**
   * 支付方式(1：现款2挂帐)
   */
  payKindName?: string;
  /**
   * 支付编号（支付中心单号）
   */
  payNo?: string;
  /**
   * 支付状态（0：未支付、1：已支付）
   */
  payStatus?: PaymentStatus;
  /**
   * 支付状态（0：未支付、1：已支付）
   */
  payStatusName?: string;
  /**
   * 支付时间
   */
  payTime?: string;
  /**
   * 支付申请编号（非两阶段支付时为订单编号，两阶段实付为【订单编号_序号】）
   */
  prePayNo?: string;
  /**
   * 修改人
   */
  updatePerson?: string;
  /**
   * 修改时间
   */
  updateTime?: string;
}

export interface OrderPayList {
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * None
   */
  cstId?: string;
  /**
   * None
   */
  id?: string;
  /**
   * 是否已删除，0:未删除，1:已删除
   */
  isDelete?: number;
  /**
   * None
   */
  orderId?: string;
  /**
   * None
   */
  orderNo?: string;
  /**
   * 支付时间
   */
  payTime?: string;
  /**
   * 支付类型，1:现款、2:挂帐
   */
  payType?: number;
  /**
   * 支付类型，1:现款、2:挂帐、3：到付
   */
  payTypeName?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
}

/**
 * 订单金额
 */
export interface OrderPrice {
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * None
   */
  cstId?: string;
  /**
   * 配送费
   */
  deliveryAmount?: number;
  deliveryAmountYuan?: number;
  /**
   * None
   */
  isDelete?: number;
  /**
   * None
   */
  orderId?: string;
  /**
   * None
   */
  orderNo?: string;
  /**
   * 应付总额=商品总额+包装+运费-订单优惠总金额
   */
  shouldTotalOrderAmount?: number;
  shouldTotalOrderAmountYuan?: number;
  /**
   * 成本价总额
   */
  totalCostAmount?: number;
  /**
   * 订单优惠总金额=各种优惠总和
   */
  totalDiscountAmount?: number;
  totalDiscountAmountYuan?: number;
  /**
   * 商品总额
   */
  totalGoodsPriceAmount?: number;
  totalGoodsPriceAmountYuan?: number;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
}

/**
 * 订单状态
 */
export interface OrderStatusObj {
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * None
   */
  cstId?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * None
   */
  orderId?: string;
  /**
   * None
   */
  orderNo?: string;
  /**
   * 订单状态'订单状态（90：待处理，300.待出库，400已出库，600交易成功800交易关闭）',
   */
  orderStatus?: OrderStatus;
  /**
   * 订单状态
   */
  orderStatusName?: string;
  /**
   * 收款状态（0待支付，1部分收款，2全部收款）
   */
  paymentStatus?: PaymentStatus;
  /**
   * 收款状态
   */
  paymentStatusName?: string;
  /**
   * 支付状态（0待支付，1部分支付，2全部支付）
   */
  payStatus?: PayStatus;
  /**
   * 订单支付状态
   */
  payStatusName?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
}

export interface OrderTimeNodeROList {
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * None
   */
  cstId?: string;
  /**
   * 当前状态
   */
  currStatus?: number;
  /**
   * None
   */
  id?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 具体时间
   */
  nodeTime?: string;
  /**
   * 操作类型
   */
  operateType?: number;
  /**
   * None
   */
  operateTypeName?: string;
  /**
   * None
   */
  operator?: string;
  /**
   * None
   */
  operatorId?: string;
  /**
   * None
   */
  orderId?: string;
  /**
   * None
   */
  orderNo?: string;
  /**
   * 原状态
   */
  origStatus?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
}

/**
 * 订单信息
 */
export interface Orders {
  /**
   * 创建人id
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 用户id
   */
  cstId?: string;
  /**
   * 买家姓名
   */
  cstName?: string;
  /**
   * 买家手机
   */
  cstPhone?: string;
  /**
   * 0：正常；1：删除
   */
  isDelete?: number;
  /**
   * 零售商id
   */
  memberId?: string;
  /**
   * 零售商名称
   */
  memberName?: string;
  /**
   * 创建时间
   */
  orderCreateTime?: string;
  /**
   * 订单完成时间
   */
  orderFinishTime?: string;
  /**
   * 订单id
   */
  orderId?: string;
  /**
   * 订单业务编号
   */
  orderNo: string;
  /**
   * 订单标签，以标签组的形式存储（以2的n次方表示，支持一个订单n种标记）多个标签组以&分隔，如9223372036854775807&33554431&520
   */
  orderTagFull?: string;
  /**
   * 业务归属人
   */
  salesman?: string;
  /**
   * 业务归属人id
   */
  salesmanId?: string;
  /**
   * 店铺编码
   */
  storeId?: string;
  /**
   * 店铺名称
   */
  storeName?: string;
  /**
   * 第三方订单编号（淘宝、京东、美团等订单编号），非第三方时存内部订单编号
   * 第三方订单编号（淘宝、京东、美团等订单编号），非第三方时存内部订单编号
   */
  thirdOrderNo?: string;
  /**
   * 修改人id
   */
  updatePerson?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}
