export interface ReturnsTableEntity {
  /** 销售单ID */
  orderId: string;
  /** 销售单号 */
  orderNo: string;
  /** 结算状态 */
  refundStatus: number;
  /** 创建时间 */
  orderCreateTime?: string;
  /** 售后单状态 */
  orderStatus: number;
  /** 结算状态名称 */
  refundStatusName: string;
  /** 退货金额 */
  orderAmount?: number;
  /** 结算方式 */
  refundTypeName?: string;
  /** 售后单状态名称 */
  orderStatusName: string;
  /** 结算方式 */
  refundType?: number;
  /** 客户ID */
  cstId?: string;
  /** 客户名称 */
  cstName?: string;
  /** 门店ID */
  storeId?: string;
  /** 门店名称 */
  storeName?: string;
  /** 制单人ID */
  salesmanId?: string;
  /** 退货入库仓ID */
  backWarehouseId?: string;
  /** 制单人名称 */
  salesmanName?: string;
  /** 退货入库仓名称 */
  backWarehouseName?: string;
  /** 备注 */
  remark?: string;
}
