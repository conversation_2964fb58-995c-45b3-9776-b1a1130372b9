export enum PaymentStatus {
  UN_PAY,
  PART_PAY,
  ALL_PAY,
}

export enum PaymentStatusName {
  UN_PAY = '未收款',
  PART_PAY = '部分收款',
  ALL_PAY = '已收款',
}

export const paymentStatusOption = [
  {
    text: PaymentStatusName.UN_PAY,
    value: PaymentStatus.UN_PAY,
  },
  {
    text: PaymentStatusName.PART_PAY,
    value: PaymentStatus.PART_PAY,
  },
  {
    text: PaymentStatusName.ALL_PAY,
    value: PaymentStatus.ALL_PAY,
  },
];

export const paymentStatusMap = {
  [PaymentStatus.UN_PAY]: {
    text: PaymentStatusName.UN_PAY,
    color: 'red',
    status: 'error',
  },
  [PaymentStatus.PART_PAY]: {
    text: PaymentStatusName.PART_PAY,
    color: 'red',
    status: 'error',
  },
  [PaymentStatus.ALL_PAY]: {
    text: PaymentStatusName.ALL_PAY,
    color: 'green',
    status: 'success',
  },
};
