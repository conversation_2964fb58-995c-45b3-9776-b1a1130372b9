interface AfterSaleOrderPageRequestParams {
    beginTime?: Date; // 销售开始时间 (Sales Start Time)
    cstIds?: string[]; // 客户ID (Customer IDs)
    orderNo?: string; // 销售单号 (Sales Order Number)
    backWarehouseIds?: string[]; // 收货仓库 (Receiving Warehouse IDs)
    operatorNo?: string; // 员工 (Operator)
    keyword?: string; // 商品查询关键字 (Product Query Keyword)
    salesmanId?: string; // 制单人 (Creator)
    endTime?: Date; // 销售结束时间 (Sales End Time)
    storeIds?: string[]; // 门店ID (Store IDs)
    orderStatusList?: string[]; // 单据状态 (Order Status List)
}