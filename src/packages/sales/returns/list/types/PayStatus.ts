export enum PayStatus {
  WAIT_TO_PAY = 0,
  PART_PAY = 1,
  ALL_PAY = 2,
  PAYING = 6,
}

export enum PayStatusName {
  WAIT_TO_PAY = '未结算',
  PART_PAY = '部分结算',
  ALL_PAY = '已结算',
  PAYING = '结算中',
}

export const payStatusOption = [
  {
    text: PayStatusName.WAIT_TO_PAY,
    value: PayStatus.WAIT_TO_PAY,
  },
  {
    text: PayStatusName.ALL_PAY,
    value: PayStatus.ALL_PAY,
  },
];

export const payStatusMap = {
  [PayStatus.WAIT_TO_PAY]: {
    text: PayStatusName.WAIT_TO_PAY,
    color: 'red',
    status: 'error',
  },
  [PayStatus.PART_PAY]: {
    text: PayStatusName.PART_PAY,
    color: 'red',
    status: 'error',
  },
  [PayStatus.ALL_PAY]: {
    text: PayStatusName.ALL_PAY,
    color: 'green',
    status: 'success',
  },
  [PayStatus.PAYING]: {
    text: PayStatusName.PAYING,
    color: 'red',
    status: 'error',
  },
};
