import { getCstDetail } from '@/components/ChooseCstModal/services';
import { CustomerSaveEntity } from '@/components/ChooseCstModal/types/CustomerSaveEntity';
import Settle, { SettleProps } from '@/packages/sales/order/edit/components/Settle';
import { PayChannel } from '@/packages/sales/order/edit/components/Settle/types/PayChannel';
import { PayKind } from '@/packages/sales/order/edit/components/Settle/types/PayKind';
import { confirmPay } from '@/packages/sales/order/edit/services';
import { ConfirmPayRequest } from '@/packages/sales/order/edit/types/confirm.pay.request';
import { UpdateOrderPayKindRequest } from '@/packages/sales/order/edit/types/update.order.pay.kind.request';
import { Button, Popup, Price, SafeArea } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import _ from 'lodash';
import { useEffect, useState } from 'react';

export interface SettleDialogProps extends SettleProps {
  visible: boolean;
  onClose: () => void;
}

const SettleDialog = (props: SettleDialogProps) => {
  const { visible, onClose, ...rest } = props;
  const [cstDetail, setCstDetail] = useState<CustomerSaveEntity>();
  const [data, setData] = useState<UpdateOrderPayKindRequest>();

  useEffect(() => {
    if (props.orderDetail) {
      // @ts-ignore
      getCstDetail({ cstId: props.orderDetail?.orders?.cstId }).then((result) => {
        setCstDetail(result);
      });
    }
  }, [props.orderDetail]);

  console.log('SettleDialog => data', data);

  const handleSubmit = () => {
    if (data?.payKind === PayKind.Cash) {
      let total = 0;
      let hasEmpty = false;
      data?.payDetailList?.forEach((item: any) => {
        if (item?.payAmount) {
          total = _.add(total, Number(item.payAmount));
        }
        if (!item?.payAmount || !item?.payeeAcount) {
          hasEmpty = true;
        }
      });
      if (hasEmpty) {
        Taro.showToast({ title: `请填写完整信息`, icon: 'none' });
        return;
      }
      console.log('data?.payDetailList', data?.payDetailList);
      if (total != props.orderDetail?.orderPrice?.shouldTotalOrderAmountYuan) {
        Taro.showToast({ title: `支付金额不等于订单应付金额`, icon: 'none' });
        return;
      }
    }
    const params = {
      orderId: props.orderDetail?.orderId,
      payKind: data?.payKind,
    } as ConfirmPayRequest;
    switch (data?.payKind) {
      case PayKind.Credit:
        params.payDetailList = [
          {
            payAmount: props.orderDetail?.orderPrice?.shouldTotalOrderAmountYuan,
            payChannel: PayChannel.PAYMENT_DAYS,
          },
        ];
        break;
      case PayKind.Cash:
        params.payDetailList = data?.payDetailList?.map((item) => ({
          payeeAcount: item.payeeAcount,
          payAmount: item.payAmount,
          payChannel: PayChannel.CASH,
        }));
        break;
    }
    confirmPay(params).then((result) => {
      if (result) {
        Taro.showToast({ title: `结算成功`, icon: 'none' });
        onClose?.();
        props.onRefresh?.();
      }
    });
  };

  return (
    <Popup visible={visible} title="确认结算" onClose={onClose} position="bottom" closeable={true}>
      <div className="p-[28px]">
        <div className="flex">
          <span className="font-medium mr-[24px]">结算金额</span>
          <Price size="normal" price={props.orderDetail?.orderPrice?.shouldTotalOrderAmountYuan} />
        </div>
        <Settle {...rest} cstDetail={cstDetail} className="!m-[0] !px-[0]" onChange={setData} />
        <div className="flex gap-[24px]">
          <Button className="flex-1" onClick={onClose}>
            取消
          </Button>
          <Button className="flex-1" type={'primary'} onClick={handleSubmit}>
            确认
          </Button>
        </div>
      </div>
      <SafeArea position={'bottom'} />
    </Popup>
  );
};

export default SettleDialog;
