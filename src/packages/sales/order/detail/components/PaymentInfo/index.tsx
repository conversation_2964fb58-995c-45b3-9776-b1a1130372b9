import Card from '@/components/Card';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { Step, Steps } from '@nutui/nutui-react-taro';

export interface PaymentInfoProps {
  detail: OrderListItemEntity;
}

const PaymentInfo = (props: PaymentInfoProps) => {
  const { detail } = props;
  return (
    <Card title="结算信息">
      <Steps direction="vertical" dot value={-1}>
        {detail?.orderPayDetailList?.map((item) => (
          <Step
            title={`${item.payeeAccountName ?? item.payKindName} 支付 ${item.payAmountYuan}`}
            description={<div className="text-thirdary">{item.payTime}</div>}
          />
        ))}
      </Steps>
    </Card>
  );
};

export default PaymentInfo;
