import { OrderStatus } from '@/packages/sales/order/list/types/OrderStatus';
import { PayStatus } from '@/packages/sales/order/list/types/PayStatus';
import { PaymentStatus } from '@/packages/sales/order/list/types/PaymentStatus';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { Tag } from '@nutui/nutui-react-taro';

export interface OrderTagsProps {
  detail: OrderListItemEntity;
  source?: 'detail' | 'list';
}

const OrderTags = (props: OrderTagsProps) => {
  const { detail, source = 'list' } = props;

  const getOrderStatus = () => {
    switch (detail.orderStatus?.orderStatus!) {
      case OrderStatus.OUTBOUND_FINISH:
      case OrderStatus.TRADE_SUCCESS:
        return 'success';
      case OrderStatus.TRADE_CLOSE:
        return 'default';
      default:
        return 'primary';
    }
  };

  return (
    <div className="flex gap-x-[12px]">
      {source !== 'detail' && (
        <Tag type={getOrderStatus()}>{detail.orderStatus?.orderStatusName}</Tag>
      )}
      {![OrderStatus.TRADE_CLOSE, OrderStatus.WAIT_TO_HANDLE].includes(
        detail.orderStatus?.orderStatus!,
      ) && (
        <>
          <Tag type={detail.orderStatus?.payStatus === PayStatus.ALL_PAY ? 'success' : 'primary'}>
            {detail.orderStatus?.payStatusName}
          </Tag>
          <Tag
            type={
              detail?.orderStatus?.paymentStatus === PaymentStatus.ALL_PAY ? 'success' : 'primary'
            }
          >
            {detail.orderStatus?.paymentStatusName}
          </Tag>
        </>
      )}
      {detail?.orders?.orderTagList?.includes(2) && <Tag type="warning">紧急</Tag>}
    </div>
  );
};

export default OrderTags;
