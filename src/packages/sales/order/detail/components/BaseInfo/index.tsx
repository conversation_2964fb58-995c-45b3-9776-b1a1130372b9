import Card from '@/components/Card';
import DetailList from '@/components/DetailList';
import { RemarkType } from '@/packages/sales/order/edit/types/update.order.remark.request';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import dayjs from 'dayjs';

export interface BaseInfoProps {
  detail: OrderListItemEntity;
}

const BaseInfo = (props: BaseInfoProps) => {
  const { detail } = props;

  const data = [
    {
      label: '销售金额',
      value: detail.orderPrice?.shouldTotalOrderAmountYuan,
    },
    {
      label: '结算方式',
      value: detail.orderPayDetailList?.[0].payKindName,
    },
    {
      label: '销售门店',
      value: detail.orders?.storeName,
    },
    {
      label: '发货仓库',
      value: detail.orderFixedDistributionList?.[0]?.warehouseName,
    },
    {
      label: '下单时间',
      value: detail.orders?.orderCreateTime,
    },
    {
      label: '期望送达时间',
      value: detail.orderFixedDistributionList?.[0]?.estimatedDeliveryTime
        ? dayjs(detail.orderFixedDistributionList?.[0]?.estimatedDeliveryTime).format('YYYY-MM-DD')
        : '',
    },
    {
      label: '制单人',
      value: detail.orders?.salesman,
    },
    {
      label: 'Reference No',
      value: detail.orders?.referenceNo,
    },
    {
      label: '销售备注',
      value: detail?.orderNoteList?.find((item) => item.noteType === RemarkType.StoreToCustomer)
        ?.noteDetail,
    },
    {
      label: '内部备注',
      value: detail?.orderNoteList?.find((item) => item.noteType === RemarkType.StoreToInner)
        ?.noteDetail,
    },
  ];

  return (
    <Card>
      <DetailList dataSource={data} colon={true} />
    </Card>
  );
};

export default BaseInfo;
