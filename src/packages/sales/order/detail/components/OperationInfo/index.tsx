import Card from '@/components/Card';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { Step, Steps } from '@nutui/nutui-react-taro';

export interface DeliveryInfoProps {
  detail: OrderListItemEntity;
}

const OperationInfo = (props: DeliveryInfoProps) => {
  const { detail } = props;
  return (
    <Card title="操作记录">
      <Steps direction="vertical" dot value={-1}>
        {detail?.orderTimeNodeROList?.map((item) => (
          <Step
            title={item.operateTypeName}
            description={
              <div className="flex justify-between text-thirdary">
                <span>{item.nodeTime}</span>
                <span>操作人: {item.operator}</span>
              </div>
            }
          />
        ))}
      </Steps>
    </Card>
  );
};

export default OperationInfo;
