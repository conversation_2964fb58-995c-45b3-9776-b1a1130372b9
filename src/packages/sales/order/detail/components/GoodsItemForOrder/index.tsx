import { OrderGoodsROList } from '@/packages/sales/order/list/types/order.list.item.entity';
import { Divider, Price, Space } from '@nutui/nutui-react-taro';
import _ from 'lodash';

export interface GoodsItemForOrderProps {
  goods?: OrderGoodsROList[];
}

const GoodsItemForOrder = (props: GoodsItemForOrderProps) => {
  const { goods = [] } = props;

  if (goods.length === 0) {
    return <div className="py-[40px] flex justify-center text-thirdary">暂无商品</div>;
  }

  return goods.map((item, index) => (
    <>
      <div className="flex py-[24px]">
        <img
          src={item.images?.[0]}
          className="rounded-[8px] mr-[20px] bg-[#f5f5f5] w-[88px] h-[88px]"
        />
        <div className="flex-1">
          <div>{item.itemName}</div>
          <div className="text-[24px] text-thirdary my-[12px]">
            {item.itemSn}
            <Divider direction="vertical" />
            {item.brandName}
            <Divider direction="vertical" />
            {item.categoryName}
            <Divider direction="vertical" />
            {item.unitName}
          </div>
          <div className="flex justify-between mt-[24px]">
            <Space className="text-secondary">
              <Price className="!text-secondary" price={item.unitPriceYuan} size="normal" />
              <span className="flex ml-[28px]">
                <span>x</span>
                {item.saleNum}
              </span>
            </Space>
            <Price
              size="normal"
              className="!text-gray-900"
              price={_.multiply(item.unitPriceYuan ?? 0, item.saleNum ?? 0)}
            />
          </div>
        </div>
      </div>
      {index + 1 !== goods.length && <Divider />}
    </>
  ));
};

export default GoodsItemForOrder;
