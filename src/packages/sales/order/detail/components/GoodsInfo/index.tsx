import Card from '@/components/Card';
import GoodsItemForOrder from '@/packages/sales/order/detail/components/GoodsItemForOrder';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { Divider, Price, Space } from '@nutui/nutui-react-taro';

export interface GoodsInfoProps {
  detail: OrderListItemEntity;
}

const GoodsInfo = (props: GoodsInfoProps) => {
  const { detail } = props;

  const getSaleNum = () => {
    let num = 0;
    detail?.orderGoodsROList?.forEach((item) => {
      num += item?.saleNum ?? 0;
    });
    return num;
  };

  return (
    <Card title="商品明细">
      <GoodsItemForOrder goods={detail?.orderGoodsROList} />
      <div className="-mx-[28px]">
        <Divider />
      </div>
      <div className="mt-[24px]">
        <Space className="flex items-center justify-end">
          <span className="mr-[24px]">共{getSaleNum()}件</span>
          <span className="flex items-center">
            合计
            <Price
              className="!text-gray-900"
              size="normal"
              price={detail?.orderPrice?.shouldTotalOrderAmountYuan}
            />
          </span>
        </Space>
        <div className="flex items-center flex-wrap gap-y-1.5 gap-x-[24px] justify-end text-thirdary text-[24px] mt-[12px]">
          <span>
            商品总金额
            <Price
              className="!text-secondary"
              price={detail?.orderPrice?.totalGoodsPriceAmountYuan}
              size="normal"
            />
          </span>
          <span>
            优惠金额
            <Price
              className="!text-secondary"
              price={detail?.orderPrice?.totalDiscountAmountYuan}
              size="normal"
            />
          </span>
          <span>
            运费
            <Price
              className="!text-secondary"
              price={detail?.orderPrice?.deliveryAmountYuan}
              size="normal"
            />
          </span>
          <span>
            GST
            <Price
              className="!text-secondary"
              price={detail?.orderPrice?.totalTaxationAmountYuan}
              size="normal"
            />
          </span>
          <span>
            调整金额
            <Price
              className="!text-secondary"
              price={detail?.orderPrice?.adjustmentYuan}
              size="normal"
            />
          </span>
        </div>
      </div>
    </Card>
  );
};

export default GoodsInfo;
