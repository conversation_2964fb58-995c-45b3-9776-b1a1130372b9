import SettleDialog from '@/packages/sales/order/detail/components/SettleDialog';
import { allOutbound } from '@/packages/sales/order/edit/services';
import { deleteOrder, withdrawOrder } from '@/packages/sales/order/list/services';
import { OrderStatus } from '@/packages/sales/order/list/types/OrderStatus';
import { PayStatus } from '@/packages/sales/order/list/types/PayStatus';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import { Button, Dialog, SafeArea, Space } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import { useState } from 'react';

export interface ActionsBarProps {
  detail: OrderListItemEntity;
  onRefresh: () => void;
}

const ActionsBar = (props: ActionsBarProps) => {
  const { detail, onRefresh } = props;
  const [showSettle, setShowSettle] = useState(false);

  return (
    <div className="fixed left-0 right-0 bottom-0 bg-white z-20">
      <Space className="px-[28px] py-[24px] empty:hidden justify-end">
        {[OrderStatus.WAIT_TO_HANDLE].includes(detail?.orderStatus?.orderStatus!) && (
          <PermissionComponent permission={'editOrder'}>
            <Button
              onClick={() =>
                Taro.redirectTo({
                  url: `/packages/sales/order/edit/index?orderNo=${detail?.orders?.orderNo}`,
                })
              }
            >
              编辑
            </Button>
          </PermissionComponent>
        )}
        {[OrderStatus.WAIT_TO_OUTBOUND].includes(detail?.orderStatus?.orderStatus!) && (
          <PermissionComponent permission={'withdrawOrder'}>
            <Button
              onClick={() => {
                Dialog.open('dialog', {
                  title: '确认要撤回吗',
                  onConfirm: () => {
                    return withdrawOrder(detail?.orderId!)
                      .then((result) => {
                        if (result) {
                          Taro.showToast({ title: '撤回成功' });
                          onRefresh?.();
                        }
                      })
                      .finally(() => {
                        Dialog.close('dialog');
                      });
                  },
                  onCancel: () => {
                    Dialog.close('dialog');
                  },
                });
              }}
            >
              撤回
            </Button>
          </PermissionComponent>
        )}
        {[OrderStatus.WAIT_TO_HANDLE, OrderStatus.WAIT_TO_OUTBOUND].includes(
          detail?.orderStatus?.orderStatus!,
        ) && (
          <PermissionComponent permission={'deleteOrder'}>
            <Button
              onClick={() => {
                Dialog.open('dialog', {
                  title: '确认要作废吗',
                  onConfirm: () => {
                    return deleteOrder({ deleteOrderItemList: [{ orderId: detail?.orderId }] })
                      .then((result) => {
                        if (result?.[0]?.success) {
                          Taro.showToast({ title: '作废成功' });
                          onRefresh?.();
                        } else {
                          Taro.showModal({
                            content: result?.[0]?.returnMsg ?? '系统错误',
                            showCancel: false,
                          });
                        }
                      })
                      .finally(() => {
                        Dialog.close('dialog');
                      });
                  },
                  onCancel: () => {
                    Dialog.close('dialog');
                  },
                });
              }}
            >
              作废
            </Button>
          </PermissionComponent>
        )}
        {[OrderStatus.WAIT_TO_OUTBOUND, OrderStatus.OUTBOUND_FINISH].includes(
          detail?.orderStatus?.orderStatus!,
        ) &&
          [PayStatus.WAIT_TO_PAY, PayStatus.PART_PAY].includes(detail?.orderStatus?.payStatus!) && (
            <PermissionComponent permission={'orderSettlement'}>
              <Button type="primary" onClick={() => setShowSettle(true)}>
                确认结算
              </Button>
            </PermissionComponent>
          )}
        {[OrderStatus.WAIT_TO_OUTBOUND].includes(detail?.orderStatus?.orderStatus!) && (
          <PermissionComponent permission={'orderOutWarehouse'}>
            <Button
              type="primary"
              onClick={() => {
                Dialog.open('dialog', {
                  title: '是否确认一键出库',
                  lockScroll: true,
                  onCancel: () => {
                    Dialog.close('dialog');
                  },
                  onConfirm: () => {
                    return allOutbound([detail?.orderId!])
                      .then((result) => {
                        if (result) {
                          Taro.showToast({ title: '出库成功' });
                          onRefresh?.();
                        }
                      })
                      .finally(() => {
                        Dialog.close('dialog');
                      });
                  },
                });
              }}
            >
              一键出库
            </Button>
          </PermissionComponent>
        )}
      </Space>
      <SettleDialog
        visible={showSettle}
        onClose={() => setShowSettle(false)}
        orderDetail={detail}
        onRefresh={onRefresh}
      />
      <SafeArea position="bottom" />
      <Dialog id="dialog" lockScroll />
    </div>
  );
};

export default ActionsBar;
