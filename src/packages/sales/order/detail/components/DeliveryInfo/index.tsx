import Card from '@/components/Card';
import { DeliveryMethod } from '@/packages/sales/order/edit/types/DeliveryMethod';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { Space } from '@nutui/nutui-react-taro';

export interface DeliveryInfoProps {
  detail: OrderListItemEntity;
}

const DeliveryInfo = (props: DeliveryInfoProps) => {
  const { detail } = props;

  if (
    [DeliveryMethod.SELF_PICKUP].includes(
      detail?.orderFixedDistributionList?.[0]?.distributionMode!,
    )
  ) {
    return null;
  }

  const data = {
    distributionModeName: detail?.orderFixedDistributionList?.[0].distributionModeName,
    logisticsCompanyName: detail?.orderFixedDistributionList?.[0].logisticsCompanyName,
    logisticsNo: detail?.orderFixedDistributionList?.[0]?.logisticsNo,
    address: `${detail?.orderFixedAddressList?.[0]?.consigneeProvinceName ?? ''}${
      detail?.orderFixedAddressList?.[0]?.consigneeCityName ?? ''
    }${detail?.orderFixedAddressList?.[0]?.consigneePrefectureName ?? ''}${
      detail?.orderFixedAddressList?.[0]?.consigneeDetail ?? ''
    }`,
  };

  return (
    <Card title="配送信息">
      <Space>
        <span>{data.distributionModeName}</span>
        {data.logisticsCompanyName && <span>{data.logisticsCompanyName}</span>}
      </Space>
      {data.logisticsNo && (
        <div className="mt-[24px]">
          <div className="text-thirdary text-[28px] mb-[12px]">物流单号</div>
          <div>{data.logisticsNo}</div>
        </div>
      )}
      {data.address && (
        <div className="mt-[24px]">
          <div className="text-thirdary text-[28px] mb-[12px]">配送地址</div>
          <div>{data.address}</div>
        </div>
      )}
    </Card>
  );
};

export default DeliveryInfo;
