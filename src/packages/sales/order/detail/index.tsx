import CustomNavBar from '@/components/CustomNavBar';
import ActionsBar from '@/packages/sales/order/detail/components/ActionsBar';
import BaseInfo from '@/packages/sales/order/detail/components/BaseInfo';
import DeliveryInfo from '@/packages/sales/order/detail/components/DeliveryInfo';
import GoodsInfo from '@/packages/sales/order/detail/components/GoodsInfo';
import OperationInfo from '@/packages/sales/order/detail/components/OperationInfo';
import OrderTags from '@/packages/sales/order/detail/components/OrderTags';
import PaymentInfo from '@/packages/sales/order/detail/components/PaymentInfo';
import { getOrderByOrderNoForDbReturnSelected } from '@/packages/sales/order/list/services';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { SafeArea } from '@nutui/nutui-react-taro';
import { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';

export default function Index() {
  const [detail, setDetail] = useState<OrderListItemEntity>();
  const router = useRouter();
  const { orderNo } = router.params;

  const queryDetail = () => {
    if (orderNo) {
      getOrderByOrderNoForDbReturnSelected(orderNo).then((result) => {
        if (result) {
          setDetail(result);
        }
      });
    }
  };

  useEffect(() => {
    queryDetail();
  }, [orderNo]);

  if (!detail) {
    return null;
  }

  return (
    <div className="pb-[120px]">
      <CustomNavBar title={detail?.orderStatus?.orderStatusName} />
      <div className="mx-[52px] mt-[28px] mb-[32px]">
        <div className="text-[34px]">{detail.orders?.cstName}</div>
        <div className="text-[28px] mt-[12px] text-secondary flex items-center">
          <span className="mr-[12px]">{detail.orders?.orderNo}</span>
          <OrderTags detail={detail} source={'detail'} />
        </div>
      </div>
      <BaseInfo detail={detail} />
      <GoodsInfo detail={detail} />
      <PaymentInfo detail={detail} />
      <DeliveryInfo detail={detail} />
      <OperationInfo detail={detail} />
      <ActionsBar detail={detail} onRefresh={queryDetail} />
      <SafeArea position={'bottom'} />
    </div>
  );
}
