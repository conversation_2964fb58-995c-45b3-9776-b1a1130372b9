import { EnableType } from '@/types/EnableType';
import { PageRequestParamsType } from '@/types/PageRequestParamsType';

export interface QueryStoreGoodsPageRequest extends PageRequestParamsType {
  /**
   * 品牌ID集合，精准匹配
   */
  brandIdList?: string[];
  /**
   * 类目ID集合，只传三级类目ID，精准匹配
   */
  categoryIdList?: string[];
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 有权限门店ID集合，多个以逗号间隔，例如查询所有权限仓库总库存使用
   */
  hasAuthStoreIds?: string;
  /**
   * 是否仅看有货
   */
  hasStock?: boolean;
  /**
   * 是否精准查询，针对使用关键字查询
   */
  isAccurate?: boolean;
  /**
   * 是否获取有权限门店成本价
   */
  isFetchAllCostPrice?: boolean;
  /**
   * 是否获取有权限门店所有库存
   */
  isFetchAllInventory?: boolean;
  /**
   * 是否获取品牌聚合数据
   */
  isFetchBrandAggs?: boolean;
  /**
   * 是否获取类目聚合数据
   */
  isFetchCategoryAggs?: boolean;
  /**
   * 是否获取有上次售价
   */
  isFetchLastSalePrice?: boolean;
  /**
   * 是否获取本地库存
   */
  isFetchLocalInventory?: boolean;
  /**
   * 是否获取商品库存
   */
  isFetchLocation?: boolean;
  /**
   * 是否获取单仓成本价
   */
  isFetchWarehouseCostPrice?: boolean;
  /**
   * 商品编码集合，精确匹配
   */
  itemSnList?: string[];

  itemIdList?: string[];
  /**
   * 状态:0-删除1-启用2-禁用
   */
  itemStatus?: EnableType;
  /**
   * 1-标准2-非标
   */
  itemType?: number;
  /**
   * 门店零售商id
   */
  memberId?: string;
  /**
   * 车型ID，vin解析场景，精准匹配
   */
  modelId?: string;
  /**
   * OE码集合，epc、日产、车型查询场景，精准匹配
   */
  oeNoList?: string[];
  /**
   * 查询关键字，通过商品名称、商品编码、OE、品牌件号、ETC号、助记码进行匹配
   */
  queryKeyWord?: string;
  /**
   * 门店ID，单个，查询上次售价必须使用
   */
  storeId?: string;
  /**
   * 仓库ID
   */
  warehouseId?: string;
  categoryIdAndNameList?: any;
}
