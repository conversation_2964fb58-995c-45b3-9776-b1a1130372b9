import { QueryStoreGoodsPageRequest } from '@/packages/sales/order/chooseGoodsPage/types/query.store.goods.page.request';
import { StoreGoodsEntity } from '@/packages/sales/order/chooseGoodsPage/types/store.goods.entity';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { post } from '@/utils/request';

/**
 * 查询门店商品
 * @param params
 * @returns
 */
export const queryStoreGoods = (params: QueryStoreGoodsPageRequest) => {
  return post<PageResponseDataType<StoreGoodsEntity>>(`/ipmsconsole/goods/member/query`, {
    data: params,
  });
};
