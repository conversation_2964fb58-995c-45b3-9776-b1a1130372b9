import CodeWithDivider from '@/components/CodeWithDivider';
import { StoreGoodsEntity } from '@/packages/sales/order/chooseGoodsPage/types/store.goods.entity';
import GoodDetailPopup, { ItemData } from '@/packages/sales/order/components/GoodDetailPopup';
import { updateOrderItem } from '@/packages/sales/order/edit/services';
import { OprateType } from '@/packages/sales/order/edit/types/update.order.item.request';
import { convertStoreGoodsToSaleGoods } from '@/packages/sales/order/edit/utils/convertStoreGoodsToSaleGoods';
import { convertStringToNumber } from '@/utils/convertStringToNumber';
import { Divider, InputNumber, Price } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import { useEffect, useState } from 'react';

export interface ConfirmSingleItem {
  itemId: string;
  price: number;
  number: number;
}

export interface GoodItemProps {
  record: StoreGoodsEntity;
  id?: string;
  price?: number;
  number?: number;
  onConfirm?: (props?: ConfirmSingleItem) => void;
  cstId?: string;
  storeId?: string;
  warehouseId?: string;
  orderId?: string;
  onRefresh?: () => void;
  index: number;
  setFooterBarVisible: (v: boolean) => void;
}

const GoodItem = (props: GoodItemProps) => {
  const {
    id,
    record,
    index,
    onRefresh,
    cstId,
    orderId,
    warehouseId,
    storeId,
    price,
    number,
    onConfirm,
    setFooterBarVisible,
  } = props;
  const [localPrice, setLocalPrice] = useState<number>();
  const [localNumber, setLocalNumber] = useState<number>();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    console.log('GoodItemProps', props);
    setLocalPrice(price ?? record.suggestPrice);
    setLocalNumber(number ?? 0);
  }, [record, price, number]);

  useEffect(() => {
    setFooterBarVisible(!visible);
  }, [visible]);

  /**
   * 弹窗内确认事件
   */
  const handleConfirm = (value: ItemData) => {
    if (orderId) {
      if (id) {
        if (value.number === 0) {
          updateOrderItem({
            orderId,
            oprateType: OprateType.Delete,
            orderItemList: [{ id }],
          }).then((result) => {
            if (result) {
              onRefresh?.();
            }
          });
        } else {
          updateOrderItem({
            orderId,
            oprateType: OprateType.Modify,
            orderItemList: [{ id, unitPrice: value.price, saleNum: value.number }],
          }).then((result) => {
            if (result) {
              onRefresh?.();
            }
          });
        }
      } else {
        const goods = convertStoreGoodsToSaleGoods([
          { ...record, number: value.number, price: value.price },
        ]);
        updateOrderItem({
          orderId,
          oprateType: OprateType.Add,
          orderItemList: goods,
        }).then((result) => {
          if (result) {
            onRefresh?.();
          }
        });
      }
    } else {
      onConfirm?.({ ...record, price: value.price, number: value.number });
    }
    setVisible(false);
  };

  /**
   * 列表中直接更新数量事件
   */
  const handleUpdateNumber = (v: number) => {
    if (loading) {
      return;
    }
    if (!localPrice) {
      Taro.showToast({ icon: 'none', title: '请输入价格' });
      return;
    }
    if (orderId) {
      const value = convertStringToNumber({
        value: v?.toString() ?? 0,
        min: 0,
        max: 999999,
        decimal: 1,
      });
      if (value === 0) {
        setLoading(true);
        updateOrderItem({
          orderId,
          oprateType: OprateType.Delete,
          orderItemList: [{ id }],
        })
          .then((result) => {
            if (result) {
              onRefresh?.();
            }
          })
          .finally(() => {
            setTimeout(() => {
              setLoading(false);
            }, 500);
          });
      } else {
        setLoading(true);
        updateOrderItem({
          orderId,
          oprateType: OprateType.Modify,
          orderItemList: [{ id, unitPrice: price, saleNum: value }],
        })
          .then((result) => {
            if (result) {
              onRefresh?.();
            }
          })
          .finally(() => {
            setTimeout(() => {
              setLoading(false);
            }, 500);
          });
      }
    }
    setLocalNumber(v);
    onConfirm?.({ itemId: record.itemId, price: localPrice, number: v });
  };

  return (
    <>
      <div
        className={classNames('flex bg-white rounded my-[24px] mx-[28px] py-[24px] px-[28px]', {
          'mt-[0]': index === 0,
        })}
        onClick={() => setVisible(true)}
      >
        <img
          src={record.images?.[0]}
          className="rounded mr-[20px] bg-[#f5f5f5] w-[88px] h-[88px]"
        />
        <div className="flex-1">
          <div>{record.itemName}</div>
          <div className="text-[24px] text-thirdary my-[12px]">
            {record.itemSn}
            <Divider direction="vertical" />
            {record.brandName}
            <Divider direction="vertical" />
            {record.categoryName}
            <Divider direction="vertical" />
            {record.unitName}
          </div>
          <div className="flex gap-[28px] text-[24px] text-secondary my-[12px]">
            <span>本地库存: {record.avaNum}</span>
            {record.locationCode && (
              <span className="flex">
                库位:&ensp;
                <CodeWithDivider
                  key={'locationCode'}
                  title="库位"
                  items={record.locationCode?.split(',') ?? []}
                />
              </span>
            )}
          </div>
          <div className="flex justify-between">
            <Price price={localPrice ?? 0} size="normal" />
            {localNumber ? (
              <InputNumber
                max={999999}
                async={true}
                allowEmpty={true}
                value={localNumber ?? 0}
                min={0}
                onClick={(e) => {
                  e.stopPropagation();
                }}
                disabled={loading}
                onChange={handleUpdateNumber}
              />
            ) : (
              <div
                className="w-[56px] h-[56px] bg-black/5 flex justify-center items-center"
                onClick={() => {
                  setVisible(true);
                }}
              >
                +
              </div>
            )}
          </div>
        </div>
      </div>
      <GoodDetailPopup
        cstId={cstId}
        visible={visible}
        warehouseId={warehouseId}
        onClose={() => setVisible(false)}
        record={record}
        storeId={storeId}
        localPrice={localPrice}
        localNumber={localNumber}
        onConfirm={handleConfirm}
      />
    </>
  );
};

export default GoodItem;
