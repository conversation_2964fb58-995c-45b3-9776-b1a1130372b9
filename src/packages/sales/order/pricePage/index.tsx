import Card from '@/components/Card';
import DetailList from '@/components/DetailList';
import { lastSalePrice } from '@/packages/sales/order/pricePage/services';
import { LastSalePriceResponse } from '@/packages/sales/order/pricePage/types/last.sale.price.response';
import { Price, SafeArea } from '@nutui/nutui-react-taro';
import { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';

export interface PricePageProps {
  itemId?: string;
  suggestPrice?: string; // 销售价
  lowPrice?: string; // 最低售价
  costPrice?: string; // 成本价
  storeId?: string;
  cstId?: string;
}

const PricePage = () => {
  const router = useRouter();
  console.log('router.params', router.params);
  const { itemId, suggestPrice, lowPrice, costPrice, storeId, cstId } =
    router.params as PricePageProps;
  const [detail, setDetail] = useState<LastSalePriceResponse>();

  const priceInfo = [
    {
      label: '销售价',
      value:
        suggestPrice !== 'undefined' ? (
          <Price className="!text-gray-900" size="mormal" price={suggestPrice} />
        ) : (
          ''
        ),
    },
    {
      label: '最低价',
      value:
        lowPrice !== 'undefined' ? (
          <Price className="!text-gray-900" size="mormal" price={lowPrice} />
        ) : (
          ''
        ),
    },
    {
      label: '成本价',
      value:
        costPrice !== 'undefined' ? (
          <Price className="!text-gray-900" size="mormal" price={costPrice} />
        ) : (
          ''
        ),
    },
  ];

  useEffect(() => {
    if (itemId && storeId && cstId) {
      lastSalePrice({ itemId, storeId, cstId }).then((result) => {
        if (result) {
          setDetail(result);
        }
      });
    }
  }, [itemId, storeId, cstId]);

  return (
    <div>
      <Card className="py-[0px]">
        <DetailList
          dataSource={priceInfo}
          justified={true}
          divider={true}
          className="leading-[108px]"
          labelColor="rgba(0, 0, 0, 0.9)"
        />
      </Card>
      <Card title="客户近5次售价">
        <DetailList
          dataSource={detail?.cstLastFiveSalePrice?.map((item) => ({
            label: (
              <div className="my-[10px]">
                <div className="text-gray-900">{item.orderNo}</div>
                <div className="text-thirdary text-[28px]">{item.sortTime}</div>
              </div>
            ),
            value: (
              <div className="flex items-end flex-col">
                <Price size="normal" className="!text-gray-900" price={item.salePrice} />
                <div className="text-thirdary text-[28px]">x{item.saleNum}</div>
              </div>
            ),
          }))}
          justified={true}
          divider={true}
        />
      </Card>
      <Card title="门店近5次售价">
        <DetailList
          dataSource={detail?.cstLastFiveSalePrice?.map((item) => ({
            label: (
              <div className="my-[10px]">
                <div className="text-gray-900">{item.cstName}</div>
                <div className="text-gray-900">{item.orderNo}</div>
                <div className="text-thirdary text-[28px]">{item.sortTime}</div>
              </div>
            ),
            value: (
              <div className="flex items-end flex-col">
                <div>&ensp;</div>
                <Price size="normal" className="!text-gray-900" price={item.salePrice} />
                <div className="text-thirdary text-[28px]">x{item.saleNum}</div>
              </div>
            ),
          }))}
          justified={true}
          divider={true}
        />
      </Card>
      <SafeArea position={'bottom'} />
    </div>
  );
};

export default PricePage;
