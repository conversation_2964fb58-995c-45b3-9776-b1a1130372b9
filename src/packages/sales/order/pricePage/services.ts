import { LastSalePriceRequest } from '@/packages/sales/order/pricePage/types/last.sale.price.request';
import { LastSalePriceResponse } from '@/packages/sales/order/pricePage/types/last.sale.price.response';
import { post } from '@/utils/request';

export const lastSalePrice = async (params: LastSalePriceRequest) => {
  return post<LastSalePriceResponse>(`/ipmsconsole/goods/price/lastSalePrice`, {
    data: params,
  });
};
