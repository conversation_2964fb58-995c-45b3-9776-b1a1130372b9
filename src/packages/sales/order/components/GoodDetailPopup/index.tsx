import CodeWithDivider from '@/components/CodeWithDivider';
import { StoreGoodsEntity } from '@/packages/sales/order/chooseGoodsPage/types/store.goods.entity';
import { convertStringToNumber } from '@/utils/convertStringToNumber';
import {
  Button,
  ConfigProvider,
  Divider,
  Input,
  InputNumber,
  Popup,
  Price,
  SafeArea,
  Space,
} from '@nutui/nutui-react-taro';
import { ScrollView } from '@tarojs/components';
import Taro from '@tarojs/taro';
import _ from 'lodash';
import { useEffect, useState } from 'react';

export interface ItemData {
  price: number;
  number: number;
}

export interface GoodDetailPopupProps {
  visible: boolean;
  onClose: () => void;
  record?: StoreGoodsEntity;
  cstId?: string;
  warehouseId?: string;
  storeId?: string;
  localPrice?: number;
  localNumber?: number;
  onConfirm?: (data: ItemData) => void;
}

const GoodDetailPopup = (props: GoodDetailPopupProps) => {
  const {
    visible,
    cstId,
    warehouseId,
    onConfirm,
    storeId,
    onClose,
    record = {} as StoreGoodsEntity,
  } = props;
  const [localPrice, setLocalPrice] = useState<number>();
  const [localNumber, setLocalNumber] = useState<number>();

  useEffect(() => {
    setLocalPrice(props.localPrice ?? record?.suggestPrice);
  }, [props.localPrice, record]);

  useEffect(() => {
    setLocalNumber(props.localNumber);
  }, [props.localNumber]);

  const goodsInfo = [
    {
      label: '商品编码',
      value: record.itemSn,
    },
    {
      label: 'OE',
      value: <CodeWithDivider key={'oeNos'} title="OE" items={record.oeNos ?? []} />,
    },
    {
      label: '品牌件号',
      value: (
        <CodeWithDivider key={'brandPartNos'} title="品牌件号" items={record.brandPartNos ?? []} />
      ),
    },
    {
      label: '商品品牌',
      value: record.brandName,
    },
    {
      label: '商品分类',
      value: record.categoryName,
    },
    {
      label: '商品产地',
      value: record.originRegionName,
    },
    {
      label: '商品规格',
      value: record.spec,
    },
    {
      label: '单位',
      value: record.unitName,
    },
    {
      label: '库位',
      value: (
        <CodeWithDivider
          key={'locationCode'}
          title="库位"
          items={record.locationCode?.split(',') ?? []}
        />
      ),
    },
  ];

  const handleConfirm = () => {
    if (!localPrice) {
      Taro.showToast({ icon: 'none', title: '请输入价格' });
      return;
    }
    if (!localNumber) {
      Taro.showToast({ icon: 'none', title: '请输入数量' });
      return;
    }
    onConfirm?.({ price: localPrice, number: localNumber });
    onClose?.();
  };

  return (
    <Popup
      visible={visible}
      title={record.itemName}
      position="bottom"
      closeable={true}
      onClose={onClose}
      destroyOnClose={true}
    >
      <ScrollView className="max-h-[25vh]" scrollY={true}>
        <div
          className="mx-[28px] p-[28px] rounded-[8px] flex flex-wrap"
          style={{ background: 'rgba(0, 0, 0, 0.03)' }}
        >
          {goodsInfo.map((item) => (
            <div className="text-[28px] flex leading-8 w-[50%] flex-shrink-0">
              <span className="text-secondary w-[150px] whitespace-nowrap">{item.label}: </span>
              <span>{item.value}</span>
            </div>
          ))}
        </div>
      </ScrollView>
      <div className="p-[28px]">
        <div className="flex items-center my-[20px]">
          <div>销售价格</div>
          <div className="flex flex-1 justify-between items-center">
            <Input
              placeholder="请输入价格"
              value={localPrice?.toString()}
              type="digit"
              maxLength={12}
              // @ts-ignore
              onChange={(v) => setLocalPrice(v)}
              onBlur={(v) => {
                const value = convertStringToNumber({
                  value: v,
                  min: 0,
                  max: 999999999.99,
                  decimal: 2,
                });
                setLocalPrice(value);
              }}
            />
            <span className="text-thirdary text-[28px]">
              上次售价：{record.lastSalePrice ?? '-'}
            </span>
          </div>
        </div>
        <Divider />
        <div className="flex items-center my-[20px]">
          <div className="mr-[50px]">销售数量</div>
          <div className="flex flex-1 justify-between items-center">
            <InputNumber
              max={999999}
              min={0}
              value={localNumber}
              onChange={(v) => {
                setLocalNumber(Number(v) > 999999 ? 999999 : Number(v));
              }}
            />
            <span className="text-thirdary text-[28px]">本地库存：{record.avaNum}</span>
          </div>
        </div>
        <Divider />
        <ConfigProvider
          theme={{
            nutuiSpaceGap: '40px',
          }}
        >
          <Space className="text-[28px] my-[20px]">
            <a
              onClick={() =>
                Taro.navigateTo({
                  url: `/packages/sales/order/pricePage/index?itemId=${record.itemId}&cstId=${cstId}&storeId=${storeId}&lowPrice=${record.lowPrice}&costPrice=${record.costPrice}&suggestPrice=${record.suggestPrice}`,
                })
              }
            >
              价格参考
            </a>
            <a
              onClick={() =>
                Taro.navigateTo({
                  url: `/packages/sales/order/purchaseHistoryPage/index?itemId=${record.itemId}`,
                })
              }
            >
              采购历史
            </a>
            <a
              onClick={() =>
                Taro.navigateTo({
                  url: `/packages/sales/order/stocksPage/index?itemId=${record.itemId}&warehouseId=${warehouseId}`,
                })
              }
            >
              库存分布
            </a>
          </Space>
        </ConfigProvider>
      </div>
      <Divider />
      <div className="p-[28px]">
        <div className="flex justify-between">
          <Price price={_.round(_.multiply(localPrice ?? 0, localNumber ?? 0), 2)} />
          <Button type={'primary'} onClick={handleConfirm} style={{ width: '160px' }}>
            确定
          </Button>
        </div>
        <SafeArea position={'bottom'} />
      </div>
    </Popup>
  );
};

export default GoodDetailPopup;
