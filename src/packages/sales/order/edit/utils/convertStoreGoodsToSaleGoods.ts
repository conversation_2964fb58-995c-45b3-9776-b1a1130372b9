import { ChooseItem } from '@/packages/sales/order/chooseGoodsPage';

export const convertStoreGoodsToSaleGoods = (items: ChooseItem[]) => {
  // 转换商品数据（提交接口和列表接口的商品字段不一致）
  const goods = items.map((item) => {
    const { number, price, brandPartNos, oeNos, suggestPrice, unitName, ...rest } = item;
    return {
      ...rest,
      brandPartNo: brandPartNos?.join(','),
      oeNo: oeNos?.join(','),
      unitPrice: price,
      saleNum: number,
      unit: unitName,
      originalPrice: suggestPrice,
    };
  });
  return goods;
};
