import Card from '@/components/Card';
import GoodsItemForOrder from '@/packages/sales/order/detail/components/GoodsItemForOrder';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { IconFont } from '@nutui/icons-react-taro';
import Taro from '@tarojs/taro';
import editIcon from './imgs/edit.svg';

export interface SalesListProps {
  orderDetail?: OrderListItemEntity;
}

const SalesList = (props: SalesListProps) => {
  const { orderDetail } = props;

  if (!orderDetail) {
    return null;
  }

  return (
    <Card
      title={
        <div className="flex justify-between">
          <span>销售明细</span>
          {orderDetail.orderGoodsROList && orderDetail.orderGoodsROList?.length > 0 && (
            <IconFont
              name={editIcon}
              onClick={() =>
                Taro.navigateTo({
                  url: `/packages/sales/order/salesGoodsPage/index?orderNo=${orderDetail?.orders?.orderNo}`,
                })
              }
            />
          )}
        </div>
      }
    >
      <GoodsItemForOrder goods={orderDetail.orderGoodsROList} />
    </Card>
  );
};

export default SalesList;
