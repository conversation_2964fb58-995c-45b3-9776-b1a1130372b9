import { ChooseItem } from '@/packages/sales/order/chooseGoodsPage';
import { queryStoreGoods } from '@/packages/sales/order/chooseGoodsPage/services';
import { StoreGoodsEntity } from '@/packages/sales/order/chooseGoodsPage/types/store.goods.entity';
import GoodDetailPopup, {
  GoodDetailPopupProps,
  ItemData,
} from '@/packages/sales/order/components/GoodDetailPopup';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { useEffect, useState } from 'react';

export interface ScanGoodProps extends GoodDetailPopupProps {
  itemId?: string;
  onConfirm?: (data: ChooseItem) => void;
  orderDetail?: OrderListItemEntity;
}

const ScanGood = (props: ScanGoodProps) => {
  const { itemId, onConfirm, orderDetail, ...rest } = props;
  const [record, setRecord] = useState<StoreGoodsEntity>();
  const [localPrice, setLocalPrice] = useState<number>();
  const [localNumber, setLocalNumber] = useState<number>();

  const handleConfirm = (data: ItemData) => {
    const value: any = { ...record!, ...data };
    const detailGood = orderDetail?.orderGoodsROList?.find(
      (item) => item.itemId === record?.itemId,
    );
    if (detailGood) {
      value.id = detailGood.id;
    }
    onConfirm?.(value);
  };

  useEffect(() => {
    if (itemId) {
      queryStoreGoods({
        isFetchLocation: true,
        isFetchWarehouseCostPrice: true,
        isFetchLocalInventory: true,
        isFetchLastSalePrice: true,
        itemIdList: [itemId],
        pageNo: 1,
        pageSize: 1,
        warehouseId: props.warehouseId,
        cstId: props.cstId,
        storeId: props.storeId,
      })
        .then((result) => {
          const good = result.data?.[0];
          if (good) {
            const detailGood = orderDetail?.orderGoodsROList?.find(
              (item) => item.itemId === good.itemId,
            );
            if (detailGood) {
              setLocalPrice(detailGood?.unitPriceYuan);
              setLocalNumber(detailGood?.saleNum);
            }
            setRecord(good);
          }
        })
        .catch(() => {
          props.onClose?.();
        });
    }
  }, [itemId]);

  return (
    <GoodDetailPopup
      record={record}
      {...rest}
      localNumber={localNumber}
      localPrice={localPrice}
      onConfirm={handleConfirm}
    />
  );
};

export default ScanGood;
