import Card from '@/components/Card';
import { updateOrderRemark } from '@/packages/sales/order/edit/services';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { ConfigProvider, TextArea } from '@nutui/nutui-react-taro';
import { useEffect, useState } from 'react';

export interface RemarkProps {
  orderDetail?: OrderListItemEntity;
  onRefresh?: () => void;
}

const Remark = (props: RemarkProps) => {
  const { orderDetail, onRefresh } = props;
  const [value, setValue] = useState('');

  useEffect(() => {
    setValue(orderDetail?.orderNoteList?.[0]?.noteDetail ?? '');
  }, [orderDetail]);

  if (!orderDetail) {
    return;
  }

  const handleChange = () => {
    updateOrderRemark({ remark: value, remarkType: 2, orderId: orderDetail?.orderId }).then(
      (result) => {
        if (result) {
          onRefresh?.();
        }
      },
    );
  };

  return (
    <Card title="备注">
      <ConfigProvider theme={{ nutuiTextareaPadding: '0' }}>
        <TextArea
          style={{ height: '100px' }}
          value={value}
          name="remark"
          maxLength={100}
          showCount={true}
          onChange={setValue}
          onBlur={handleChange}
          placeholder="请输入备注信息"
        />
      </ConfigProvider>
    </Card>
  );
};

export default Remark;
