import Card from '@/components/Card';
import RadioButtonGroup from '@/components/RadioButtonGroup';
import { updateOrderDiscount } from '@/packages/sales/order/edit/services';
import { DiscountType, discountTypeOptions } from '@/packages/sales/order/edit/types/DiscountType';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { convertStringToNumber } from '@/utils/convertStringToNumber';
import { Form, Input } from '@nutui/nutui-react-taro';
import { useEffect, useState } from 'react';

export interface DiscountProps {
  orderDetail?: OrderListItemEntity;
  onRefresh?: () => void;
}

const Discount = (props: DiscountProps) => {
  const [form] = Form.useForm();
  const { orderDetail, onRefresh } = props;
  const [localDiscountType, setLocalDiscountType] = useState<DiscountType>(DiscountType.NONE);

  /**
   * 订单值回显
   */
  useEffect(() => {
    if (orderDetail) {
      const defaultData: any = {};
      const defaultDiscountType =
        orderDetail?.orderFixedDiscountList?.[0]?.discountType ?? DiscountType.NONE;
      defaultData.discountType = defaultDiscountType;
      form.setFieldsValue(defaultData);
      setLocalDiscountType(defaultDiscountType);

      resetRateAndDiscount();
    }
  }, [orderDetail]);

  /**
   * 回显折扣和金额
   */
  const resetRateAndDiscount = () => {
    if (orderDetail) {
      const defaultData: any = {};

      const defaultDiscountMoney = orderDetail?.orderFixedDiscountList?.[0]?.discountMoneyYuan;
      if (defaultDiscountMoney) {
        defaultData.discountMoney = defaultDiscountMoney;
      }

      if (orderDetail.orderFixedDiscountList?.[0]?.discountDesc !== 'null') {
        const defaultDiscountRate = orderDetail?.orderFixedDiscountList?.[0]?.discountDesc;
        if (defaultDiscountRate) {
          defaultData.discountRate = defaultDiscountRate;
        }
      }
      form.setFieldsValue(defaultData);
    }
  };

  if (!orderDetail) {
    return;
  }

  return (
    <Card title="优惠类型">
      <Form form={form}>
        <Form.Item name="discountType">
          <RadioButtonGroup
            value={localDiscountType}
            options={discountTypeOptions}
            onChange={(v) => {
              setLocalDiscountType(v as DiscountType);
              if (v === DiscountType.NONE) {
                updateOrderDiscount({
                  discountType: v,
                  orderId: orderDetail?.orderId,
                }).then((result) => {
                  if (result) {
                    onRefresh?.();
                  }
                });
              } else {
                resetRateAndDiscount();
              }
            }}
          />
        </Form.Item>
        {localDiscountType === DiscountType.DISCOUNT_ON_ORDER && (
          <Form.Item name="discountRate" label="打折">
            <Input
              type="digit"
              placeholder="请输入折扣"
              clearable={false}
              onBlur={(v) => {
                if (!v) {
                  return;
                }
                if (v === orderDetail?.orderFixedDiscountList?.[0]?.discountDesc) {
                  return;
                }
                const value = convertStringToNumber({
                  value: v,
                  min: 0.1,
                  max: 9.9,
                  decimal: 1,
                });
                if (value.toString() !== v) {
                  form.setFieldsValue({ discountRate: value });
                }
                updateOrderDiscount({
                  discountType: localDiscountType,
                  orderId: orderDetail?.orderId,
                  discountRate: value,
                }).then((result) => {
                  if (result) {
                    onRefresh?.();
                  }
                });
              }}
            />
          </Form.Item>
        )}
        {localDiscountType === DiscountType.DEDUCTION_ON_ORDER && (
          <Form.Item name="discountMoney" label="减去">
            <Input
              type="digit"
              clearable={false}
              placeholder="请输入金额"
              onBlur={(v) => {
                if (!v) {
                  return;
                }
                if (Number(v) === orderDetail?.orderFixedDiscountList?.[0]?.discountMoneyYuan) {
                  return;
                }
                const value = convertStringToNumber({
                  value: v,
                  min: 0.01,
                  max: 999999999.99,
                  decimal: 2,
                });
                if (value.toString() !== v) {
                  form.setFieldsValue({ discountMoney: value });
                }
                updateOrderDiscount({
                  discountType: localDiscountType,
                  orderId: orderDetail?.orderId,
                  discountMoney: value,
                }).then((result) => {
                  if (result) {
                    onRefresh?.();
                  }
                });
              }}
            />
          </Form.Item>
        )}
      </Form>
    </Card>
  );
};

export default Discount;
