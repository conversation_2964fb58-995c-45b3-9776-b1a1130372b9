export enum PayKind {
  Cash = 1, // 现款
  Credit, // 挂帐
}

export enum PayKindName {
  Cash = '现款',
  Credit = '挂帐',
}

export const payKindOptions = (canCredit: boolean) => {
  const options: any[] = [];
  if (canCredit) {
    options.push({
      label: PayKindName.Credit,
      value: PayKind.Credit,
    });
  }
  options.push({
    label: PayKindName.Cash,
    value: PayKind.Cash,
  });
  return options;
};
