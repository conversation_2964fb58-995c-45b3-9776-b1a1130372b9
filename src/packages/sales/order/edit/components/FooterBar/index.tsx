import DetailList from '@/components/DetailList';
import { allOutbound, confirmPay, submitOrder } from '@/packages/sales/order/edit/services';
import { SubmitOption, submitOptions } from '@/packages/sales/order/edit/types/SubmitOptions';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import { ArrowUp } from '@nutui/icons-react-taro';
import {
  Button,
  CheckboxGroup,
  Divider,
  Popup,
  Price,
  SafeArea,
  Space,
  Tag,
} from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import { useState } from 'react';

export interface FooterBarProps {
  orderDetail?: OrderListItemEntity;
}

const FooterBar = (props: FooterBarProps) => {
  const { orderDetail } = props;
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<SubmitOption[]>([]);

  if (!orderDetail) {
    return;
  }

  const getSaleNum = () => {
    let num = 0;
    orderDetail?.orderGoodsROList?.forEach((item) => {
      num += item?.saleNum ?? 0;
    });
    return num;
  };

  const priceInfo = [
    {
      label: '商品总数',
      value: getSaleNum(),
    },
    {
      label: '商品总金额',
      value: <Price size="mormal" price={orderDetail?.orderPrice?.totalGoodsPriceAmountYuan} />,
    },
    {
      label: '优惠金额',
      value: <Price size="mormal" price={orderDetail?.orderPrice?.totalDiscountAmountYuan} />,
    },
    {
      label: '运费',
      value: <Price size="mormal" price={orderDetail?.orderPrice?.deliveryAmountYuan} />,
    },
    {
      label: '合计',
      value: <Price size="mormal" price={orderDetail?.orderPrice?.shouldTotalOrderAmountYuan} />,
    },
  ];

  const handSubmit = () => {
    if (orderDetail?.orderGoodsROList?.length === 0) {
      Taro.showToast({ title: '请先添加商品', icon: 'none' });
      return;
    }
    setLoading(true);
    submitOrder(orderDetail?.orderId)
      .then(async (result) => {
        if (result) {
          if (options.includes(SubmitOption.StockOut)) {
            try {
              await allOutbound([orderDetail?.orderId!]);
            } catch (error) {
              console.error(error);
            }
          }
          if (options.includes(SubmitOption.ConfirmSettlement)) {
            try {
              await confirmPay({
                orderId: orderDetail?.orderId,
                payKind: orderDetail?.orderPayDetailList?.[0].payKind,
                payDetailList: orderDetail?.orderPayDetailList?.map((item) => ({
                  payeeAcount: item.payeeAccount,
                  payAmount: item.payAmountYuan,
                  payChannel: item.payChannel,
                })),
              });
            } catch (error) {
              console.error(error);
            }
          }
          Taro.redirectTo({
            url: `/packages/sales/order/detail/index?orderNo=${orderDetail?.orders?.orderNo}`,
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className="fixed left-0 right-0 bottom-0 z-[20] bg-white px-[28px]">
      <div className="bg-[#FFF4F4] leading-[56px] text-[24px] mb-[20px] -mx-[28px] px-[28px]">
        销售单号: {orderDetail?.orders?.orderNo}
        <Tag type="primary" className="ml-[24px]">
          {orderDetail?.orderStatus?.orderStatusName}
        </Tag>
      </div>
      <CheckboxGroup
        direction={'horizontal'}
        options={submitOptions}
        onChange={(v) => setOptions(v as SubmitOption[])}
      />
      <Divider />
      <div className="flex justify-between py-[28px]">
        <div className="text-[24px]">
          <Space className="text-secondary items-end">
            <span>共{getSaleNum()}件</span>
            <span className="flex items-end">
              合计
              <Price
                className="leading-none"
                price={orderDetail?.orderPrice?.shouldTotalOrderAmountYuan}
              />
            </span>
          </Space>
          <div className="mt-[10px]">
            <span
              className="text-primary flex items-center"
              onClick={() => {
                setVisible(true);
              }}
            >
              金额明细
              <ArrowUp size={12} className="ml-1" />
            </span>
          </div>
        </div>
        <PermissionComponent permission={'submitOrder'}>
          <Button
            type="primary"
            style={{ width: '120px' }}
            onClick={handSubmit}
            loading={loading}
            disabled={orderDetail.orderGoodsROList?.length === 0}
          >
            提交
          </Button>
        </PermissionComponent>
      </div>
      <Popup
        title="金额明细"
        visible={visible}
        position="bottom"
        closeable={true}
        onClose={() => setVisible(false)}
      >
        <DetailList dataSource={priceInfo} className="leading-[88px] px-[28px]" justified={true} />
        <SafeArea position={'bottom'} />
      </Popup>
      <SafeArea position={'bottom'} />
    </div>
  );
};

export default FooterBar;
