import { AllOutboundResponse } from '@/packages/sales/order/edit/types/AllOutboundResponse';
import { ConfirmPayRequest } from '@/packages/sales/order/edit/types/confirm.pay.request';
import { CreateDraftOrderRequest } from '@/packages/sales/order/edit/types/create.draft.order.request';
import { EditInvoiceRequest } from '@/packages/sales/order/edit/types/edit.invoice.request';
import { UpdateExtendExpenseRequest } from '@/packages/sales/order/edit/types/update.extend.expense.request';
import { UpdateOrderAddressRequest } from '@/packages/sales/order/edit/types/update.order.address.request';
import { UpdateOrderDeliveryInfoRequest } from '@/packages/sales/order/edit/types/update.order.delivery.info.request';
import { UpdateOrderDiscountRequest } from '@/packages/sales/order/edit/types/update.order.discount.request';
import { UpdateOrderItemRequest } from '@/packages/sales/order/edit/types/update.order.item.request';
import { UpdateOrderMainRequest } from '@/packages/sales/order/edit/types/update.order.main.request';
import { UpdateOrderPayKindRequest } from '@/packages/sales/order/edit/types/update.order.pay.kind.request';
import { UpdateOrderRemarkRequest } from '@/packages/sales/order/edit/types/update.order.remark.request';
import { post } from '@/utils/request';
import Taro from '@tarojs/taro';

/**
 * 创建销售草稿单
 */
export const createDraftOrder = async (params: CreateDraftOrderRequest) => {
  return post<{ orderNo: string }>(`/ipmssale/createDraftOrder`, {
    data: params,
  });
};

/**
 * 修改商品明细
 */
export const updateOrderItem = async (params: UpdateOrderItemRequest) => {
  return post<boolean>(`/ipmssale/updateOrderItem`, {
    data: params,
  });
};

/**
 * 修改付款方式
 */
export const updateOrderPayKind = async (params: UpdateOrderPayKindRequest) => {
  return post<boolean>(`/ipmssale/updateOrderPayKind`, {
    data: params,
  });
};

/**
 * 修改配送信息
 */
export const updateOrderDeliveryInfo = async (params: UpdateOrderDeliveryInfoRequest) => {
  return post<boolean>(`/ipmssale/updateOrderDeliveryInfo`, {
    data: params,
  });
};

/**
 * 修改备注
 */
export const updateOrderRemark = async (params: UpdateOrderRemarkRequest) => {
  return post<boolean>(`/ipmssale/updateOrderRemark`, {
    data: params,
  });
};

/**
 * 提交订单
 */
export const submitOrder = async (orderId: string) => {
  return post<boolean>(`/ipmssale/submitOrder`, {
    data: { orderId },
  });
};

/**
 * 修改配送地址
 */
export const updateOrderAddress = async (params: UpdateOrderAddressRequest) => {
  return post<boolean>(`/ipmssale/updateOrderAddress`, {
    data: params,
  });
};

/**
 * 修改优惠金额
 */
export const updateOrderDiscount = async (params: UpdateOrderDiscountRequest) => {
  return post<boolean>(`/ipmssale/updateOrderDiscount`, {
    data: params,
  });
};

/**
 * 修改运费
 */
export const updateExtendExpense = async (params: UpdateExtendExpenseRequest) => {
  return post<boolean>(`/ipmssale/updateExtendExpense`, {
    data: params,
  });
};

/**
 * 一键出库
 */
export const allOutbound = async (orderIds: string[]) => {
  return post<AllOutboundResponse[]>(`/ipmssale/allOutbound`, {
    data: { orderIdList: orderIds },
  }).then((result) => {
    if (result?.[0]?.success) {
      return true;
    } else {
      Taro.showModal({ content: result?.[0]?.returnMsg ?? '', showCancel: false });
    }
  });
};

/**
 * 确认支付
 */
export const confirmPay = async (params: ConfirmPayRequest) => {
  return post<boolean>(`/ipmssale/confirmPay`, {
    data: params,
  });
};

/**
 * 修改订单主体信息
 */
export const updateOrderMain = async (params: UpdateOrderMainRequest) => {
  return post<boolean>(`/ipmssale/updateOrderMain`, {
    data: params,
  });
};

/**
 * 更新订单开票
 */
export const editOrderInvoice = async (params: Partial<EditInvoiceRequest>) => {
  console.log(params, '开票接口传参');
  return post<boolean>(`/ipmssale/orderInvoice`, {
    data: params,
  });
};
