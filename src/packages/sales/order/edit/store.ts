import { ChooseItem } from '@/packages/sales/order/chooseGoodsPage';
import { create } from 'zustand';

export interface BaseInfo {
  warehouseId?: string;
  storeId?: string;
  cstId?: string;
  orderNo?: string;
}

type Store = {
  baseInfo: BaseInfo;
  modelId?: string;
  setBaseInfo: (baseInfo: BaseInfo) => void;
  chooseItems?: ChooseItem[];
  setModelId: (modelId?: string) => void;
  setChooseItems: (chooseItems: ChooseItem[]) => void;
};

const useOrderSaleStore = create<Store>()((set) => ({
  baseInfo: {},
  modelId: undefined,
  chooseItems: [],
  setBaseInfo: (baseInfo: BaseInfo) => set(() => ({ baseInfo })),
  setModelId: (modelId) => set(() => ({ modelId })),
  setChooseItems: (chooseItems) => set(() => ({ chooseItems })),
}));

export default useOrderSaleStore;
