export enum OprateType {
  Add = 1,
  Modify,
  Delete,
}

export interface UpdateOrderItemRequest {
  /**
   * 操作人姓名
   */
  operator?: string;
  /**
   * 操作人工号
   */
  operatorId?: string;
  /**
   * 操作类型1新增2修改3删除
   */
  oprateType?: OprateType;
  /**
   * 订单号
   */
  orderId?: string;
  /**
   * 商品明细
   */
  orderItemList?: OrderItemList[];
}

export interface OrderItemList {
  /**
   * 品牌名称
   */
  brandName?: string;
  /**
   * 品牌件号
   */
  brandPartNo?: string;
  /**
   * 类目编码
   */
  categoryId?: string;
  /**
   * 类目
   */
  categoryName?: string;
  /**
   * 成本价,单位：分
   */
  costPrice?: number;
  /**
   * 订单商品明细id
   */
  id?: string;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * oe号
   */
  oeNo?: string;
  /**
   * 原价,单位：分
   */
  originalPrice?: number;
  /**
   * 购买数量
   */
  saleNum?: number;
  /**
   * sku编码
   */
  skuId?: string;
  /**
   * 单位
   */
  unit?: string;
  /**
   * 售价,单位：分
   */
  unitPrice?: number;
}
