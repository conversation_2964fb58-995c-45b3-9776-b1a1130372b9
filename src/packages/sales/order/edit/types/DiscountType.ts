export enum DiscountType {
  NONE,
  DISCOUNT_ON_ORDER,
  DEDUCTION_ON_ORDER,
}

export enum DiscountTypeName {
  NONE = '无折扣',
  DISCOUNT_ON_ORDER = '整单折',
  DEDUCTION_ON_ORDER = '整单减',
}

export const discountTypeOptions = [
  {
    label: DiscountTypeName.NONE,
    value: DiscountType.NONE,
  },
  {
    label: DiscountTypeName.DISCOUNT_ON_ORDER,
    value: DiscountType.DISCOUNT_ON_ORDER,
  },
  {
    label: DiscountTypeName.DEDUCTION_ON_ORDER,
    value: DiscountType.DEDUCTION_ON_ORDER,
  },
];
