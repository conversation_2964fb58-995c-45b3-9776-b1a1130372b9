import CodeWithDivider from '@/components/CodeWithDivider';
import { updateOrderItem } from '@/packages/sales/order/edit/services';
import { OprateType } from '@/packages/sales/order/edit/types/update.order.item.request';
import { OrderGoodsROList } from '@/packages/sales/order/list/types/order.list.item.entity';
import { convertStringToNumber } from '@/utils/convertStringToNumber';
import { Close, Trash } from '@nutui/icons-react-taro';
import { Divider, Input, InputNumber, Space } from '@nutui/nutui-react-taro';
import { useEffect, useState } from 'react';

export interface GoodItemProps {
  record: OrderGoodsROList;
  orderId: string;
  onRefresh?: () => void;
}

const GoodItem = (props: GoodItemProps) => {
  const { record, orderId, onRefresh } = props;
  const [saleNum, setSaleNum] = useState<number>();
  const [unitPrice, setUnitPrice] = useState<string>();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (record) {
      setSaleNum(record.saleNum);
      setUnitPrice(record.unitPriceYuan?.toString());
    }
  }, [record]);

  /**
   * 更新数量
   * @param v
   */
  const handleUpdateNumber = (v: number) => {
    if (loading) {
      return;
    }
    setSaleNum(v);
    setLoading(true);
    const value = convertStringToNumber({
      value: v?.toString() ?? 1,
      min: 1,
      max: 999999,
      decimal: 1,
    });
    updateOrderItem({
      orderId,
      oprateType: OprateType.Modify,
      orderItemList: [{ id: record?.id, unitPrice: Number(unitPrice), saleNum: value }],
    })
      .then((result) => {
        if (result) {
          onRefresh?.();
        }
      })
      .finally(() => {
        setTimeout(() => {
          setLoading(false);
        }, 500);
      });
  };

  /**
   * 更新价格
   */
  const handleUpdatePrice = (v: string) => {
    if (!v) {
      return;
    }
    if (v === record.unitPrice?.toString()) {
      return;
    }
    const value = convertStringToNumber({
      value: v,
      min: 0.01,
      max: 999999999.99,
      decimal: 2,
    });
    updateOrderItem({
      orderId,
      oprateType: OprateType.Modify,
      orderItemList: [{ id: record.id, unitPrice: value, saleNum }],
    }).then((result) => {
      if (result) {
        onRefresh?.();
      }
    });
  };

  /**
   * 删除商品
   */
  const handleDelete = () => {
    updateOrderItem({
      orderId,
      oprateType: OprateType.Delete,
      orderItemList: [{ id: record.id }],
    }).then((result) => {
      if (result) {
        onRefresh?.();
      }
    });
  };

  return (
    <div className="flex bg-white rounded my-[24px] mx-[28px] py-[24px] px-[28px]">
      <img src={record.images?.[0]} className="rounded mr-[20px] bg-[#f5f5f5] w-[88px] h-[88px]" />
      <div className="flex-1">
        <div className="flex justify-between">
          <span>{record.itemName}</span>
          <Trash size={20} color="rgb(0, 0, 0, 0.4)" onClick={handleDelete} />
        </div>
        <div className="text-[24px] text-thirdary my-[12px]">
          {record.itemSn}
          <Divider direction="vertical" />
          {record.brandName}
          <Divider direction="vertical" />
          {record.categoryName}
          <Divider direction="vertical" />
          {record.unitName}
        </div>
        <Space className="text-[24px] text-secondary my-[12px]">
          <span>本地库存: {record.avaNum}</span>
          <span className="flex">
            库位:&ensp;
            <CodeWithDivider
              key={'locationCode'}
              title="库位"
              items={record.locationCode?.split(',') ?? []}
            />
          </span>
        </Space>
        <div className="flex justify-between items-center">
          <Input
            value={unitPrice}
            type={'digit'}
            style={{
              border: '1px solid #ddd',
              padding: '5px 8px',
              textAlign: 'center',
            }}
            maxLength={12}
            onChange={(v) => setUnitPrice(v)}
            onBlur={handleUpdatePrice}
          />
          <span className="mx-[28px]">
            <Close size={12} color="#999" />
          </span>
          <InputNumber
            async={true}
            allowEmpty={true}
            max={999999}
            value={saleNum}
            disabled={loading}
            onChange={handleUpdateNumber}
          />
        </div>
      </div>
    </div>
  );
};

export default GoodItem;
