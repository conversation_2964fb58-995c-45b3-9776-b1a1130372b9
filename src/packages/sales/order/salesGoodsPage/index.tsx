import '@/assets/lightPage.scss';
import { getOrderByOrderNoForDbReturnSelected } from '@/packages/sales/order/list/services';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import GoodItem from '@/packages/sales/order/salesGoodsPage/components/GoodItem';
import { Button, Price, SafeArea, Space } from '@nutui/nutui-react-taro';
import Taro, { useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import CustomNavBar from "@/components/CustomNavBar";

const SalesGoodsPage = () => {
  const [orderDetail, setOrderDetail] = useState<OrderListItemEntity>();
  const router = useRouter();
  const { orderNo } = router.params;

  useEffect(() => {
    queryDetail();
  }, [orderNo]);

  /**
   * 查询订单详情
   */
  const queryDetail = () => {
    if (orderNo) {
      getOrderByOrderNoForDbReturnSelected(orderNo).then((result) => {
        if (result) {
          setOrderDetail(result);
        }
      });
    }
  };

  if (!orderDetail) {
    return null;
  }

  if (orderDetail?.orderGoodsROList?.length === 0) {
    return <div className="py-[40px] flex justify-center text-thirdary">暂无商品</div>;
  }

  /**
   * 计算总数
   */
  const getTotalNumber = () => {
    let totalNumber = 0;
    orderDetail?.orderGoodsROList?.forEach((item) => {
      totalNumber += item.saleNum!;
    });
    return totalNumber;
  };

  return (
    <div className="pb-[120px]">
      <CustomNavBar title="已选商品" />
      {orderDetail?.orderGoodsROList?.map((record) => (
        <GoodItem record={record} onRefresh={queryDetail} orderId={orderDetail?.orderId} />
      ))}
      <div className="fixed left-0 right-0 bottom-0 p-[28px] bg-white z-20">
        <div className="flex justify-between">
          <Space className="justify-center text-secondary text-[24px] items-end leading-4">
            <span>共{getTotalNumber()}件</span>
            <span className="flex items-end">
              合计
              <Price price={orderDetail?.orderPrice?.shouldTotalOrderAmountYuan} />
            </span>
          </Space>
          <Button type="primary" style={{ width: '120px' }} onClick={() => Taro.navigateBack()}>
            确定
          </Button>
        </div>
        <SafeArea position="bottom" />
      </div>
      <SafeArea position="bottom" />
    </div>
  );
};

export default SalesGoodsPage;
