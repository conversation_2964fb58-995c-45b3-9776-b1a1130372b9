import { QueryPurchaseRecordRequest } from '@/packages/sales/order/stocksPage/types/query.purchase.record.request';
import { QueryPurchaseRecordResponse } from '@/packages/sales/order/stocksPage/types/query.purchase.record.response';
import { post } from '@/utils/request';

export const queryStockRecord = async (params: QueryPurchaseRecordRequest) => {
  return post<QueryPurchaseRecordResponse>(`/ipmsconsole/stockinventory/queryList`, {
    data: params,
  });
};
