import DetailList from '@/components/DetailList';
import OrderTags from '@/packages/sales/order/detail/components/OrderTags';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { Divider, Price } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import classNames from 'classnames';

export interface OrderItemProps {
  record: OrderListItemEntity;
  index: number;
}

const OrderItem = (props: OrderItemProps) => {
  const { record, index } = props;
  return (
    <div
      className={classNames('bg-white rounded-[16px] mt-[24px] mx-[28px] px-[28px] py-[20px]', {
        '!mt-[0]': index === 0,
      })}
      onClick={() =>
        Taro.navigateTo({
          url: `/packages/sales/order/detail/index?orderNo=${record?.orders?.orderNo}`,
        })
      }
    >
      <div className="pb-[16px]">
        <div className="flex justify-between items-center mb-1">
          <span>{record.orders?.cstName}</span>
          <span className="text-[24px] text-gray-400">{record.orders?.channelName}</span>
        </div>
        <OrderTags detail={record} />
      </div>
      <div className="-mx-[28px]">
        <Divider />
      </div>
      <div className="flex mt-[24px]">
        <div className="flex-1 text-secondary text-[28px] leading-6">
          <DetailList
            className="text-secondary text-[28px]"
            labelWidth="70px"
            dataSource={[
              { label: '销售单号', value: record.orders?.orderNo },
              { label: '下单时间', value: record.orders?.orderCreateTime },
              { label: '销售门店', value: record.orders?.storeName },
              { label: '发货仓库', value: record.orderFixedDistributionList?.[0]?.warehouseName },
            ]}
          />
        </div>
        <div className="flex flex-col justify-center items-end">
          <div>
            <Price
              className="!text-gray-900"
              price={record.orderPrice?.shouldTotalOrderAmountYuan}
              size="normal"
            />
          </div>
          <div className="text-[24px] mt-[12px] text-thirdary">
            {record?.orderPayDetailList?.[0]?.payKindName}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderItem;
