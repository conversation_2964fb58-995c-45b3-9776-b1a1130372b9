import { DeleteOrderRequest } from '@/packages/sales/order/list/types/delete.order.request';
import { DeleteOrderResponse } from '@/packages/sales/order/list/types/delete.order.response';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { PageQueryOrderByConditionRequest } from '@/packages/sales/order/list/types/page.query.order.by.condition.request';
import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { post } from '@/utils/request';

/**
 * 销售单列表查询
 * @param params
 * @returns
 */
export const pageQueryOrderByCondition = (
  params: PageQueryOrderByConditionRequest & PageRequestParamsType,
) => {
  return post<PageResponseDataType<OrderListItemEntity>>(`/ipmssale/pageQueryOrderByCondition`, {
    data: params,
  });
};

/**
 * 订单详情查询
 */
export const getOrderByOrderNoForDbReturnSelected = async (orderNo: string) => {
  return post<OrderListItemEntity>(`/ipmssale/getOrderByOrderNoForDbReturnSelected`, {
    data: { orderNo },
  });
};

/**
 * 撤回订单
 */
export const withdrawOrder = (orderId: string) => {
  return post<boolean>(`/ipmssale/withdrawOrder`, {
    data: { orderId },
  });
};

/**
 * 作废订单
 */
export const deleteOrder = (params: DeleteOrderRequest) => {
  return post<DeleteOrderResponse[]>(`/ipmssale/deleteOrder`, {
    data: params,
  });
};
