import '@/assets/lightPage.scss';
import CalendarRangeCardChoose from '@/components/CalendarRangeCardChoose';
import { queryStore, queryWarehouse } from '@/components/ChooseStoreAndWarehouseModal/services';
import { StoreEntity } from '@/components/ChooseStoreAndWarehouseModal/types/store.entity';
import { WarehouseEntity } from '@/components/ChooseStoreAndWarehouseModal/types/warehouse.entity';
import CustomMultipleChoose from '@/components/CustomMultipleChoose';
import CustomNavBar from '@/components/CustomNavBar';
import CustomSearchBar from '@/components/CustomSearchBar';
import FilterCustomerPicker from '@/components/FilterCustomerPicker';
import MenuWrap from '@/components/MenuWrap';
import FunPagination from '@/components/Pagination/FunPagination';
import OrderItem from '@/packages/sales/order/list/components/OderItem';
import { pageQueryOrderByCondition } from '@/packages/sales/order/list/services';
import { orderStatusTabOption } from '@/packages/sales/order/list/types/OrderStatus';
import { payStatusOption } from '@/packages/sales/order/list/types/PayStatus';
import { paymentStatusOption } from '@/packages/sales/order/list/types/PaymentStatus';
import { Menu, SafeArea } from '@nutui/nutui-react-taro';
import { navigateTo, useRouter } from '@tarojs/taro';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import { useEffect, useRef, useState } from 'react';

export default function Index() {
  const router = useRouter();
  const { initOrderStatus, initStoreId } = router.params;
  const [params, setParams] = useState<any>({
    orderStatusList: initOrderStatus ? [Number(initOrderStatus)] : [],
    storeIdList: initStoreId ? [initStoreId] : [],
  });
  const cstMenuRef = useRef<any>();
  const storeRef = useRef<any>();
  const orderStatusRef = useRef<any>();
  const orderTimeRef = useRef<any>();
  const [storeList, setStoreList] = useState<StoreEntity[]>([]);
  const [warehouseList, setWarehouseList] = useState<WarehouseEntity[]>([]);

  const itemList = [
    { key: 'itemInfo', name: '商品', scanShow: true, placeholder: '商品名称/编码/OE码' },
    { key: 'orderNo', name: '销售单号', scanShow: false },
  ];

  const storeAndWarehouseFilteredItems = [
    {
      label: '门店',
      keyStr: 'storeIdList',
      multiple: true,
      item: storeList.map((item) => ({ text: item.name, value: item.id })),
    },
    {
      label: '仓库',
      keyStr: 'deliWarehouseIdList',
      multiple: true,
      item: warehouseList.map((item) => ({ text: item.warehouseName, value: item.warehouseId })),
    },
  ];

  const orderStatusFilteredItems = [
    {
      label: '单据状态',
      keyStr: 'orderStatusList',
      multiple: true,
      item: orderStatusTabOption,
    },
    {
      label: '结算状态',
      keyStr: 'payStatusList',
      multiple: true,
      item: payStatusOption,
    },
    {
      label: '收款状态',
      keyStr: 'paymentStatusList',
      multiple: true,
      item: paymentStatusOption,
    },
  ];

  useEffect(() => {
    queryStore({}).then((result) => setStoreList(result));
    queryWarehouse({}).then((result) => setWarehouseList(result));
  }, []);

  const fetchData = (query) => {
    return pageQueryOrderByCondition({ ...query, pageSize: 10 }).then(
      (result) => result?.data ?? [],
    );
  };

  const setInputValue = (param) => {
    if (!isEmpty(param)) {
      setParams((prevData) => ({ ...prevData, ...param }));
    }
  };

  return (
    <div className="flex flex-col h-screen">
      <CustomNavBar showBack={true} title="销售管理" />
      <div className="px-[28px] pt-[16px] mb-[24px]">
        <CustomSearchBar
          itemList={itemList}
          defaultItem={itemList[0]}
          inputValue={setInputValue}
          addUrl={() => navigateTo({ url: '/packages/sales/order/edit/index' })}
        />
      </div>
      <MenuWrap
        menu={
          <Menu>
            <Menu.Item title="单据状态" ref={orderStatusRef}>
              <CustomMultipleChoose
                key={'orderStatus'}
                onClose={() => {
                  orderStatusRef.current?.toggle(false);
                }}
                selected={{
                  orderStatusList: params.orderStatusList ?? [],
                  deliWarehouseIdList: params.deliWarehouseIdList ?? [],
                }}
                onConfirm={(e) => {
                  setParams({ ...params, ...e });
                }}
                items={orderStatusFilteredItems}
              />
            </Menu.Item>
            <Menu.Item title="客户" ref={cstMenuRef}>
              <FilterCustomerPicker
                cstId={params.cstId}
                onChange={(cstId) => {
                  setParams({ ...params, cstId });
                  cstMenuRef.current?.toggle(false);
                }}
              />
            </Menu.Item>
            <Menu.Item title="门店仓库" ref={storeRef}>
              <CustomMultipleChoose
                key={'storeAndWarehouse'}
                onClose={() => {
                  storeRef.current?.toggle(false);
                }}
                selected={{
                  storeIdList: params.storeIdList ?? [],
                  deliWarehouseIdList: params.deliWarehouseIdList ?? [],
                }}
                onConfirm={(e) => {
                  setParams({ ...params, ...e });
                }}
                items={storeAndWarehouseFilteredItems}
              />
            </Menu.Item>
            <Menu.Item title="下单时间" ref={orderTimeRef}>
              <CalendarRangeCardChoose
                value={
                  params.beginOrderTime
                    ? [dayjs(params.beginOrderTime).toDate(), dayjs(params.endOrderTime).toDate()]
                    : []
                }
                onChange={(value) => {
                  if (value) {
                    setParams({
                      ...params,
                      beginOrderTime: value[0]
                        ? dayjs(value[0]).format('YYYY-MM-DD 00:00:00')
                        : undefined,
                      endOrderTime: value[1]
                        ? dayjs(value[1]).format('YYYY-MM-DD 23:59:59')
                        : undefined,
                    });
                    orderTimeRef.current?.toggle(false);
                  }
                }}
              />
            </Menu.Item>
          </Menu>
        }
      />
      <div className="flex-1 min-h-0 overflow-scroll mt-[20px]">
        <FunPagination
          fetchData={fetchData}
          params={params}
          renderItem={(record, index) => <OrderItem record={record} index={index} />}
        />
      </div>
      <SafeArea position={'bottom'} />
    </div>
  );
}
