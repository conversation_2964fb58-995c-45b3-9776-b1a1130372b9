export enum OrderStatus {
  WAIT_TO_HANDLE = 90,
  WAIT_TO_OUTBOUND = 300,
  OUTBOUND_FINISH = 400,
  TRADE_SUCCESS = 600,
  TRADE_CLOSE = 800,
}

export enum OrderStatusName {
  WAIT_TO_HANDLE = '草稿',
  WAIT_TO_OUTBOUND = '待出库',
  OUTBOUND_FINISH = '已出库',
  TRADE_SUCCESS = '已完成',
  TRADE_CLOSE = '已作废',
}

export const orderStatusMap = {
  [OrderStatus.WAIT_TO_HANDLE]: {
    text: OrderStatusName.WAIT_TO_HANDLE,
    color: 'red',
  },
  [OrderStatus.WAIT_TO_OUTBOUND]: {
    text: OrderStatusName.WAIT_TO_OUTBOUND,
    color: 'red',
  },
  [OrderStatus.OUTBOUND_FINISH]: {
    text: OrderStatusName.OUTBOUND_FINISH,
    color: 'green',
  },
  [OrderStatus.TRADE_SUCCESS]: {
    text: OrderStatusName.TRADE_SUCCESS,
    color: 'green',
  },
  [OrderStatus.TRADE_CLOSE]: {
    text: OrderStatusName.TRADE_CLOSE,
    color: '',
  },
};

export const orderStatusTabOption = [
  {
    text: OrderStatusName.WAIT_TO_HANDLE,
    value: OrderStatus.WAIT_TO_HANDLE,
  },
  {
    text: OrderStatusName.WAIT_TO_OUTBOUND,
    value: OrderStatus.WAIT_TO_OUTBOUND,
  },
  {
    text: OrderStatusName.OUTBOUND_FINISH,
    value: OrderStatus.OUTBOUND_FINISH,
  },
  {
    text: OrderStatusName.TRADE_SUCCESS,
    value: OrderStatus.TRADE_SUCCESS,
  },
  {
    text: OrderStatusName.TRADE_CLOSE,
    value: OrderStatus.TRADE_CLOSE,
  },
];
