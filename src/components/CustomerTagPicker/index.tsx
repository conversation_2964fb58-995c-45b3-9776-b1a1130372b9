import { DefautlOptionType } from '@/packages/sales/returns/operation/types/DefaultOptionType';
import { Checklist, Close } from '@nutui/icons-react-taro';
import { Divider, Popup, SafeArea } from '@nutui/nutui-react-taro';
import { indexOf } from 'lodash';
import { useEffect, useState } from 'react';
interface CustomerTagPickerProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (selectedItems: string[]) => void;
  items?: DefautlOptionType[];
  selected: string[];
}
export default ({ visible, onClose, onConfirm, items, selected }: CustomerTagPickerProps) => {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  useEffect(() => {
    if (selected) {
      setSelectedItems(selected);
    }
  }, [selected]);

  /**
   * 确认
   */
  const handleConfirm = () => {
    onConfirm(selectedItems!);
    onClose();
  };
  /**
   * 重置
   */
  const handleRest = () => {
    onConfirm([]);
    setSelectedItems([]);
  };
  const handleChecked = (value) => {
    const index = indexOf(selectedItems, value);
    const items = [...selectedItems];
    if (index < 0) {
      items.push(value);
    } else {
      items.splice(index, 1);
    }
    setSelectedItems(items);
  };

  return (
    <Popup visible={visible} onClose={onClose} closeable={false} position="bottom" round>
      <div className="flex justify-between items-center px-[28px] pt-[47px]">
        <span className="text-main text-[32px] font-medium">客户标签</span>
        <Close
          onClick={() => {
            setSelectedItems(selected);
            onClose();
          }}
          width="16px"
          height="16px"
          color="#000000CC"
        />
      </div>
      <div className="flex-1 min-h-0 overflow-y-scroll px-[28px] mt-[48px] flex flex-wrap">
        {items &&
          items.map((item) => {
            return (
              <div
                className="basis-1/2 flex items-center gap-[20px] mb-[40px] h-[40px]"
                onClick={() => handleChecked(item.value)}
              >
                <span className="truncate max-w-[200px]">{item.title}</span>
                <span>
                  {indexOf(selectedItems, item.value) >= 0 && <Checklist color="#F83431" />}
                </span>
              </div>
            );
          })}
      </div>
      <Divider></Divider>
      <div className="flex justify-between gap-[24px] py-[28px] px-[28px]">
        <div
          className="flex-1 justify-center text-center items-center py-[14px] rounded-[8px] border border-solid border-[#999999]"
          onClick={handleRest}
        >
          重置
        </div>
        <div
          className="flex-1 justify-center text-white text-center items-center  py-[14px] rounded-[8px] bg-[#F83431]"
          onClick={handleConfirm}
        >
          确定
        </div>
      </div>
      <SafeArea position="bottom" />
    </Popup>
  );
};
