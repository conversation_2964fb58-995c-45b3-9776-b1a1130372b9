import { EnableType } from '@/types/EnableType';

export interface QueryWarehouseRequest {
  /**
   * 是否忽略权限
   */
  ignoreStoreAuth?: boolean;
  /**
   * 是否默认门店，1:默认，0：否
   */
  isDefault?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * None
   */
  operatorName?: string;
  /**
   * None
   */
  operatorNo?: string;
  /**
   * 仓库状态：1启用，0禁用
   */
  state?: EnableType;
  /**
   * 门店id列表
   */
  storeIdList?: string[];
}
