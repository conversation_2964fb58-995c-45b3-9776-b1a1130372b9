import { EnableType } from '@/types/EnableType';

export interface WarehouseEntity {
  /**
   * 主键
   */
  id?: string;
  /**
   * 是否默认门店，1:默认，0：否
   */
  isDefault?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * 仓库状态1正常0删除
   */
  state?: EnableType;
  /**
   * 门店id
   */
  storeId?: string;
  /**
   * 门店名称
   */
  storeName?: string;
  /**
   * 仓库id
   */
  warehouseId?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
}
