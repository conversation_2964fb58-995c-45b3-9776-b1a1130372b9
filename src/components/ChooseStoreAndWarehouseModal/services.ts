import { QueryStoreRequest } from '@/components/ChooseStoreAndWarehouseModal/types/query.store.request';
import { QueryWarehouseRequest } from '@/components/ChooseStoreAndWarehouseModal/types/query.warehouse.request';
import { QueryWarehouseResponse } from '@/components/ChooseStoreAndWarehouseModal/types/query.warehouse.response';
import { StoreEntity } from '@/components/ChooseStoreAndWarehouseModal/types/store.entity';
import { WarehouseEntity } from '@/components/ChooseStoreAndWarehouseModal/types/warehouse.entity';
import { post } from '@/utils/request';

/**
 * 查询账户有权限的所有门店
 * @param params
 * @returns
 */
export const queryStore = (params: QueryStoreRequest) => {
  return post<StoreEntity[]>(`/ipmspassport/AccountFacade/queryStore`, {
    data: params,
  });
};

/**
 * 批量查询门店下的仓库
 */
export const queryWarehouse = (params: QueryWarehouseRequest): Promise<WarehouseEntity[]> => {
  return post<QueryWarehouseResponse>(`/ipmswarehouse/warehouseStoreRelation/queryList`, {
    data: params,
  }).then((result) => result.warehouseStoreRelationRoList ?? []);
};
