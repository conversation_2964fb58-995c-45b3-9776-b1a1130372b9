import { queryStore, queryWarehouse } from '@/components/ChooseStoreAndWarehouseModal/services';
import { EnableType } from '@/types/EnableType';
import { Picker } from '@nutui/nutui-react-taro';
import { PickerProps } from '@nutui/nutui-react-taro/dist/types/packages/picker/picker.taro';
import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import { useEffect, useRef, useState } from 'react';

export interface ChooseStoreAndWarehouseModalProps extends Partial<PickerProps> {
  handleClose?: () => void;
  handleConfirm?: (options: PickerOption[], value: string[]) => void;
  disabledColumns?: boolean[];
}

const ChooseStoreAndWarehouseModal = (props: ChooseStoreAndWarehouseModalProps) => {
  const {
    handleClose,
    disabledColumns = [false, false],
    value,
    handleConfirm,
    title = '选择门店与仓库',
    ...rest
  } = props;
  const [data, setData] = useState<PickerOption[]>([]);
  const [currentValue, setCurrentValue] = useState<string[]>();
  const [defaultStoreId, setDefaultStoreId] = useState<string>();
  const [defaultWarehouseId, setDefaultWarehouseId] = useState<string>();
  const hasInit = useRef<boolean>(false);

  /**
   * 获取权限内所有门店和仓库
   */
  useEffect(() => {
    // 这里延迟加载数据是为了等订单默认值优先返回
    setTimeout(() => {
      queryStore({ status: EnableType.ENABLE }).then((stores) => {
        if (stores) {
          queryWarehouse({ storeIdList: stores.map((item) => item.id!) }).then((warehouses) => {
            if (warehouses) {
              setData(
                stores.map((m) => ({
                  text: m.name!,
                  value: m.id!,
                  children: warehouses
                    .filter((n) => n.storeId === m.id)
                    .map((n) => ({ text: n.warehouseName!, value: n.warehouseId! })),
                })),
              );
              const defaultStore = stores?.[0];
              if (defaultStore) {
                setDefaultStoreId(defaultStore.id);
                const defaultWarehouse = warehouses.find(
                  (item) => item.storeId === defaultStore.id && item.isDefault,
                );
                if (defaultWarehouse) {
                  setDefaultWarehouseId(defaultWarehouse.warehouseId);
                }
              }
            }
          });
        }
      });
    }, 500);
    return () => {
      setData([]);
    };
  }, []);

  /**
   * 设置默认值
   */
  useEffect(() => {
    if (value?.length === 2) {
      setCurrentValue([value[0] as string, value[1] as string]);
    } else if (defaultStoreId && defaultWarehouseId) {
      setCurrentValue([defaultStoreId, defaultWarehouseId]);
    }
  }, [value, defaultStoreId, defaultWarehouseId]);

  /**
   * 默认回调数据到外层
   */
  useEffect(() => {
    if (currentValue?.length === 2 && data.length > 0) {
      const defaultStoreOption = data.find(
        (item) => item.value === currentValue[0],
      ) as PickerOption;
      const defaultWarehouseOption = defaultStoreOption?.children?.find(
        (item) => item.value === currentValue[1],
      ) as PickerOption;
      const options = [defaultStoreOption, defaultWarehouseOption];
      if (!hasInit.current) {
        handleConfirm?.(options, currentValue);
        hasInit.current = true;
      }
    }
  }, [currentValue, data]);

  /**
   * 禁用效果
   */
  useEffect(() => {
    let _data = data.filter((m) => !disabledColumns[0] || m.value === value?.[0]);
    if (_data.length === 1) {
      _data[0].children = _data[0].children?.filter(
        (m) => !disabledColumns[1] || m.value === value?.[1],
      );
    }
    setData(_data);
  }, [disabledColumns]);

  return (
    <div>
      <Picker
        title={title}
        onClose={handleClose}
        onCancel={handleClose}
        {...rest}
        value={currentValue}
        options={data}
        onConfirm={(options, value) => {
          // @ts-ignore
          handleConfirm?.(options, value);
          hasInit.current = false;
        }}
      />
    </div>
  );
};

export default ChooseStoreAndWarehouseModal;
