import empty from '@/assets/empty.png';
import { InfiniteLoading } from '@nutui/nutui-react-taro';
import { Image, View } from '@tarojs/components';
import { isEmpty } from 'lodash';
import React, { CSSProperties, useCallback, useEffect, useState } from 'react';
import './FunPagination.scss';

interface PaginationProps<T, paramsType = any> {
  fetchData: (params: paramsType) => never[] | Promise<T[]>;
  renderItem: (item: T, index: number, length?: number) => React.ReactNode;
  initialPage?: number;
  height?: number;
  params?: paramsType;
  popupNode?: React.ReactNode;
}

const FunPagination = <T extends any>({
  fetchData,
  renderItem,
  params,
  initialPage = 1,
  popupNode,
}: PaginationProps<T>) => {
  const [pageNo, setPageNo] = useState(initialPage);
  const [data, setData] = useState<T[]>([]);
  const [hasMore, setHasMore] = useState(true);

  const loadData = useCallback(
    async (pageNo: number) => {
      params.pageNo = pageNo;
      const newData = await fetchData(params);
      setData((prevData) => (pageNo === initialPage ? newData : [...prevData, ...newData]));
      setHasMore((newData?.length ?? 0) == 10);
    },
    [fetchData, initialPage],
  );

  useEffect(() => {
    handleRefresh();
  }, [params]);

  const handleLoadMore = async () => {
    console.log(hasMore);

    if (hasMore) {
      await loadData(pageNo + 1);
      setPageNo((prevPage) => prevPage + 1);
      params.pageNo = pageNo;
    }
  };

  const handleRefresh = async () => {
    setPageNo(initialPage);
    await loadData(initialPage);
    setHasMore(true);
  };

  const InfiniteUlStyle: CSSProperties = {
    height: '100%',
    width: '100%',
    padding: '0',
    overflowY: 'auto',
    overflowX: 'hidden',
  };

  return (
    <div id="primaryScroll" style={InfiniteUlStyle}>
      <InfiniteLoading
        hasMore={hasMore}
        target="primaryScroll"
        onLoadMore={handleLoadMore}
        onRefresh={handleRefresh}
        loadingText={<>加载中.....</>}
        pullRefresh={true}
        loadMoreText={isEmpty(data) ? <></> : <>没有更多了</>}
      >
        {popupNode}
        {data.map((item, index) => (
          <View className="listItem" key={index}>
            {renderItem(item, index, data.length)}
          </View>
        ))}
        {isEmpty(data) && (
          <div className="flex flex-col items-center justify-center">
            <Image src={empty} style={{ width: '160px', height: '160px' }} />
            <span className="text-[24px] text-black/60">暂无数据......</span>
          </div>
        )}
      </InfiniteLoading>
    </div>
  );
};

export default FunPagination;
