import React from 'react';

export interface CardProps {
  children?: React.ReactNode;
  title?: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

const Card = (props: CardProps) => {
  const { title = '', className = '', onClick } = props;
  return (
    <div
      className={`bg-white rounded-[16px] px-[28px] py-[24px] mx-[28px] my-[24px] ${className}`}
      onClick={() => onClick?.()}
    >
      {title && <div className="mb-[24px] font-medium">{title}</div>}
      {props.children}
    </div>
  );
};

export default Card;
