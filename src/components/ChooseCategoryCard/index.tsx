import { queryGoodsPropertyPage } from '@/packages/stocks/inventory/services';
import { Checklist } from '@nutui/icons-react-taro';
import { Button } from '@nutui/nutui-react-taro';
import classNames from 'classnames';
import _ from 'lodash';
import { ReactNode, useEffect, useState } from 'react';

export interface CategoryTreeItem {
  label: ReactNode;
  value: string;
  children: CategoryTreeItem[];
}

export const transformCategoryTree = (tree: any) => {
  return tree.map((node: any) => {
    let children: any[] = [];

    if (node.sonList && node.sonList.length > 0) {
      children = transformCategoryTree(node.sonList);
    } else {
      // 复制当前节点为二级和三级数据
      children = [
        {
          label: node.categoryName,
          value: node.categoryId,
          children: [
            {
              label: node.categoryName,
              value: node.categoryId,
              children: [],
            },
          ],
        },
      ];
    }
    return {
      label: node.categoryName,
      value: node.categoryId,
      children: children,
    };
  });
};

export interface ChooseCategoryCardProps {
  values?: string[];
  onChange: (v: string[], items: Pick<CategoryTreeItem, 'label' | 'value'>[]) => void;
}

const ChooseCategoryCard = (props: ChooseCategoryCardProps) => {
  const [list, setList] = useState<CategoryTreeItem[]>([]);
  const [openFirst, setOpenFirst] = useState<string>();
  const [openSecond, setOpenSecond] = useState<string>();
  const [values, setValues] = useState<string[]>([]);
  const [checkedItems, setCheckedItems] = useState<Pick<CategoryTreeItem, 'label' | 'value'>[]>([]);

  useEffect(() => {
    if (props.values) {
      setValues(values);
    }
  }, [props.values]);

  useEffect(() => {
    queryGoodsPropertyPage(
      { pageNo: 1, pageSize: 999, categoryStatus: 1, isReturnTree: true },
      'category',
    ).then((result) => {
      if (result?.data) {
        setList(transformCategoryTree(result.data));
      }
    });
  }, []);

  useEffect(() => {
    if (list.length) {
      setOpenFirst(list[0].value);
      setOpenSecond(list[0].children?.[0].value);
    }
  }, [list]);

  const handleChecked = (checkNode: CategoryTreeItem) => {
    if (values.includes(checkNode.value)) {
      const newList = _.cloneDeep(values);
      _.remove(newList, (item) => item === checkNode.value);
      setValues(newList);
      const newCheckedList = _.cloneDeep(checkedItems);
      _.remove(newCheckedList, (item) => item.value === checkNode.value);
      setCheckedItems(newCheckedList);
    } else {
      setValues([...values, checkNode.value]);
      setCheckedItems([...checkedItems, { label: checkNode.label, value: checkNode.value }]);
    }
  };

  return (
    <div className="flex flex-col w-full max-h-[60vh] text-[28px]">
      <div className="flex-1 min-h-0 flex leading-[80px]">
        <div className="min-h-0 overflow-y-scroll basis-1/4">
          {list.map((item) => (
            <div
              className={classNames('px-[28px] whitespace-nowrap text-ellipsis overflow-hidden', {
                'bg-[#f5f5f5]': item.value === openFirst,
              })}
              onClick={() => {
                setOpenFirst(item.value);
                setOpenSecond(item.children?.[0].value);
              }}
            >
              {item.label}
            </div>
          ))}
        </div>
        <div className="min-h-0 overflow-y-scroll bg-[#f5f5f5] basis-1/3">
          {list
            .find((item) => item.value === openFirst)
            ?.children?.map((item) => (
              <div
                className={classNames('px-[28px] whitespace-nowrap text-ellipsis overflow-hidden', {
                  'bg-[#efefef]': item.value === openSecond,
                })}
                onClick={() => setOpenSecond(item.value)}
              >
                {item.label}
              </div>
            ))}
        </div>
        <div className="min-h-0 overflow-y-scroll bg-[#efefef] flex-1">
          {list
            .find((item) => item.value === openFirst)
            ?.children?.find((item) => item.value === openSecond)
            ?.children?.map((item) => (
              <div
                className={classNames('flex items-center px-[28px]')}
                onClick={() => handleChecked(item)}
              >
                <span className="flex-1 min-w-0 whitespace-nowrap text-ellipsis overflow-hidden">
                  {item.label}
                </span>
                {values.includes(item.value) && (
                  <Checklist color="#F83431" className="flex-shrink-0" />
                )}
              </div>
            ))}
        </div>
      </div>
      <div className="flex justify-between gap-[22px] mt-[24px]">
        <Button
          className="flex-1"
          onClick={() => {
            setValues([]);
            props.onChange([], []);
          }}
        >
          重置
        </Button>
        <Button
          className="flex-1"
          type="primary"
          onClick={() => {
            props.onChange(values, checkedItems);
          }}
        >
          确定
        </Button>
      </div>
    </div>
  );
};

export default ChooseCategoryCard;
