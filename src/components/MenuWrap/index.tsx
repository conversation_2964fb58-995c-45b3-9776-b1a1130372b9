import classNames from 'classnames';
import _ from 'lodash';
import React from 'react';
import './index.scss';

export interface MenuCheckBoxProps {
  options: {
    label: React.ReactNode;
    value: number | string;
  }[];
  value?: (number | string)[];
  onChange: (value: (string | number)[]) => void;
}

export interface MenuWrapProps {
  menu?: React.ReactNode;
  checkbox?: MenuCheckBoxProps;
  className?: string;
}

const MenuWrap = (props: MenuWrapProps) => {
  const { checkbox, menu, className } = props;
  console.log('checkbox', checkbox);
  return (
    <div
      className={`${
        className ?? ''
      } flex menuWrap min-w-0 overflow-scroll items-center mx-[28px] flex-shrink-0`}
    >
      {checkbox && (
        <div className="flex flex-shrink-0">
          {checkbox.options?.map((item) => (
            <span
              className={classNames('menuButton', {
                checked: checkbox.value?.includes(item.value),
              })}
              onClick={() => {
                let newValue = _.cloneDeep(checkbox.value ?? []);
                const index = newValue.indexOf(item.value);
                if (index !== -1) {
                  newValue.splice(index, 1);
                } else {
                  newValue.push(item.value);
                }
                checkbox?.onChange?.(newValue);
              }}
            >
              {item.label}
            </span>
          ))}
        </div>
      )}
      <div className="flex-shrink-0" style={{ '--nutui-menu-scroll-fixed-z-index': 3000 }}>
        {menu}
      </div>
    </div>
  );
};

export default MenuWrap;
