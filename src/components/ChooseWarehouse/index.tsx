import { Picker, PickerOption, PickerProps } from '@nutui/nutui-react-taro';
import { useAsyncEffect } from 'ahooks';
import { isEmpty } from 'lodash';
import { useState } from 'react';
import { warehouseList } from './services';
import { YesNoStatus } from './types/YesNo';
export interface ChooseWarehouseProps extends Partial<PickerProps> {
  handleClose?: () => void;
  handleConfirm?: (options: PickerOption[], value: string[]) => void;
}
export default (props: Partial<ChooseWarehouseProps>) => {
  const { title = '选择仓库', ...rest } = props;
  const [warehouse, setWarehouse] = useState<PickerOption[]>([]);
  useAsyncEffect(async () => {
    const result = await warehouseList({ state: YesNoStatus.YES }).then(
      (s) => s?.warehouseSimpleRoList ?? [],
    );
    if (!isEmpty(result)) {
      setWarehouse(
        result?.map((t) => ({
          text: t.warehouseName!,
          value: t.id!,
        })),
      );
    }
  }, []);
  return <Picker title={title} options={warehouse} {...rest} onClose={props.handleClose} />;
};
