import { post } from '@/utils/request';
import { QueryPostDetailRequest } from './types/query.post.detail.request';
import { WarehouseListEntity } from './types/warehouse.list.entity';

/**
 * 查询所有仓库列表
 * @param params
 * @returns
 */
export const warehouseList = async (params?: Partial<QueryPostDetailRequest>) => {
  return post<WarehouseListEntity>(`/ipmswarehouse/warehouseStoreRelation/queryList`, {
    data: params,
  });
};
