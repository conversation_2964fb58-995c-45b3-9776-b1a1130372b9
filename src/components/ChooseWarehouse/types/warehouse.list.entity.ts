export interface WarehouseListEntity {
  warehouseSimpleRoList?: warehouseSimpleRoList[];
  warehouseStoreRelationRoList?: WarehouseStoreRelationRoList[];
}

export interface warehouseSimpleRoList {
  /**
   * 主键
   */
  id?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
}

export interface WarehouseStoreRelationRoList {
  /**
   * 主键
   */
  id?: string;
  /**
   * 是否默认门店，1:默认，0：否
   */
  isDefault?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * 门店id
   */
  storeId?: string;
  /**
   * 门店名称
   */
  storeName?: string;
  /**
   * 仓库id
   */
  warehouseId?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
}
