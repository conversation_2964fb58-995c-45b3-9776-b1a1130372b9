import { DeviceItem } from '@/packages/print/setting';
import usePrintStore from '@/stores/print';
import { ArrowRight } from '@nutui/icons-react-taro';
import { Cell } from '@nutui/nutui-react-taro';
import Taro, { useDidShow } from '@tarojs/taro';
import classNames from 'classnames';
import { useState } from 'react';

const BluePrintCell = () => {
  const [myDeviceItem, setMyDeviceItem] = useState<DeviceItem>();
  const printStore = usePrintStore();
  useDidShow(() => {
    setMyDeviceItem(Taro.getStorageSync('myDeviceItem'));
  });

  return (
    <Cell
      title={<span>打印机</span>}
      extra={
        <div className="flex items-center">
          {myDeviceItem ? (
            <span className="flex items-center whitespace-nowrap">
              <span
                className={classNames('mr-[5px] text-[80px]', {
                  'text-green-600': printStore.isConnect,
                  'text-red-600': !printStore.isConnect,
                })}
              >
                &middot;
              </span>
              {myDeviceItem.name}
            </span>
          ) : (
            <span className="text-gray-400">请选择</span>
          )}
          <ArrowRight color="#B2B2B2" size={16} />
        </div>
      }
      onClick={() => Taro.navigateTo({ url: '/packages/print/setting/index' })}
    />
  );
};

export default BluePrintCell;
