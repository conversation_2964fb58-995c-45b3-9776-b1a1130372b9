import { ConfigProvider, Divider } from '@nutui/nutui-react-taro';
import { isEmpty } from 'lodash';

export interface ItemsWithDividerProps {
  items?: Array<string | undefined>;
}
/**
 * 带竖线分割
 */
export default (
  props: ItemsWithDividerProps & Pick<React.HTMLAttributes<HTMLElement>, 'className'>,
) => {
  const { items = [], className } = props;
  return (
    <>
      {items &&
        items.map((t, index) => {
          if (!isEmpty(t)) {
            return (
              <>
                {index !== 0 && (
                  <ConfigProvider theme={{ nutuiDividerVerticalMargin: '0 8px' }}>
                    <Divider direction="vertical" className="mx-[0px]" />
                  </ConfigProvider>
                )}
                <span className={className}>{t}</span>
              </>
            );
          }
        })}
    </>
  );
};
