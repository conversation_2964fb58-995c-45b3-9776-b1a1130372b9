import { RadioGroup } from '@nutui/nutui-react-taro';
import { RadioGroupProps } from '@nutui/nutui-react-taro/dist/types/packages/radiogroup/radiogroup.taro';
import { RadioGroupShape } from '@nutui/nutui-react-taro/dist/types/packages/radiogroup/types';
import './index.scss';

type Option = {
  label: string;
  value: string | number;
  disabled?: boolean;
  shape?: RadioGroupShape;
  onChange?: (checked: boolean) => void;
};

// 计算子类型
type ToChildOption<T> = T extends {
  label: infer L;
  value;
  disabled?: infer D;
  shape?: infer S;
  onChange?: infer C;
}
  ? { label: L; value: string; disabled?: D; shape?: S; onChange?: C }
  : never;

export interface RadioButtonGroupProps extends Partial<Omit<RadioGroupProps, 'options'>> {
  options?: Option[];
}

const RadioButtonGroup = (props: RadioButtonGroupProps) => {
  const { options } = props;
  return (
    <RadioGroup
      {...props}
      options={options as unknown as ToChildOption<Option>[]}
      shape="button"
      direction={'horizontal'}
      className="radioGroup"
    />
  );
};

export default RadioButtonGroup;
