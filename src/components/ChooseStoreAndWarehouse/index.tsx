import ChooseStoreAndWarehouseModal, {
  ChooseStoreAndWarehouseModalProps,
} from '@/components/ChooseStoreAndWarehouseModal';
import { ArrowDown } from '@nutui/icons-react-taro';
import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import classnames from 'classnames';
import { useState } from 'react';

export interface ChooseStoreAndWarehouseProps extends ChooseStoreAndWarehouseModalProps {
  onConfirm?: (options: PickerOption[], value: string[]) => void;
  disabledColumns: boolean[];
  disabled?: boolean;
}

const ChooseStoreAndWarehouse = (props: ChooseStoreAndWarehouseProps) => {
  const { onConfirm, disabled = false, ...rest } = props;
  const [visible, setVisible] = useState(false);
  const [checkedOptions, setCheckedOptions] = useState<PickerOption[]>([]);
  // 仓库选择
  const warehouseClass = classnames('flex items-center text-[34px]', {
    'text-main font-semibold': checkedOptions.length !== 0,
    'text-[#777777]': checkedOptions.length === 0,
  });
  return (
    <div>
      <div
        onClick={() => {
          if (!disabled) {
            setVisible(true);
          }
        }}
        className={warehouseClass}
      >
        {checkedOptions.length === 0
          ? ' 请选择门店与仓库'
          : `${checkedOptions?.[0]?.text ?? ''}-
        ${checkedOptions?.[1]?.text ?? ''}`}
        {!disabled && (
          <span className="ml-2 flex items-center">
            <ArrowDown style={{ width: '32px' }} />
          </span>
        )}
      </div>
      <ChooseStoreAndWarehouseModal
        {...rest}
        visible={visible}
        handleClose={() => setVisible(false)}
        handleConfirm={(options, value) => {
          onConfirm?.(options, value as string[]);
          setCheckedOptions(options);
        }}
      />
    </div>
  );
};

export default ChooseStoreAndWarehouse;
