import { defaultTo } from 'lodash';
import CodeWithDivider from '../CodeWithDivider';

export enum ValueType {
  /**默认为字符串原样展示 */
  TEXT = 'text',
  /**当为数组时使用join(',') */
  ARRAY = 'array',
}
export interface Column {
  key: string;
  title: string;
  valueType?: ValueType;
}
export interface DescriptionsProps {
  columns: Column[];
  record?: Record<string, any>;
  emptyText?: string;
}

export default (
  props: DescriptionsProps & Pick<React.HTMLAttributes<HTMLElement>, 'className'>,
) => {
  const { columns = [], record = {}, emptyText = '', className = '' } = props;
  return (
    <div
      className={`rounded-[16px] bg-[#00000008] p-[28px] flex flex-col justify-start gap-[24px] ${className}`}
    >
      {columns.map(({ key, title, valueType }) => {
        const value = record[key];
        let returnValue = defaultTo(value, emptyText);
        switch (valueType) {
          case ValueType.ARRAY:
            returnValue = <CodeWithDivider key={key} title={title} items={value ?? []} />;
            break;
          default:
            returnValue = <span className="text-main truncate">{returnValue}</span>;
            break;
        }
        return (
          <div key={key} className="flex justify-start items-center">
            <span className="text-[#777777] whitespace-nowrap">{title}：</span>
            {returnValue}
          </div>
        );
      })}
    </div>
  );
};
