import CustomPicker from '@/components/CustomPicker';
import {useAsyncEffect} from 'ahooks';
import {isEmpty} from 'lodash';
import {useState} from 'react';
import {queryMemberAccountPage} from './services';
import {Divider, Popup, SafeArea, SearchBar} from "@nutui/nutui-react-taro";
import {ScrollView} from "@tarojs/components";
import {Checklist} from "@nutui/icons-react-taro";

export interface AccountSelectModalProps extends Partial<CustomPicker> {
  handleClose: () => void;
  handleConfirm: (options: { title: string; value: string }) => void;
  visibleAccount: boolean;
  belongToStore?: string[];
  placeholder?: string;
  title?: string;
  accountId?: string;
}

export default (props: AccountSelectModalProps) => {
  const {
    title = '选择账户',
    placeholder = '账户名称',
    visibleAccount,
    handleClose,
    handleConfirm,
    belongToStore,
    accountId,
  } = props;
  const [isPickerVisible, setIsPickerVisible] = useState<boolean>(false);
  const [accountList, setAccountList] = useState<{ title: string; value: string }[]>([]);
  useAsyncEffect(async () => {
    setIsPickerVisible(false);
    queryAccountList().then();
  }, [visibleAccount, belongToStore]);

  const queryAccountList = async (memberAccountName?: string) => {
    const result = await queryMemberAccountPage({belongToStore: belongToStore , memberAccountName: memberAccountName});
    const data = result.data;
    if (!isEmpty(data)) {
      const accountList =
        data.map((t) => ({
          title: t.memberAccountName,
          value: t.id,
        }));
      setAccountList(accountList);
      // 如果当前没有选择账户，默认选择
      if (isEmpty(accountId)) {
        handleSelect(accountList[0]);
      }
      if (visibleAccount) {
        setIsPickerVisible(true);
      }
    }
  };

  const handleSelect = (selectItem: { title: string; value: string }) => {
    handleConfirm(selectItem);
  };

  const handleSearch = (keyword?: string) => {
    queryAccountList(keyword).then();
  };

  return (
    <Popup visible={isPickerVisible} onClose={handleClose} title={title} position="bottom">
      <div>
        <SearchBar placeholder={placeholder} onSearch={handleSearch} onClear={() => queryAccountList()} />
        <ScrollView className="h-[60vh]" scrollY={true}>
          {accountList?.map((item) => (
            <div>
              <div
                className="flex flex-shrink-0 justify-center mx-[28px] py-[28px] leading-6"
                onClick={() => handleSelect(item)}
              >
                <div className="flex-1">
                  <div className="text-[32px] flex items-center flex-wrap">
                    <span className="mr-2">{item.title}</span>
                  </div>
                </div>
                <div className="flex items-center">
                  {accountId === item.value && <Checklist className="mr-1" color="red"/>}
                </div>
              </div>
              <div className="mx-[28px]">
                <Divider/>
              </div>
            </div>
          ))}
        </ScrollView>
        <SafeArea position={'bottom'}/>
      </div>
    </Popup>
  );
}
