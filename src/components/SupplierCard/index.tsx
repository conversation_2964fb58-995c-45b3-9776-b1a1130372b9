import { ArrowRight, IconFont } from '@nutui/icons-react-taro';
import classNames from 'classnames';
import defaultIcon from './imgs/default.svg';
import ChooseSupplierModal from "@/components/ChooseSupplierModal";
import {SupplierPostEntity} from "@/components/ChooseSupplierModal/types/supplier.post.entity";
import {useState} from "react";

export interface SupplierCardProps {
  className?: string;
  currentSupplier?: SupplierPostEntity | undefined;
  onConfirm: (data: SupplierPostEntity) => void;
  disable?: boolean;
}

const SupplierCard = (props: SupplierCardProps) => {
  const { className, onConfirm, currentSupplier, disable } = props;
  const [visible, setVisible] = useState(false);

  return (
    <div className="relative">
      <div
        className={`bg-white p-[24px] rounded-[16px] flex ${className ?? ''}`}
        onClick={() => setVisible(true)}
      >
        <IconFont name={defaultIcon} style={{ width: '100px', height: '100px' }} />
        <div className="flex-1 ml-[24px]">
          {currentSupplier ? (
            <div>
              <div>{currentSupplier?.supplierInfo?.supplierName}</div>
              <div className="text-[28px] text-thirdary mt-[10px]">
                应付总额：{currentSupplier?.totalPayableAmount ?? '-'}
              </div>
            </div>
          ) : (
            <div className="text-thirdary h-full flex items-center">请先选择供应商</div>
          )}
        </div>
        <div
          className={classNames('flex items-center', {
            'opacity-[0.5]': !currentSupplier,
          })}
        >
          <ArrowRight />
        </div>
      </div>
      <ChooseSupplierModal
        visible={visible}
        onClose={() => setVisible(false)}
        onConfirm={onConfirm}
        supplierId={currentSupplier?.supplierInfo?.id ?? ''}
      />
      {disable && <div className="absolute inset-0 bg-white opacity-40" />}
    </div>
  );
};

export default SupplierCard;
