import ChooseCstModal from '@/components/ChooseCstModal';
import { getCstDetail } from '@/components/ChooseCstModal/services';
import { CustomerEntity } from '@/components/ChooseCstModal/types/CustomerEntity';
import { CustomerSaveEntity } from '@/components/ChooseCstModal/types/CustomerSaveEntity';
import { ArrowRight } from '@nutui/icons-react-taro';
import { Tag } from '@nutui/nutui-react-taro';
import { Image } from '@tarojs/components';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import defaultIcon from './imgs/default.svg';

export interface CstCardProps {
  className?: string;
  cstId?: string;
  onConfirm: (data: CustomerSaveEntity) => void;
  disabled?: boolean;
  /** 是否财务收款 */
  isFin?: boolean;
}

const CstCard = (props: CstCardProps) => {
  const { className, onConfirm, cstId, disabled, isFin } = props;
  const [visible, setVisible] = useState(false);
  const [currentCst, setCurrentCst] = useState<CustomerSaveEntity>();

  useEffect(() => {
    if (cstId && disabled) {
      queryCstDetail();
    }
  }, [cstId]);

  const queryCstDetail = (selectedCst?: CustomerEntity) => {
    if (selectedCst?.cstId ?? cstId) {
      // @ts-ignore
      getCstDetail({ cstId: selectedCst?.cstId ?? cstId }).then((result) => {
        result = { ...result, totalReceivableAmount: selectedCst?.settle?.receivableAmount };
        setCurrentCst(result);
        onConfirm?.(result);
      });
    }
  };

  return (
    <div className="relative">
      <div
        className={`bg-white p-[24px] rounded-[16px] flex ${className ?? ''}`}
        onClick={() => setVisible(true)}
      >
        <Image
          src={currentCst?.images?.[0]?.url ?? defaultIcon}
          style={{ width: '50px', height: '50px' }}
          mode={'aspectFit'}
        />
        <div className="flex-1 ml-[24px]">
          {currentCst ? (
            <div>
              <div>{currentCst?.base?.cstName}</div>
              <div className="mt-[12px]">
                {currentCst?.settle?.credit && (
                  <>
                    <div className="flex gap-[12px] flex-wrap">
                      <Tag type="primary">挂帐客户</Tag>
                      {currentCst.tags?.map((item) => (
                        <Tag type="info">{item.tagName}</Tag>
                      ))}
                    </div>
                    {!isFin && (
                      <div className="text-[26px] text-thirdary mt-[16px]">
                        信用额度：已用{currentCst?.settle?.usedAmount ?? '-'}/可用
                        {currentCst?.settle?.availableAmount ?? '-'}
                      </div>
                    )}
                    {isFin && (
                      <div className="text-[26px] text-thirdary mt-[16px]">
                        应收金额：{currentCst?.totalReceivableAmount ?? '-'}
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          ) : (
            <div className="text-thirdary h-full flex items-center">请先选择客户</div>
          )}
        </div>
        {!disabled && (
          <div
            className={classNames('flex items-center', {
              'opacity-[0.5]': !currentCst,
            })}
          >
            <ArrowRight />
          </div>
        )}
      </div>
      <ChooseCstModal
        visible={visible}
        onClose={() => setVisible(false)}
        onConfirm={(selectedCst) => queryCstDetail(selectedCst)}
        cstId={cstId}
      />
      {disabled && <div className="absolute inset-0 bg-white opacity-0" />}
    </div>
  );
};

export default CstCard;
