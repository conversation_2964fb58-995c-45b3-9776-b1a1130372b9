import SupplierItem from '@/components/ChooseSupplierModal/components/SupplierItem';
import {
  queryPayablePageGroup,
  querySupplierPostList,
} from '@/components/ChooseSupplierModal/services';
import { SupplierPostEntity } from '@/components/ChooseSupplierModal/types/supplier.post.entity';
import { Divider, Popup, SafeArea, SearchBar } from '@nutui/nutui-react-taro';
import { isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import {ScrollView} from "@tarojs/components";

export interface ChooseSupplierModalProps {
  visible: boolean;
  onClose: () => void;
  supplierId?: string;
  onConfirm: (record: SupplierPostEntity) => void;
}

const ChooseSupplierModal = (props: ChooseSupplierModalProps) => {
  const { visible, onClose, supplierId, onConfirm } = props;
  const [list, setList] = useState<SupplierPostEntity[]>();

  useEffect(() => {
    queryList();
  }, [visible]);

  const queryList = async (keyword?: string) => {
    const { data: supplierList } = await querySupplierPostList({
      supplierName: keyword,
      supplierStatus: 1,
    });
    const supplierIdList = supplierList.map((t) => t.supplierInfo?.id ?? '');
    if (!isEmpty(supplierIdList)) {
      const pageSize = supplierIdList.length;
      const result = await queryPayablePageGroup({
        sellerIdList: supplierIdList,
        pageSize,
        pageNo: 1,
      });
      let map = new Map<string, number>();
      const finPayableList = result?.data?.[0]?.finPayableList;
      if (finPayableList) {
        finPayableList.forEach((t) => {
          const sellerId = t.sellerId;
          const payableAmountYuan = t.remainPayableAmountYuan;
          if (sellerId && payableAmountYuan) {
            // map中存在，则与payableAmountYuan相加
            if (map.has(sellerId)) {
              const oldValue = map.get(sellerId);
              if (oldValue) {
                map.set(sellerId, oldValue + payableAmountYuan);
              }
            } else {
              map.set(sellerId, payableAmountYuan);
            }
          }
        });
        const updateList = supplierList.map((t) => ({
          ...t,
          totalPayableAmount: map.get(t.supplierInfo?.id ?? ''),
        }));
        setList(updateList);
      }
    }
  };

  const handleSearch = (keyword?: string) => {
    queryList(keyword);
  };

  return (
    <Popup visible={visible} onClose={onClose} title="选择供应商" position="bottom">
      <div>
        <SearchBar placeholder="供应商名称" onSearch={handleSearch} onClear={() => queryList()} />
        <ScrollView className="h-[60vh]" scrollY={true}>
          {list?.map((item) => (
            <>
              <SupplierItem
                record={item}
                onSelect={(value) => {
                  onConfirm(value);
                  onClose();
                }}
                supplierId={supplierId ?? ''}
              />
              <div className="mx-[28px]">
                <Divider />
              </div>
            </>
          ))}
        </ScrollView>
        <SafeArea position={'bottom'} />
      </div>
    </Popup>
  );
};

export default ChooseSupplierModal;
