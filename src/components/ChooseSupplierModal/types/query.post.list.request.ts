import { PaginationRequest } from '@/types/PaginationRequest';

export interface QueryPostListRequest extends PaginationRequest {
  /**
   * 创建开始时间
   */
  startTime?: string;
  /**
   * 联系人，查默认联系人
   */
  concatPerson?: string;
  /**
   * 联系方式，查默认联系人
   */
  concatPhone?: string;
  /**
   * 创建结束时间
   */
  endTime?: string;
  /**
   * 门店系统侧零售商ID
   */
  memberId?: string;
  /**
   * 门店系统侧零售商ID列表
   */
  memberIdList?: string[];
  /**
   * None
   */
  pageNo?: number;
  /**
   * None
   */
  pageSize?: number;
  /**
   * None
   */
  startRow?: number;
  /**
   * 供应商code
   */
  supplierCode?: string;
  /**
   * 供应商name
   */
  supplierName?: string;
  /**
   * 供应商状态
   */
  supplierStatus?: number;
}
