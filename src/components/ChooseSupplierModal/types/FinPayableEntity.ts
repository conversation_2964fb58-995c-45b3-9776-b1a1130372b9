export interface FinPayableEntity {
  payableAmount?: string;
  storeId?: string;
  storeName?: string;
  remainPayableAmount?: string;
  buyerName?: string;
  sellerId?: string;
  sellerName?: string;
  buyerId?: string;
  payableFlag?: number;
  billDate?: string;
  paymentAmount?: string;
  orderAmount?: string;
  ledgerType?: number;
  id?: string
  currPayAmount?: string;
  payableAmountYuan?: number;
  orderAmountYuan?: number;
  paymentAmountYuan?: number;
  remainPayableAmountYuan?: number;
  orderNo?: string;
  storeIdList?: string[];
  sellerIdList?: string[];
}
