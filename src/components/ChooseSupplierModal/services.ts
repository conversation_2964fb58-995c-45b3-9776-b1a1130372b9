import { FinPayableEntity } from "@/components/ChooseSupplierModal/types/FinPayableEntity";
import { FinPayableGroupEntity } from "@/components/ChooseSupplierModal/types/FinPayableGroupEntity";
import { QueryPostListRequest } from "@/components/ChooseSupplierModal/types/query.post.list.request";
import { SupplierPostEntity } from "@/components/ChooseSupplierModal/types/supplier.post.entity";
import { PageRequestParamsType } from "@/types/PageRequestParamsType";
import { post } from '@/utils/request';
import { PageResponseDataType } from "../../../types/PageResponseDataType";

/**
 * 供应商列表查询
 * @param params
 * @returns
 */
export const querySupplierPostList = async (params: Partial<QueryPostListRequest>) => {
  return post<PageResponseDataType<SupplierPostEntity>>(
    `/ipmspurchase/vendor/SupplierFacade/queryPage`,
    {
      data: params,
    },
  );
};

/**
 * 应付分页查询
 *
 * @param params
 * @returns
 */
export const queryPayablePageGroup = async (params: Partial<FinPayableEntity> & PageRequestParamsType) => {
  return post<PageResponseDataType<FinPayableGroupEntity>>(`/ipmsaccount/queryPayablePageGroup`, {
    data: params,
  });
};
