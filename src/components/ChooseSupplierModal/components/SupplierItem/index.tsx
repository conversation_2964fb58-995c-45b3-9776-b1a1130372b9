import { SupplierPostEntity } from '@/components/ChooseSupplierModal/types/supplier.post.entity';
import { Checklist } from '@nutui/icons-react-taro';

export interface SupplierItemProps {
  record: SupplierPostEntity;
  onSelect: (value: SupplierPostEntity) => void;
  supplierId?: string;
}

const CstItem = (props: SupplierItemProps) => {
  const { record, onSelect, supplierId } = props;

  const defaultContact =
    record?.supplierConcatList?.find((item) => item.isDefault) ?? record?.supplierConcatList?.[0];
  return (
    <div
      className="flex flex-shrink-0 justify-center mx-[28px] py-[28px] leading-6"
      onClick={() => onSelect(record)}
    >
      <div className="flex-1">
        <div className="text-[32px] flex items-center flex-wrap">
          <span className="mr-2">{record?.supplierInfo?.supplierName}</span>
        </div>
        <div className="text-thirdary text-[28px]">
          <div>
            {defaultContact?.concatPerson ?? '暂无联系人'} |{' '}
            {defaultContact?.concatPhone ?? '暂无联系方式'}
          </div>
          <div>应付金额：{record?.totalPayableAmount ?? 0}</div>
        </div>
      </div>
      <div className="flex items-center">
        {supplierId === record?.supplierInfo?.id && <Checklist className="mr-1" color="red" />}
      </div>
    </div>
  );
};

export default CstItem;
