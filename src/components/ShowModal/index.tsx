import { Dialog, Divider } from '@nutui/nutui-react-taro';

export interface ShowModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  listData?: Array<string | undefined>;
}

const ShowModal = (props: ShowModalProps) => {
  const { visible, onClose, listData, title } = props;

  return (
    <Dialog
      visible={visible}
      title={title}
      lockScroll
      onClose={onClose}
      hideCancelButton={true}
      onOverlayClick={() => {
        return;
      }}
    >
      <div className="flex flex-col max-h-[50vh]">
        <div className="flex-1 min-h-0 overflow-scroll">
          {listData?.map((item) => (
            <div className="mx-[28px]">
              <div className="text-black/90 text-[32px] py-[32px]">{item}</div>
              <Divider />
            </div>
          ))}
        </div>
      </div>
    </Dialog>
  );
};

export default ShowModal;
