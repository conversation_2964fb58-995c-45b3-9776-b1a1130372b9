import { Button, CalendarCard } from '@nutui/nutui-react-taro';
import { CalendarCardProps } from '@nutui/nutui-react-taro/dist/types/packages/calendarcard/calendarcard';
import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';

export interface CalendarRangeCardChooseProps extends Partial<Omit<CalendarCardProps, 'value'>> {
  value?: Date[];
}

const CalendarRangeCardChoose = (props: CalendarRangeCardChooseProps) => {
  const { value = [], onChange, ...rest } = props;
  const [values, setValues] = useState<Date[]>([]);

  useEffect(() => {
    if (value?.length === 2) {
      setValues(value);
    }
  }, [value]);

  return (
    <div className="pb-[28px]">
      <CalendarCard
        type="range"
        onChange={(e) => setValues(e as Date[])}
        value={values}
        {...rest}
      />
      <div className="flex mt-[28px] gap-[22px]">
        <Button
          className="flex-1"
          onClick={() => {
            setValues([]);
            onChange?.([]);
          }}
        >
          重置
        </Button>
        <Button
          className="flex-1"
          type={'primary'}
          onClick={() => {
            // @ts-ignore
            if (values?.length === 2) {
              onChange?.(values);
            } else {
              Taro.showToast({ title: '请选择时间段', icon: 'none' });
            }
          }}
        >
          确定
        </Button>
      </div>
    </div>
  );
};

export default CalendarRangeCardChoose;
