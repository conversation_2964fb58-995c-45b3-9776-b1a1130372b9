import { PageResponseDataType } from '../../../types/PageResponseDataType';
import { MemberAccountEntity } from './types/MemberAccountEntity';

import { post } from '@/utils/request';

/**
 * 查询门店账户列表
 * @param params
 * @returns
 */
export const queryMemberAccountPage = (params: { belongToStore: string[] | undefined }) => {
  return post<PageResponseDataType<MemberAccountEntity>>(`/ipmsaccount/memberAccount/queryByPage`, {
    data: params,
  });
};
