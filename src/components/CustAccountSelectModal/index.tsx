import CustomPicker from '@/components/CustomPicker';
import { useAsyncEffect } from 'ahooks';
import { isEmpty } from 'lodash';
import { useState } from 'react';
import { queryMemberAccountPage } from './services';

export interface AccountSelectModalProps extends Partial<CustomPicker> {
  handleClose: () => void;
  handleConfirm: (options: { title: string; value: string }[]) => void;
  visibleAccount: boolean;
  belongToStore?: string[];
  placeholder?: string;
  title?: string;
}

export default (props: AccountSelectModalProps) => {
  const {
    title = '选择账户',
    placeholder = '账户名称',
    visibleAccount,
    handleClose,
    handleConfirm,
    belongToStore,
  } = props;
  const [isPickerVisible, setIsPickerVisible] = useState<boolean>(false);
  const [accountList, setAccountList] = useState<{ title: string; value: string }[]>([]);
  useAsyncEffect(async () => {
    setIsPickerVisible(false);
    const result = await queryMemberAccountPage({ belongToStore: belongToStore });
    const data = result.data;
    if (!isEmpty(data)) {
      setAccountList(
        data.map((t) => ({
          title: t.memberAccountName,
          value: t.id,
        })),
      );
      if (visibleAccount) {
        setIsPickerVisible(true);
      }
    }
  }, [visibleAccount, belongToStore]);

  if (accountList.length > 0 && isPickerVisible) {
    const handleSelect = (selectItems: string[]) => {
      if (isEmpty(selectItems)) return;
      const selectedList = accountList.filter((item) => selectItems.includes(item.value));
      handleConfirm(selectedList);
    };

    return (
      <CustomPicker
        visible={isPickerVisible}
        onClose={handleClose}
        title={title}
        placeholder={placeholder}
        items={accountList}
        onConfirm={handleSelect}
        multiple={false}
      />
    );
  }

  return null;
};
