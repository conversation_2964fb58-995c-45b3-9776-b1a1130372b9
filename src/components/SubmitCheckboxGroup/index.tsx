import iconCheckboxDef from '@/assets/icons/icon_checkbox_def.png';
import iconCheckboxSel from '@/assets/icons/icon_checkbox_sel.png';
import { SubmitCheckboxEnum } from '@/packages/sales/returns/operation/types/SubmitCheckboxEnum';
import { Image } from '@nutui/nutui-react-taro';
/**
 * 确认结算/一键入库/提交后打印
 */
export default () => {
  const options = [
    {
      label: '确认结算',
      value: SubmitCheckboxEnum.CONFIRM_REFUND,
      checked: true,
    },
    {
      label: '一键入库',
      value: SubmitCheckboxEnum.DIRECT_IN,
      checked: false,
    },
  ];
  return (
    <div className="flex justify-start items-center gap-[80px]">
      {options &&
        options.map((option, index) => (
          <div
            className="flex justify-start items-center gap-[16px]"
            onClick={() => {
              options[index]['checked'] = !options[index]['checked'];
            }}
          >
            <Image
              src={option.checked ? iconCheckboxSel : iconCheckboxDef}
              width="40px"
              height="40px"
            ></Image>
            <span>{option.label}</span>
          </div>
        ))}
    </div>
  );
};
