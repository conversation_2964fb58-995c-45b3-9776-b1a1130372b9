import { CustomerEntity } from '@/packages/finance/receive/list/types/CustomerEntity';
import { getCstList } from '@/packages/finance/receive/services';
import { Checklist } from '@nutui/icons-react-taro';
import { SearchBar, VirtualList } from '@nutui/nutui-react-taro';
import { useEffect, useState } from 'react';

export interface FilterCustomerPickerProps {
  cstId?: string;
  onChange: (cstId?: string) => void;
}

const FilterCustomerPicker = (props: FilterCustomerPickerProps) => {
  const { cstId, onChange } = props;
  const [keyword, setKeyword] = useState<string>('');
  const [list, setList] = useState<CustomerEntity[]>([]);

  useEffect(() => {
    getCstList({ limit: 50, keyword }).then((result) => {
      setList(result ?? []);
    });
  }, [keyword]);

  console.log('list', list);

  return (
    <div className="h-[50vh] flex w-full flex-col">
      <SearchBar
        placeholder="客户名称"
        className="!p-0 mb-[24px]"
        value={keyword}
        onSearch={setKeyword}
        onClear={() => setKeyword('')}
      />
      <div className="flex-1 min-h-0 overflow-y-scroll">
        {list.length === 0 ? (
          <div className="py-[48px] flex justify-center items-center text-gray-400">
            客户列表为空
          </div>
        ) : (
          <VirtualList
            itemHeight={30}
            list={list}
            itemRender={(record: CustomerEntity) => (
              <div
                className="flex justify-between leading-[48px] text-[28px] px-[28px]"
                onClick={() => {
                  onChange(cstId === record.cstId ? '' : record.cstId);
                }}
              >
                <span className="flex-1 overflow-hidden overflow-ellipsis whitespace-nowrap">
                  {record.cstName}
                </span>
                {cstId === record.cstId && (
                  <span className="flex-shrink-0">
                    <Checklist color="#F83431" />
                  </span>
                )}
              </div>
            )}
          />
        )}
      </div>
    </div>
  );
};

export default FilterCustomerPicker;
