import scan from '@/assets/scan.svg';
import { scanCode } from "@/utils/scanCode";
import { Add, ArrowDown, IconFont, Search } from '@nutui/icons-react-taro';
import { Divider, Popover, SearchBar } from '@nutui/nutui-react-taro';
import { defaultTo } from 'lodash';
import { useState } from 'react';

/**
 * itemList 选择内容
 * defaultItem：默认显示内容
 * addUrl：新增跳转链接
 * inputValue：返回值json 可以直接丢到请求参数中
 * isShowAdd：是否显示新增按钮
 */
interface Item {
  key: string;
  name: string;
  scanShow?: boolean;
  placeholder?: string;
}

interface CustomSearchBarProps {
  itemList: Item[];
  defaultItem: Item;
  addUrl?: (e) => void;
  inputValue: (e) => void;
  isShowAdd?: boolean;
}

const CustomSearchBar: React.FC<CustomSearchBarProps> = ({
  itemList,
  defaultItem,
  addUrl,
  inputValue,
  isShowAdd = true,
}) => {
  const [showItem, setShowItem] = useState<boolean>(false);
  const [valueInfo, setValueInfo] = useState<string>('');
  const [showScan, setShowScan] = useState<boolean>(defaultItem?.scanShow!);
  const [selectItem, setSelectItem] = useState<Item>(defaultItem);
  const handleChange = (value: string) => {
    inputValue({ [selectItem?.key]: value });
  };
  return (
    <SearchBar
      leftIn={
        itemList ? <Popover
          className="demo-popover"
          location="bottom-start"
          visible={showItem}
          list={itemList}
          onSelect={(item: Item) => {
            console.log('selectItem', selectItem);
            //先置空之前的值
            inputValue({ [selectItem?.key]: '' });
            setValueInfo('');
            setSelectItem(item);
            setShowScan(item.scanShow!);
          }
          }
          onClick={() => {
            showItem ? setShowItem(false) : setShowItem(true);
          }}
        >
          <div className="flex items-center text-[28px] py-[16px]">
            {selectItem?.name}
            <ArrowDown size={12} style={{ marginLeft: '5px' }} />
            <Divider direction="vertical" />
          </div>
        </Popover> :
          <Search />
      }
      value={valueInfo}
      onSearch={handleChange}
      onClear={() => handleChange('')}
      onChange={(val: string) => {
        setValueInfo(val);
      }}
      //autoFocus={true}
      placeholder={defaultTo(selectItem?.placeholder, '请输入')}
      rightIn={
        showScan && (
          <div
            className="flex items-center"
            onClick={() => {
              scanCode().then(result => {
                if (result) {
                  setValueInfo(result);
                  handleChange(result);
                }
              })
            }}
          >
            <IconFont name={scan} style={{ width: '31px' }} />
          </div>
        )
      }
      right={isShowAdd && <Add onClick={addUrl} />}
      style={{ '--nutui-searchbar-padding': '0px', '--nutui-searchbar-input-height': '36px' }}
    />
  );
};

export default CustomSearchBar;
