import { Divider } from '@nutui/nutui-react-taro';
import classNames from 'classnames';
import React from 'react';

export interface DetailListItem {
  label: React.ReactNode;
  value: React.ReactNode;
}

export interface DetailListProps {
  dataSource?: DetailListItem[];
  className?: string;
  justified?: boolean; // 是否两端对齐
  labelWidth?: number | string; // label的宽度
  colon?: boolean; // 是否显示冒号
  divider?: boolean; // 是否显示分割线
  labelColor?: string; // label的颜色
}

const DetailList = (props: DetailListProps) => {
  const {
    dataSource = [],
    className = '',
    justified = false,
    labelWidth = '',
    colon = false,
    divider = false,
    labelColor = 'text-secondary',
  } = props;

  if (dataSource.length === 0) {
    return null;
  }

  return (
    <div className={`leading-[2] text-xs ${className}`}>
      {dataSource.map((item, index) => (
        <>
          {index !== 0 && divider && <Divider />}
          <div
            className={classNames('flex', {
              'justify-between': justified,
            })}
          >
            <span className={`${labelColor} flex flex-shrink-0`} style={{ width: labelWidth }}>
              {item.label}
              {colon && <span>:&ensp;</span>}
            </span>
            <span className="break-all">{item.value ?? '-'}</span>
          </div>
        </>
      ))}
    </div>
  );
};

export default DetailList;
