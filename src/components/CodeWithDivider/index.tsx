import down from '@/assets/down.svg';
import { IconFont } from '@nutui/icons-react-taro';
import Taro from '@tarojs/taro';
import { useState } from 'react';

export interface CodeWithDividerProps {
  items?: Array<string | undefined>;
  title?: string;
}
/**
 * 多个值下拉查看
 */
export default (
  props: CodeWithDividerProps & Pick<React.HTMLAttributes<HTMLElement>, 'className'>,
) => {
  const { items = [], className, title } = props;
  const [visible, setVisible] = useState(false);
  return (
    <>
      <div>
        {items && (
          <div
            className="flex items-center"
            onClick={(e) => {
              if (items.length > 1) {
                setVisible(true);
                Taro.showModal({
                  content: items.toString(),
                  showCancel: false,
                });
              }
              e.stopPropagation();
            }}
          >
            <div>{items[0]}</div>
            {items.length > 1 && (
              <div className="pl-[8px]">
                <IconFont name={down} style={{ width: '24px', height: '24px' }} />
              </div>
            )}
          </div>
        )}
      </div>
      {/* <ShowModal
        visible={visible}
        title={title}
        listData={items}
        onClose={() => {
          setVisible(false);
        }}
      /> */}
    </>
  );
};
