import React, { useEffect, useState } from 'react';
import {Button} from "@nutui/nutui-react-taro";

interface StableStepperProps {
  value: number | string;
  min?: number;
  max?: number;
  step?: number;
  disabled?: boolean;
  onChange?: (val: number | string) => void;
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void;
  className?: string;
  style?: React.CSSProperties;
}

export default function StableStepper({
                                        value,
                                        min = 0,
                                        max = 999999,
                                        step = 1,
                                        disabled = false,
                                        onChange,
                                        onClick,
                                        className = '',
                                        style = {},
                                      }: StableStepperProps) {
  const [inputVal, setInputVal] = useState<string>(String(value ?? ''));

  // 外部 value 变化时同步内部值（保持受控）
  useEffect(() => {
    if (String(value ?? '') !== inputVal) {
      setInputVal(String(value ?? ''));
    }
  }, [value]);

  const clamp = (val: number) => Math.max(min, Math.min(max, val));

  const handleInput = (v: string) => {
    setInputVal(v);
  };

  const handleBlur = () => {
    const parsed = parseInt(inputVal, 10) || 0;
    const clamped = clamp(parsed);
    onChange?.(clamped);
  };

  const changeByStep = (delta: number) => {
    if (disabled) return;
    const current = parseInt(String(inputVal || '0'), 10);
    const next = clamp(current + delta);
    setInputVal(String(next));
    onChange?.(next);
  };

  return (
    <div
      className={`flex items-center gap-[2px] ${className}`}
      onClick={onClick}
      style={style}
    >
      <Button
        disabled={disabled || parseInt(inputVal || '0') <= min}
        fill={"none"}
        onClick={() => changeByStep(-step)}
        className="h-[60px] leading-[60px] w-[60px] text-center p-0 !bg-[#f5f5f5]"
      >
        -
      </Button>

      <input
        type="number"
        value={inputVal}
        onChange={e => handleInput(e.target.value)}
        onBlur={handleBlur}
        disabled={disabled}
        className="w-[100px] h-[64px] leading-[64px] rounded text-center box-border bg-[#f5f5f5]"
      />

      <Button
        disabled={disabled || parseInt(inputVal || '0') >= max}
        fill={"none"}
        onClick={() => changeByStep(step)}
        className="h-[60px] leading-[60px] w-[60px] text-center p-0 !bg-[#f5f5f5]"
      >
        +
      </Button>
    </div>
  );
}
