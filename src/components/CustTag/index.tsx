export default (props: {
  text?: string;
  borderWidth?: number;
  radius?: number;
  borderColor?: string;
  bgColor?: string;
  textColor?: string;
  paddingX?: number;
  paddingY?: number;
}) => {
  const {
    text,
    borderWidth = 1,
    borderColor = '#FCAEADFF',
    bgColor = '#FFF4F4',
    textColor = '#F49C1F',
    radius = 4,
    paddingX = 12,
    paddingY = 6,
  } = props;
  // const className = classNames('border-solid', {
  //   [`bg-[${bgColor}]`]: true,
  //   [`text-[${textColor}]`]: true,
  //   [`border-[${borderColor}]`]: true,
  //   [`border-[${borderWidth}px]`]: true,
  //   [`rounded-[${radius}px]`]: true,
  //   [`px-[${paddingX}px]`]: true,
  //   [`py-[${paddingY}px]`]: true,
  // });
  // console.log(className);
  return (
    <span className="border-solid bg-[#FFF4F4] text-[#F49C1F] border-[#FCAEADFF] border-[1px] rounded-[4px] px-[12px] py-[6px] text-[20px]">
      {text}
    </span>
  );
};
