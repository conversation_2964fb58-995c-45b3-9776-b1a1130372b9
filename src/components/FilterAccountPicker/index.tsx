import { SupplierList } from "@/packages/finance/payment/list/types/supplier.post.entity";
import { Checklist } from '@nutui/icons-react-taro';
import { SearchBar, VirtualList } from '@nutui/nutui-react-taro';
import { useEffect, useState } from 'react';
import { queryMemberAccountPage } from "../AccountSelectModal/services";

export interface FilterAccountPickerProps {
  accountId?: string;
  onChange: (accountId?: string) => void;
}

const FilterAccountPicker = (props: FilterAccountPickerProps) => {
  const { accountId, onChange } = props;
  const [keyword, setKeyword] = useState<string>('');
  const [list, setList] = useState<SupplierList[]>([]);
  const [allSupplierList, setAllSupplierList] = useState<SupplierList[]>([]);

  useEffect(() => {
    queryMemberAccountPage({}).then((result) => {
      // setList(result ?? []);
      // setAllSupplierList(result ?? []);
    });
  }, []);

  useEffect(() => {
    if (!keyword) {
      setList(allSupplierList);
    }
    setList(allSupplierList?.filter((item) => item.supplierName?.includes(keyword)) ?? []);
  }, [keyword]);

  return (
    <div className="h-[50vh] flex w-full flex-col">
      <SearchBar
        placeholder="供应商名称"
        className="!p-0 mb-[24px]"
        value={keyword}
        onSearch={setKeyword}
        onClear={() => setKeyword('')}
      />
      <div className="flex-1 min-h-0 overflow-y-scroll">
        {list.length === 0 ? (
          <div className="py-[48px] flex justify-center items-center text-gray-400">
            供应商列表为空
          </div>
        ) : (
          <VirtualList
            itemHeight={30}
            list={list}
            itemRender={(record: SupplierList) => (
              <div
                className="flex justify-between leading-[48px] text-[28px] px-[28px]"
                onClick={() => {
                  onChange(accountId === record.supplierId ? '' : record.supplierId);
                }}
              >
                <span className="flex-1 overflow-hidden overflow-ellipsis whitespace-nowrap">
                  {record.supplierName}
                </span>
                {accountId === record.supplierId && (
                  <span className="flex-shrink-0">
                    <Checklist color="#F83431" />
                  </span>
                )}
              </div>
            )}
          />
        )}
      </div>
    </div>
  );
};

export default FilterAccountPicker;
