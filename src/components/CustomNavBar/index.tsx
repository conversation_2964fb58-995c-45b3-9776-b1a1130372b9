import { ArrowLeft } from '@nutui/icons-react-taro';
import { NavBar } from '@nutui/nutui-react-taro';
import { View } from '@tarojs/components';
import './index.scss';

const CustomNavBar = ({ title, showBack = true }) => {
  const statusBarHeight = 0;
  const navBarHeight = statusBarHeight + 44;

  return (
    <>
      <View className="custom-header" style={{ height: `${navBarHeight}px` }}>
        <NavBar
          style={{ marginTop: `${statusBarHeight}px`, backgroundColor: 'transparent' }}
          back={
            showBack && (
              <span className="w-[70px] flex">
                <ArrowLeft size={18} />
              </span>
            )
          }
          onBackClick={() => {
            history.back();
          }}
          titleAlign={showBack ? 'center' : 'left'}
        >
          <span>{title}</span>
        </NavBar>
      </View>
      <div style={{ height: `${navBarHeight}px` }} />
    </>
  );
};

export default CustomNavBar;
