.custom-header {
  background-image: url("../../assets/bgTop.png");
  background-repeat: no-repeat;
  background-size: cover;
  position: fixed;
  width: 100%;
  box-sizing: content-box;
  top: 0;
  left: 0;
  z-index: 1000;
  padding-top: env(safe-area-inset-top);

  .title {
    font-size: 34px;
    font-weight: bold;
    color: #111111;
    align-items: center;
    padding-left: 30px;
  }
}

page, html {
  background: url('../../assets/bgTop.png') #F5F5F5 no-repeat fixed;
  background-size: contain;
}

.nut-navbar-left {
  background-image: url("../../assets/bgTop.png");
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: contain;
  height: 92%;

}

.nut-navbar-right {
  background-image: url("../../assets/bgTop.png");
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: contain;
  height: 92%;
}
