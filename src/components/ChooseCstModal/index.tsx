import CstItem from '@/components/ChooseCstModal/components/CstItem';
import { getCstList } from '@/components/ChooseCstModal/services';
import { CustomerEntity } from '@/components/ChooseCstModal/types/CustomerEntity';
import { Divider, Popup, SafeArea, SearchBar } from '@nutui/nutui-react-taro';
import { ScrollView } from '@tarojs/components';
import { useEffect, useState } from 'react';

export interface ChooseCstModalProps {
  visible: boolean;
  onClose: () => void;
  cstId?: string;
  onConfirm: (record: CustomerEntity) => void;
}

const ChooseCstModal = (props: ChooseCstModalProps) => {
  const { visible, onClose, cstId, onConfirm } = props;
  const [list, setList] = useState<CustomerEntity[]>([]);

  useEffect(() => {
    queryList();
  }, []);

  const queryList = async (keyword?: string) => {
    const cstList = await getCstList({
      keyword,
      needSettle: true,
      needContact: true,
      needTag: true,
      cstStatus: 0,
    });
    setList(cstList);
  };

  const handleSearch = (keyword?: string) => {
    queryList(keyword);
  };

  console.log('list', list);

  return (
    <Popup visible={visible} onClose={onClose} title="选择客户" position="bottom">
      <div>
        <SearchBar placeholder="客户名称" onSearch={handleSearch} onClear={() => queryList()} />
        <ScrollView className="h-[60vh]" scrollY={true}>
          {list?.map((item) => (
            <>
              <CstItem
                record={item}
                onSelect={(value) => {
                  onConfirm(value);
                  onClose();
                }}
                cstId={cstId}
              />
              <div className="mx-[28px]">
                <Divider />
              </div>
            </>
          ))}
        </ScrollView>
        <SafeArea position={'bottom'} />
      </div>
    </Popup>
  );
};

export default ChooseCstModal;
