import { CustomerEntity } from '@/components/ChooseCstModal/types/CustomerEntity';
import { CustomerSaveEntity } from '@/components/ChooseCstModal/types/CustomerSaveEntity';
import { GetCstListRequest } from '@/components/ChooseCstModal/types/get.cst.list.request';
import { post } from '@/utils/request';

/**
 * 客户模糊查询
 */
export const getCstList = (params: GetCstListRequest) => {
  return post<CustomerEntity[]>(`/ipmscst/CstManageFacade/getCstList`, {
    data: params,
  });
};

/**
 * 用户管理-获取客户详情
 * @param params
 * @returns
 */
export const getCstDetail = async (params: { cstId: string }) => {
  return post<CustomerSaveEntity>(`/ipmscst/CstManageFacade/getCstDetail`, {
    data: params,
  });
};
