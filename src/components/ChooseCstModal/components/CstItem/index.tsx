import { CustomerEntity } from '@/components/ChooseCstModal/types/CustomerEntity';
import { Checklist } from '@nutui/icons-react-taro';
import { Tag } from '@nutui/nutui-react-taro';

export interface CstItemProps {
  record: CustomerEntity;
  onSelect: (value: CustomerEntity) => void;
  cstId?: string;
}

const CstItem = (props: CstItemProps) => {
  const { record, onSelect, cstId } = props;

  const defaultContact = record?.contacts?.[0];

  return (
    <div
      className="flex flex-shrink-0 justify-center mx-[28px] py-[28px]"
      onClick={() => onSelect(record)}
    >
      <div className="flex-1">
        <div className="text-[32px] leading-[1.6] flex items-center flex-wrap">
          <span className="mr-2">{record.cstName}</span>
          <div className="flex gap-[12px] flex-wrap">
            {record.settle?.credit && <Tag type="primary">挂帐客户</Tag>}
            {record.tags?.map((item) => (
              <Tag type="info">{item.tagName}</Tag>
            ))}
          </div>
        </div>
        <div className="text-thirdary text-[28px] leading-[1.6]">
          <div>
            {defaultContact?.name ?? '暂无联系人'} | {defaultContact?.phone ?? '暂无联系方式'}
          </div>
          {record?.settle?.credit && (
            <>
              <div>
                信用额度: 已用{record.settle?.usedAmount ?? '-'}/可用
                {record?.settle?.availableAmount ?? '-'}
              </div>
              <div>应收金额: {record?.settle?.receivableAmount ?? '-'}</div>
            </>
          )}
        </div>
      </div>
      <div className="flex items-center">
        {cstId === record.cstId && <Checklist className="mr-1" color="red" />}
      </div>
    </div>
  );
};

export default CstItem;
