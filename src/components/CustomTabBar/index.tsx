import { SafeArea } from '@nutui/nutui-react-taro';
import Taro from "@tarojs/taro";
import classNames from "classnames";
import homeIcon from '../../assets/tabBar/home.png';
import homeIconSelected from '../../assets/tabBar/home_selected.png';
import mineIcon from '../../assets/tabBar/mine.png';
import mineIconSelected from '../../assets/tabBar/mine_selected.png';
import workbenchIcon from '../../assets/tabBar/workbench.png';
import workbenchIconSelected from '../../assets/tabBar/workbench_selected.png';

const tabBarData = [
  {
    pagePath: 'pages/home/<USER>',
    text: '首页',
    iconPath: homeIcon,
    selectedIconPath: homeIconSelected,
  },
  {
    pagePath: 'pages/workbench/index',
    text: '工作台',
    iconPath: workbenchIcon,
    selectedIconPath: workbenchIconSelected,
  },
  // {
  //   pagePath: 'pages/report/index',
  //   text: '报表',
  //   iconPath: reportIcon,
  //   selectedIconPath: reportIconSelected,
  // },
  {
    pagePath: 'pages/mine/index',
    text: '我的',
    iconPath: mineIcon,
    selectedIconPath: mineIconSelected,
  },
]

export interface CustomTabBarProps {
  activeTabIndex?: number;
}

export default function CustomTabBar(props: CustomTabBarProps) {
  const { activeTabIndex = 0 } = props;

  return <div>
    <div className="fixed left-0 right-0 bottom-0 bg-white py-[8px]">
      <div className="flex">
        {tabBarData.map((item, index) =>
          <div
            className={classNames("flex-1 flex flex-col justify-center items-center", {
              "!text-[#F49C1F]": index === activeTabIndex,
              "!text-[#33333]": index !== activeTabIndex,
            })}
            key={item.pagePath}
            onClick={() => {
              Taro.navigateTo({ url: `/${item.pagePath}` });
            }}
          >
            <div><img src={index === activeTabIndex ? item.selectedIconPath : item.iconPath} width={28} height={28} /></div>
            <div className="text-[20px] mt-[6px]">{item.text}</div>
          </div>)}
      </div>
      <SafeArea position={"bottom"} />
    </div>
    <div>
      <div className="h-[90px]" />
      <SafeArea position={"bottom"} />
    </div>
  </div>
}
