import { PostSelect } from '@/packages/finance/payment/list/types/post.select';
import { GetSessionResponse } from '@/types/GetSession.response';
import { YesNoStatus } from '@/types/YesNo';
import { post } from '@/utils/request';
import { ResponseDataType } from '../../types/ResponseDataType';

export const getSession = () => {
  return post<ResponseDataType<GetSessionResponse>>(`/ipmspassport/LoginFacade/getSession`, {
    origin: true,
    silence: true,
    data: {},
  });
};
/**
 * 菜单按钮权限列表查询
 * @param params
 * 类型：1菜单，2按钮
 * @returns
 */
export const menuListQueryPost = async (params: { type?: string }) => {
  return post<string[]>(`/ipmsauthcenter/PermissionFacade/menuListQuery`, {
    data: params,
  });
};

/**
 * 查询账户/业务员/员工列表
 * @returns
 */
export const accountListQuerySimple = async ({
  status = YesNoStatus.YES,
  name = '',
}: {
  status?: YesNoStatus;
  name?: string;
}): Promise<PostSelect[]> => {
  return post(`/ipmspassport/AccountFacade/listQuerySimple`, {
    data: { status, name },
  });
};
