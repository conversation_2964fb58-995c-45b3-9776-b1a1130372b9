import usePermissionStore from '@/pages/splash/permissionStore';
import Taro from '@tarojs/taro';
import { isEmpty } from 'lodash';
import CustomTabBar from "@/components/CustomTabBar";

export default function Index() {
  const token = Taro.getStorageSync('token');
  const permissions = usePermissionStore();

  console.log('token', token);
  let url = '';
  if (permissions?.buttonItem.includes('/report/sales/view')) {
    url = '/report/sales';
  } else if (permissions?.buttonItem.includes('/report/purchase/view')) {
    url = '/report/purchase';
  } else if (permissions?.buttonItem.includes('/report/inventory/view')) {
    url = '/report/inventory';
  } else if (permissions?.buttonItem.includes('/report/finance/view')) {
    url = '/report/finance';
  }

  if (!url) {
    return (
      <div className="text-center">
        <div className="mt-[300px] text-[#999999]">暂无任何报表权限</div>
      </div>
    );
  }

  console.log(`${PC_WEB_DOMAIN}${url}?sid=${token}`);

  if (isEmpty(token)) {
    Taro.removeStorageSync('token');
    Taro.redirectTo({ url: '/pages/login/index' });
    return <></>;
  }
  return (
    <div className="flex flex-col h-screen">
      <iframe className="flex-1 w-screen" src={`${PC_WEB_DOMAIN}${url}?sid=${token}`} />
      <CustomTabBar activeTabIndex={2} />
    </div>
  );
}
