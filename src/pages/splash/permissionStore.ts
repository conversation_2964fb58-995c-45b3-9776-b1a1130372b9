import { includes, isEmpty } from 'lodash';
import { create } from 'zustand';

type Store = {
  buttonItem: string[];
  hasPermission: (permission: string) => boolean;
  setButtonItem: (buttonItem?: string[]) => void;
  reset: () => void;
};

const usePermissionStore = create<Store>()((set, get) => ({
  buttonItem: [],
  hasPermission: (permission) => {
    const permissions = get().buttonItem;
    return (!isEmpty(permissions)) && includes(permissions, permission)
  },
  setButtonItem: (buttonItem) => set(() => ({ buttonItem })),
  reset: () => set(() => ({ buttonItem: [] })),
}));

export default usePermissionStore;
