import { isEmpty } from 'lodash';
import React, { ReactNode } from 'react';
import usePermissionStore from './permissionStore';

interface PermissionComponentProps {
  permission: string;
  children: ReactNode;
}

const PermissionComponent: React.FC<PermissionComponentProps> = ({ permission, children }) => {
  const permissions = usePermissionStore();
  if (!isEmpty(permission)) {
    const hasPermission = permissions?.buttonItem.includes(permission);

    if (!hasPermission) {
      return null; // 没有权限时不显示组件
    }
  }

  return <>{children}</>;
};

export default PermissionComponent;
