import {
  Divider,
  Price,
} from '@nutui/nutui-react-taro';

const StatComponent = ({title, stat, isDivider = true, classnames, state}) => {

  return (
    <div className={`flex flex-row items-left ${classnames}`}>
      <div className="flex flex-col items-left">
        <div className="text-[26px] mb-[16px] font-light">{title}</div>
        <Price
          price={!state ? stat ?? '-' : '***'}
          digits={!state && stat && title != '销售单数' ? 2 : 0} className="custom-price" symbol=''
          size="normal" thousands
          style={{
            '--nutui-price-integer-medium-size': '16px',
            '--nutui-price-decimal-medium-size': '16px'
          }}
        />
      </div>
      {isDivider &&
        <div className="flex-grow flex justify-end items-center">
          <Divider
            direction="vertical"
            className="font-light"
            style={{
              '--nutui-divider-vertical-height': '60%',
              borderColor: '#FFFFFF',
            }}
          />
        </div>
      }
    </div>
  );
};

export default StatComponent;
