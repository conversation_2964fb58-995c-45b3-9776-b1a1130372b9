import Card from '@/components/Card';
import PermissionComponent from '@/pages/splash/PermissionComponent';
import collection from '@/pages/workbench/images/collection.png';
import customerList from '@/pages/workbench/images/customer_list.png';
import inventory from '@/pages/workbench/images/inventory.png';
import salesInvoicing from '@/pages/workbench/images/sales_invoicing.png';
import { Image } from '@tarojs/components';
import { navigateTo } from '@tarojs/taro';

const QuickActionsComponent = ({ title, items }) => {
  const icons = {
    客户管理: {
      imageUrl: customerList,
      url: '/packages/customer/list/index',
      permission: '/customer/list/view',
    },
    销售开单: {
      imageUrl: salesInvoicing,
      url: '/packages/sales/order/edit/index',
      permission: '/sales/order/edit/view',
    },
    盘点管理: {
      imageUrl: inventory,
      url: '/packages/stocks/check/list/index',
      permission: '/stocks/check/view',
    },
    收款: {
      imageUrl: collection,
      url: '/packages/finance/receive/list/index',
      permission: '/finance/receive/view',
    },
  };

  // 单个图标项组件
  const IconItem = ({ name, imageUrl, url, permission }) => (
    <PermissionComponent permission={permission}>
      <div className="flex flex-col items-center" onClick={() => navigateTo({ url })}>
        <Image src={imageUrl} style={{ width: '22px', height: '22px' }} />
        <div className="text-center text-[28px] pt-[16px] text-[#666666]">{name}</div>
      </div>
    </PermissionComponent>
  );

  return (
    <Card title={title}>
      <div className="grid grid-cols-4 gap-4 items-center">
        {items.map((item, index) => (
          <IconItem
            key={index}
            name={item.name}
            imageUrl={icons[item.name]?.imageUrl}
            url={icons[item.name]?.url}
            permission={icons[item.name]?.permission}
          />
        ))}
      </div>
    </Card>
  );
};

export default QuickActionsComponent;
