import {Tabs} from "@nutui/nutui-react-taro";
import {useAsyncEffect} from "ahooks";
import {queryIndexSaleTrendList} from "@/pages/home/<USER>";
import {forEach, isEmpty, isNil} from "lodash";
import {useState} from "react";
import {SalesTrendDataType} from "@/pages/home/<USER>/SaleTrendChartComponent/types/SalesTrendDataType";
import SaleTrendChart from "@/pages/home/<USER>/SaleTrendChartComponent/SaleTrendChart";
import Card from "@/components/Card";
import './index.scss'

const SaleTrendChartComponent = ({storeId, isReloadFlag}) => {

  const [saleAmountLineData, setSaleAmountLineData] = useState<SalesTrendDataType[]>();
  const [saleGrossLineData, setSaleGrossLineData] = useState<SalesTrendDataType[]>();
  const [saleCountBarData, setSaleCountBarData] = useState<SalesTrendDataType[]>();
  const [saleTrendIndex, setSaleTrendIndex] = useState<Number>(0);

  // 销售趋势
  useAsyncEffect(async () => {
    queryIndexSaleTrendList({"storeIdList": isEmpty(storeId) ? [] : [storeId]}).then((result) => {
      if (isEmpty(result)) {
        return;
      }
      const saleAmountData: SalesTrendDataType[] = [];
      const saleGrossData: SalesTrendDataType[] = [];
      const saleCountData: SalesTrendDataType[] = [];
      forEach(result, (item) => {
        const {saleDate, saleGrossProfit, saleAmount, saleOrderNum} = item;
        const date = saleDate;
        saleAmountData.push({
          value: saleAmount ? Number(saleAmount) : 0,
          date
        });
        saleGrossData.push({
          value: saleGrossProfit ? Number(saleGrossProfit) : null,
          date
        });
        saleCountData.push({
          value: saleOrderNum ? Number(saleOrderNum) : 0,
          date
        });
      });
      setSaleAmountLineData(saleAmountData);
      // 如果有空值则隐藏销售毛利
      if (!(saleGrossData) || saleGrossData.find((t) => isNil(t.value))) {
        setSaleGrossLineData([]);
      } else {
        setSaleGrossLineData(saleGrossData);
      }
      setSaleCountBarData(saleCountData);
    });
  }, [storeId, isReloadFlag]);

  const saleTrendTabList = [
    {
      title: '销售额',
      paneKey: 0,
    },
    {
      title: '销售毛利',
      paneKey: 1,
    },
    {
      title: '销售单数',
      paneKey: 2,
    },
  ]

  return (
    <Card className="tabs-card">
      <Tabs
        style={{
          '--nutui-tabs-titles-background-color': 'white',
          '--nutui-tabs-titles-item-active-font-weight': 'Medium',
          '--nutui-tabs-titles-item-active-color': 'rgba(0,0,0,0.9)',
          '--nutui-tabs-tab-line-width': '64px',
          '--nutui-tabs-tab-line-color': 'linear-gradient( 270deg, rgba(252,100,95,0) 0%, #F83431 100%)',
          '--nutui-tabs-tab-line-height': '3px',
          '--nutui-tabs-titles-font-size': '16px',
          '--nutui-tabs-titles-item-color': 'rgba(0,0,0,0.6)',
        }}
        onChange={(value) => {
          setSaleTrendIndex(Number(value));
        }}
        align="left"
      >
        <Tabs.TabPane title="销售额"/>
        <Tabs.TabPane title="销售毛利"/>
        <Tabs.TabPane title="销售单数"/>
      </Tabs>
      {saleTrendIndex == 0 &&
        <SaleTrendChart
          chartData={saleAmountLineData}
          chartType="area"
          canvasId="saleAmount"
          chartName="销售额"
        />
      }
      {saleTrendIndex == 1 &&
        <SaleTrendChart
          chartData={saleGrossLineData}
          chartType="area"
          canvasId="saleGross"
          chartName="销售毛利"
        />
      }
      {saleTrendIndex == 2 &&
        <SaleTrendChart
          chartData={saleCountBarData}
          chartType="column"
          canvasId="saleCount"
          chartName="销售单数"
        />
      }
    </Card>
  );
};

export default SaleTrendChartComponent;
