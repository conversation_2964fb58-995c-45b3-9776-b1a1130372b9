import React, {Component, useState} from 'react';
import Taro from '@tarojs/taro';
import {View, Canvas} from '@tarojs/components';
import uCharts from '../ucharts/u-charts.min';
import './index.scss';
import {useAsyncEffect} from "ahooks";
import RedPoint from '../images/point.svg'
import BluePoint from '../images/bluePoint.svg'
import {IconFont} from "@nutui/icons-react-taro";
import dayjs from "dayjs";
import {Skeleton} from "@nutui/nutui-react-taro";

const uChartsInstance = {};

const SaleTrendChart = ({chartData, canvasId, chartType, chartName}) => {

  const [checkData, setCheckData] = useState<{ category: string, value: string }>();
  const [isVisible, setIsVisible] = useState<boolean>(false);

  const sysInfo = Taro.getSystemInfoSync();
  let cWidth = sysInfo.windowWidth - 50;
  let cHeight = cWidth / 2;

  const chartOpts = () => {
    return {
      categories: chartData?.map(item => item.date),
      series: [
        {
          name: "",
          connectNulls: true,
          data: chartData?.map(item => item.value),
        }
      ]
    };
  }

  useAsyncEffect(async () => {
    if (!chartData) {
      return;
    }
    const data = chartOpts();
    drawCharts(canvasId, data);
    setIsVisible(true);
    if (data && data.categories && data.categories.length > 0 && data.series && data.series.length > 0 && data.series[0].data && data.series[0].data.length > 0) {
      setCheckData({
        category: data.categories[data.categories.length - 1],
        value: data.series[0].data[data.series[0].data.length - 1]
      });
    }
  }, [chartData]);

  const drawCharts = (id, data) => {
    uChartsInstance[id] = new uCharts({
      type: chartType,
      context: Taro.createCanvasContext(id),
      width: cWidth,
      height: cHeight,
      background: 'white',
      enableScroll: true,
      categories: data.categories,
      series: data.series,
      dataLabel: false,
      pixelRatio: 1,
      padding: [10, 20, 0, 0],
      update: true,
      dataPointShape: false,
      legend: {
        show: false
      },
      xAxis: {
        disableGrid: true,
        axisLine: false,
        itemCount: 12,
        scrollShow: true,
        scrollAlign: 'right',
        labelCount: 6,
        marginTop: 8,
        formatter: (val) => {
          return val ? dayjs(val).format('MM.DD') : val;
        }
      },
      yAxis: {
        data: [
          {
            axisLine: false,
          }
        ]
      },
      color: [canvasId == 'saleCount' ? "#176EFF" : "#F83431"],
      extra: {
        area: {
          type: "curve",
          opacity: 0.2,
          addLine: true,
          gradient: true,
          activeType: "hollow"
        },
        column: {
          type: "group",
          width: 10,
        },
        tooltip: {
          showBox: false,
        }
      }
    });
  }

  const touchstart = (e) => {
    uChartsInstance[e.target.id].scrollStart(e);
  }

  const touchmove = (e) => {
    uChartsInstance[e.target.id].scroll(e);
  }

  const touchend = (e) => {
    uChartsInstance[e.target.id].scrollEnd(e);
    uChartsInstance[e.target.id].touchLegend(e);
    uChartsInstance[e.target.id].showToolTip(e);
    const index = uChartsInstance[e.target.id].getCurrentDataIndex(e);
    if (index && index.index > -1) {
      setCheckData({
        category: uChartsInstance[e.target.id].opts.categories[index.index],
        value: uChartsInstance[e.target.id].opts.series[0].data[index.index]
      })
    }
  }

  return (
    <View className="flex flex-col no-scroll">
      <Skeleton animated rows={8} visible={isVisible}>
        {checkData &&
          <div
            className="flex justify-between items-center py-0 mt-[12px] mb-[31px] text-[24px] text-[rgba(0,0,0,0.6)]">
            <div>{checkData?.category}</div>
            <div className="flex flex-grow items-center justify-end">
              {
                canvasId == 'saleCount' ? (
                  <IconFont name={BluePoint}/>
                ) : (
                  <IconFont name={RedPoint}/>
                )
              }
              {chartName}
              <span className="ml-[12px] text-[#F83431]">{checkData?.value}</span>
            </div>
          </div>
        }
        <Canvas
          canvas-id={canvasId}
          id={canvasId}
          style={{
            width: cWidth,
            height: cHeight + 10
          }}
          onTouchStart={touchstart}
          onTouchMove={touchmove}
          onTouchEnd={touchend}
        />
      </Skeleton>
    </View>
  )
};


export default SaleTrendChart;
