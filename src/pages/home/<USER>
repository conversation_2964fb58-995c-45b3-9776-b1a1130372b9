import {post} from '@/utils/request';
import {SalesResponseType} from "@/pages/home/<USER>/SalesResponseType";
import {
  PageQueryOrderByConditionRequest
} from "@/packages/sales/order/list/types/page.query.order.by.condition.request";
import {OrderCount} from "@/pages/home/<USER>/OrderCount";

/**
 * 销售概况(日/月)
 * @param params
 * @returns
 */
export const queryIndexOverviewList = (params: {
  storeIdList?: string[] | undefined;
  type: 'DAY' | 'MONTH';
}) => {
  return post<SalesResponseType[]>(`/ipmsconsole/console/IndexFacade/queryIndexOverviewList`, {
    data: params,
  });
};

/**
 * 查询各销售单状态对应的单据数量
 */
export const queryOrderStatusCount = (params: PageQueryOrderByConditionRequest) => {
  return post<OrderCount>(`/ipmssale/queryOrderStatusCount`, {
    data: params,
  });
};

/**
 * 近30天销售趋势
 * @param params
 * @returns
 */
export const queryIndexSaleTrendList = (params: { storeIdList?: string[] | undefined }) => {
  return post<SalesResponseType[]>(`/ipmsconsole/console/IndexFacade/queryIndexSaleTrendList`, {
    data: params,
  });
};
