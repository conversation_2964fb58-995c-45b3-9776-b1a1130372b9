import { queryStore } from '@/components/ChooseStoreAndWarehouseModal/services';
import CustomTabBar from '@/components/CustomTabBar';
import { queryMsgList } from '@/packages/message/services';
import { MsgStatus } from '@/packages/message/types/MsgStatus';
import { MsgListItemEntity } from '@/packages/message/types/msg.list.item.entity';
import { OrderStatus } from '@/packages/sales/order/list/types/OrderStatus';
import { queryInventoryPagePost, warehouseList } from '@/packages/stocks/inventory/services';
import QuickActionsComponent from '@/pages/home/<USER>/QuickActionsComponent';
import { queryIndexOverviewList, queryOrderStatusCount } from '@/pages/home/<USER>';
import { StoreRequestParams } from '@/pages/home/<USER>/StoreRequestParams';
import logoShare from '@/pages/login/images/logo-share.png';
import RegUtils from '@/utils/RegUtils';
import { ArrowRight } from '@nutui/icons-react-taro';
import { Card, Ellipsis, Image, PullToRefresh } from '@nutui/nutui-react-taro';
import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import { navigateTo, useShareAppMessage } from '@tarojs/taro';
import { useAsyncEffect, useReactive, useToggle } from 'ahooks';
import dayjs from 'dayjs';
import { defaultTo, forEach, isEmpty } from 'lodash';
import { useState } from 'react';
import SaleTrendChartComponent from './components/SaleTrendChartComponent';
import messagebg from './imgs/messagebg.png';
import './index.scss';

export default function Index() {
  const [state, { toggle }] = useToggle(false);
  const [chooseStoreVisible, setChooseStoreVisible] = useState(false);
  const [storeOptions, setStoreOptions] = useState<PickerOption[]>([]);
  const [dataType, setDataType] = useState<string>('DAY');
  const [storeIdListParams, setStoreIdListParams] = useState<StoreRequestParams>();
  const [saleGrossProfit, setSaleGrossProfit] = useState<string[]>();
  const [saleOrderNum, setSaleOrderNum] = useState<string[]>();
  const [fundsReceived, setFundsReceived] = useState<string[]>();
  const [saleAmount, setSaleAmount] = useState<string[]>();
  const [storeId, setStoreId] = useState<string>('');
  const [storeName, setStoreName] = useState<string>();
  const [msgList, setMsgList] = useState<MsgListItemEntity[]>();
  const [isReloadFlag, setIsReloadFlag] = useState<number>();

  useShareAppMessage(() => {
    return {
      imageUrl: logoShare,
      path: '/pages/splash/index',
    };
  });

  // 查询消息列表服务
  const queryMsgListFn = () => {
    queryMsgList({ pageNo: 1, pageSize: 1, isDelete: 0 }).then((result) => {
      if (result?.data) {
        setMsgList(result.data);
      }
    });
  };
  // 查询门店数据服务
  const queryStoreFn = () => {
    queryStore({ status: 1 }).then((storeResult) => {
      if (storeResult) {
        setStoreOptions(
          storeResult.map((store) => ({ value: store.id ?? '', text: store.name ?? '' })),
        );
      }
    });
  };

  useAsyncEffect(async () => {
    // 查询门店数据
    queryStoreFn();
    // 查询消息列表
    queryMsgListFn();
  }, []);

  // 查询实时概况服务
  const queryIndexOverviewListFn = () => {
    queryIndexOverviewList({
      type: dataType,
      ...storeIdListParams,
    }).then((result) => {
      if (isEmpty(result)) return;
      const [yesterday, today] = result;
      setSaleAmount([today?.saleAmount ?? '', yesterday?.saleAmount ?? '']);
      setSaleGrossProfit([today?.saleGrossProfit ?? '', yesterday?.saleGrossProfit ?? '']);
      setSaleOrderNum([today?.saleOrderNum ?? '', yesterday?.saleOrderNum ?? '']);
      setFundsReceived([today?.fundsReceived ?? '', yesterday?.fundsReceived ?? '']);
    });
  };

  // 实时概况
  useAsyncEffect(async () => {
    queryIndexOverviewListFn();
  }, [dataType, storeIdListParams]);

  // 待办事项
  const todoMap = useReactive<Record<string, number>>({
    // 待处理订单
    WillHandle: 0,
    // 待出库订单
    OutBound: 0,
    // 库存预警
    StockWarning: 0,
  });
  // 查询待处理/到出库订单统计数据
  const queryOrderStatusCountFn = () => {
    queryOrderStatusCount({
      ...storeIdListParams,
    }).then((result) => {
      const res = result as Record<OrderStatus, number>;
      todoMap.OutBound = defaultTo(res[OrderStatus.WAIT_TO_OUTBOUND], 0);
      todoMap.WillHandle = defaultTo(res[OrderStatus.WAIT_TO_HANDLE], 0);
    });
  };
  // 待出库
  useAsyncEffect(async () => {
    queryOrderStatusCountFn();
  }, [storeIdListParams]);

  // 查询库存预警服务
  const queryInventoryPagePostFn = () => {
    queryInventoryPagePost({
      onlyTotalStatistics: true,
      invLimitStatusList: [1, 2],
      storeId: storeId == '0' ? undefined : storeId,
    }).then((result) => {
      todoMap.StockWarning = defaultTo(result?.total, 0);
    });
  };
  // 库存预警
  useAsyncEffect(async () => {
    queryInventoryPagePostFn();
  }, [storeId]);

  // 下拉刷新数据
  const pullToRefresh = async () => {
    queryStoreFn();
    queryMsgListFn();
    queryIndexOverviewListFn();
    queryOrderStatusCountFn();
    queryInventoryPagePostFn();
    setIsReloadFlag(dayjs().valueOf());
  };

  return (
    // #FFF5EE => #F5F5F5 jian bian
    <div>

      <PullToRefresh onRefresh={() => pullToRefresh()}>
        {/*实时概况统计区域*/}
        {/* <Card className="sale-amount-background">
          <div className="flex justify-end">
            <div className="text-[28px] font-light flex-grow flex justify-start items-center gap-[8px] text-[#FFFFFF]">
              <span>销售额</span>
              {!state ? (
                <IconFont name={Eye} onClick={toggle} />
              ) : (
                <IconFont name={EyeInvisible} onClick={toggle} />
              )}
            </div>
            <div className="flex flex-row-reverse text-[24px]">
              <Radio.Group
                defaultValue={'DAY'}
                shape="button"
                direction="horizontal"
                className="today-month"
                onChange={(value) => {
                  setDataType(value.toString());
                }}
              >
                <Radio value={'DAY'}>今日</Radio>
                <Radio value={'MONTH'}>本月</Radio>
              </Radio.Group>
            </div>
          </div>
          <div className="flex flex-grow items-left pb-[30px] text-[#FFFFFF]">
            <Price
              price={!state ? saleAmount?.[0] ?? '-' : '***'}
              digits={!state && saleAmount?.[0] ? 2 : 0}
              className="custom-price text-[48px]"
              symbol=""
              thousands
              size="normal"
              style={{
                '--nutui-price-integer-medium-size': '24px',
                '--nutui-price-decimal-medium-size': '24px',
              }}
            />
            <LastStatComponent
              title="销售额度"
              stat={saleAmount?.[1]}
              classnames="ml-[20px] pt-[18px] text-[26px]"
              state={state}
              dataType={dataType}
            />
          </div>

          <div className="flex justify text-[#FFFFFF]">
            <StatComponent
              title="销售毛利"
              stat={saleGrossProfit?.[0]}
              state={state}
              classnames="w-[35%]"
            />
            <StatComponent
              title="销售单数"
              stat={saleOrderNum?.[0]}
              state={state}
              classnames="ml-[10px] w-[35%]"
            />
            <StatComponent
              title="资金入账"
              stat={fundsReceived?.[0]}
              isDivider={false}
              state={state}
              classnames="ml-[10px] w-[30%]"
            />
          </div>
          <div className="flex justify mt-[12px] text-[#FFFFFF] text-[26px] font-light">
            <LastStatComponent
              stat={saleGrossProfit?.[1]}
              state={state}
              dataType={dataType}
              classnames="w-[35%]"
            />
            <LastStatComponent
              title="销售单数"
              stat={saleOrderNum?.[1]}
              classnames="ml-[10px] w-[35%]"
              state={state}
              dataType={dataType}
            />
            <LastStatComponent
              stat={fundsReceived?.[1]}
              classnames="ml-[10px] w-[30%]"
              state={state}
              dataType={dataType}
            />
          </div>
        </Card> */}

        {/*消息通知*/}
        {msgList && msgList.length > 0 && (
          <div
            className="px-[28px] relative"
            onClick={() => navigateTo({ url: '/packages/message/list/index' })}
          >
            <Image height={50} mode="aspectFill" src={messagebg}></Image>
            <div className="absolute left-[140px] top-0 bottom-0 right-[50px] flex justify-between items-center">
              <div className="border-0 border-solid border-l-2 border-l-[#00000014] flex items-center">
                {msgList[0]?.msgStatus == MsgStatus.NoRead && (
                  <span className="h-[40px] ml-[12px] msgUnRead"></span>
                )}
                <Ellipsis
                  className="ml-[12px] text-[26px] text-[#666666]"
                  content={RegUtils.removeHtmlTag(msgList[0]?.content ?? '')}
                  direction="end"
                />
              </div>
              <ArrowRight color="#666666FF" width={12} height={20} />
            </div>
          </div>
        )}

        {/*快捷操作*/}
        <QuickActionsComponent
          items={[
            { name: '客户管理' },
            { name: '销售开单' },
            { name: '盘点管理' },
            { name: '收款' },
          ]}
        />

        {/*待办事项*/}
        <Card title="待办事项" className="todo-background">
          <div className="flex justify mt-[28px] my-[28px] text-block">
            <div
              className="flex flex-col items-center w-1/3"
              onClick={() =>
                navigateTo({
                  url: '/packages/sales/order/list/index?initOrderStatus=90&initStoreId=' + storeId,
                })
              }
            >
              <div className="text-[32px]">{todoMap.WillHandle}</div>
              <div className="text-[28px] opacity-60 pt-[15px]">待处理订单</div>
            </div>
            <div
              className="flex flex-col items-center w-1/3"
              onClick={() =>
                navigateTo({
                  url:
                    '/packages/sales/order/list/index?initOrderStatus=300&initStoreId=' + storeId,
                })
              }
            >
              <div className="text-[32px]">{todoMap.OutBound}</div>
              <div className="text-[28px] opacity-60 pt-[15px]">待出库订单</div>
            </div>
            <div
              className="flex flex-col items-center w-1/3"
              onClick={async () => {
                // 根据门店ID查询所有的仓库
                if (storeId) {
                  const { warehouseSimpleRoList } = await warehouseList({ storeIdList: [storeId] });
                  if (warehouseSimpleRoList) {
                    const initWarehouseIdList: string[] = [];
                    forEach(warehouseSimpleRoList, (value) => {
                      initWarehouseIdList.push(value.id!);
                    });
                    navigateTo({
                      url:
                        '/packages/stocks/inventory/list/index?initInvLimitStatusList=1,2&initWarehouseIdList=' +
                        initWarehouseIdList,
                    }).then();
                    return;
                  }
                }
                navigateTo({
                  url: '/packages/stocks/inventory/list/index?initInvLimitStatusList=1,2',
                }).then();
              }}
            >
              <div className="text-[32px]">{todoMap.StockWarning}</div>
              <div className="text-[28px] opacity-60 pt-[15px]">库存预警</div>
            </div>
          </div>
        </Card>

        {/*销售趋势图表*/}
        <div style={{ display: chooseStoreVisible ? 'none' : '' }}>
          <SaleTrendChartComponent storeId={storeId} isReloadFlag={isReloadFlag} />
        </div>
      </PullToRefresh>
      <CustomTabBar activeTabIndex={0} />

    </div>
  );
}
