.sale-amount-background {
  background-image: url('./imgs/saleamountbg.svg');
  background-size: cover;
  background-position: center;
}

.msg-background {
  background-image: url('./imgs/messagebg.png');
  background-size: cover;
  background-position: center;
}

.todo-background {
  background-image: url('./imgs/todobg.png');
  background-size: cover;
  background-position: center;
}

.custom-price {
  color: #FFFFFF;
  font-size: 32px;
}

.custom-price-last {
  color: #FFFFFF;
  font-weight: lighter;
}

.today-month {
  --nutui-radio-button-border-radius: 4px;
  --nutui-radiogroup-radio-margin: -1px;
  --nutui-radio-button-active-border: 1px solid #FFFFFF;
  --nutui-radio-label-color: #FFFFFF
}

.no-wrap {
  white-space: nowrap;
  overflow: hidden;
  /* 隐藏溢出内容 */
  text-overflow: ellipsis;
  /* 当内容溢出时，在结尾添加省略号 */
}

.msg-divider {
  borderColor: 'rgba(0,0,0,0.08)'
}

.msgUnRead {
  height: 12px;
  width: 12px;
  border-radius: 24px;
  background-color: #F83431;
}