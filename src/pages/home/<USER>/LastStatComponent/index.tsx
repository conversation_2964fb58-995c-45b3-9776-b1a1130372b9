import {
  Price,
} from '@nutui/nutui-react-taro';

const LastStatComponent = ({stat, classnames, state, dataType, title}) => {

  return (
    <div className={`flex flex-grow items-left items-center ${classnames}`}>
      <div className="mr-[8px]">{dataType == 'MONTH' ? '上月' : '昨日'}</div>
      <Price
        price={!state ? stat ?? '-' : '***'}
        size="normal"
        digits={!state && stat && title != '销售单数' ? 2 : 0} className="custom-price-last" symbol='' thousands
        style={{
          '--nutui-price-integer-medium-size': '13px',
          '--nutui-price-decimal-medium-size': '13px'
        }}
      />
    </div>
  );
};

export default LastStatComponent;
