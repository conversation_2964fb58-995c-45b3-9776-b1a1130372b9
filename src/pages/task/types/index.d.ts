import { StatusEnum } from "./todo.enum";

export interface TodoEntity {
  id: number;
  taskDesc: string;
  creator: string;
  createPerson: string;
  todoPersonName: string;
  todoPerson: string;
  memberId: string;
  status: StatusEnum;
  createTime: string;
  completionTime: string;
  cancelTime: string;
  completionDesc: string;
}

export interface CreateTodoParams {
  todoPerson: string;
  todoPersonName: string;
  taskDesc: string;
}

export interface UpdateTodoParams extends CreateTodoParams {
  id: number;
}

export interface CancelTodoParams {
  id: number;
}

export interface CompleteTodoParams {
  id: number;
  completionDesc?: string;
}
