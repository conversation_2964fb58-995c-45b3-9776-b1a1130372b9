
import { PostEntity } from '@/pages/common/login/types/post.entity';
import { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { PageResponseDataType } from '@/types/PageResponseDataType';
import { post as request } from '@/utils/request';
import { QueryStoreListRequest } from '../../store/list/types/query.store.list.request';
import type { CancelTodoParams, CompleteTodoParams, CreateTodoParams, TodoEntity, UpdateTodoParams } from './types';


export const queryStoreList = async (params: QueryStoreListRequest & PageRequestParamsType) => {
  return request<PageResponseDataType<PostEntity>>(`/ipmspassport/StoreFacade/pageQuery`, {
    data: params,
  });
};

export const queryTodoList = (params: PageRequestParamsType): Promise<PageResponseDataType<TodoEntity>> => {
  return request('/ipmspassport/store/todoTask/pageQuery', { data: params });
};

export const createTodo = (params: CreateTodoParams) => {
  return request('/ipmspassport/store/todoTask/create', { data: params });
};

export const updateTodo = (params: UpdateTodoParams) => {
  return request('/ipmspassport/store/todoTask/update', { data: params });
};

export const cancelTodo = (params: CancelTodoParams) => {
  return request('/ipmspassport/store/todoTask/cancel', { data: params });
};

export const completeTodo = (params: CompleteTodoParams) => {
  return request('/ipmspassport/store/todoTask/complete', { data: params });
};
