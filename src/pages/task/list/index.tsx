import CustomNavBar from "@/components/CustomNavBar";
import MenuWrap from "@/components/MenuWrap";
import { Add } from '@nutui/icons-react-taro';
import { Menu, SearchBar } from "@nutui/nutui-react-taro";
import { useRef, useState } from "react";
import { TodoEntity } from "../types";


export default function TaskList() {
  const statusRef = useRef<any>();
  const [params, setParams] = useState<Pick<Partial<TodoEntity>, 'taskDesc' | 'status' | 'todoPerson' | 'creator'>>({});

  const setInputValue = (param) => {
    setParams((prevData) => ({ ...prevData, ...param }));
  };



  return <div className="flex flex-col h-screen">
    <CustomNavBar showBack={true} title="待办任务" />

    <div className="px-[28px] pt-[16px] mb-[24px]">
      <SearchBar
        value={params.taskDesc}
        onChange={(val: string) => {
          setInputValue({ taskDesc: val });
        }}
        onClear={() => {
          setInputValue({ taskDesc: '' });
        }}
        placeholder={'任务描述'}
        right={<Add onClick={() => {

        }} />}
      />
    </div>
    <MenuWrap
      menu={
        <Menu>
          <Menu.Item title="创建人" ref={statusRef}>
            {/* <FilterAccountPicker onChange={(supplierId) => {

            }}
            /> */}
          </Menu.Item>
          <Menu.Item title="创建人" ref={statusRef}>
          </Menu.Item>
          <Menu.Item title="完成状态" ref={statusRef}>

          </Menu.Item>
        </Menu>
      }
    />

  </div>;
}
