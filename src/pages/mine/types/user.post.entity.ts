export interface UserPostEntity {
  /**
   * 创建人
   */
  createPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 一体系账户ID
   */
  etcAccountId?: string;
  /**
   * 一体系绑定状态：0未绑定，1已绑定
   */
  etcBindStatus?: string;
  /**
   * 一体系零售商ID
   */
  etcMemberId?: string;
  /**
   * 账户ID
   */
  id?: string;
  /**
   * 零售商ID
   */
  memberId?: string;
  /**
   * 账户名
   */
  name?: string;

  memberName?: string;
  /**
   * 手机号
   */
  phone?: string;
  /**
   * 角色名列表
   */
  roleNames?: string;
  /**
   * 状态：0禁用，1启用，-1注销
   */
  status?: string;
  /**
   * 门店名列表
   */
  storeNames?: string;
  /**
   * 类型：0主账户，5子账户
   */
  type?: string;
}
