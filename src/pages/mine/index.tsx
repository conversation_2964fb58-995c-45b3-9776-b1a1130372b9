import BluePrintCell from '@/components/BluePrintCell';
import CustomNavBar from '@/components/CustomNavBar';
import ItemsWithDivider from '@/components/ItemsWithDivider';
import { Cell, Image } from '@nutui/nutui-react-taro';
import { redirectTo, removeStorageSync, useLoad } from '@tarojs/taro';
import { useState } from 'react';
import { logoutPost } from '../login/services';
import user from './image/user.png';
import { queryUserInfoPost } from './services';
import { UserPostEntity } from './types/user.post.entity';
import CustomTabBar from "@/components/CustomTabBar";

export default function Index() {
  const [userInfo, setUserInfo] = useState<UserPostEntity>({});

  useLoad(async () => {
    const data = await queryUserInfoPost({});
    setUserInfo(data);
  });

  const handelLoginOut = async () => {
    const data = await logoutPost({});
    if (data) {
      //退出成功
      removeStorageSync('token');
      redirectTo({ url: '/pages/login/index' });
    }
  };

  return (
    <div>
      <CustomNavBar title="" showBack={false} />
      <div className="px-[28px] flex items-center">
        <div>
          <Image src={user} style={{ width: '60px', height: '60px' }} />
        </div>
        <div className="pl-[24px]">
          <div className="flex items-center gap-[16px]">
            <div className="text-[34px]">{userInfo.name}</div>
            {userInfo.roleNames &&
              userInfo.roleNames
                ?.split(',')
                .map((s) => (
                  <div className="text-[20px] bg-[#FFF4F4] px-[12px] py-[6px] rounded border border-solid border-[#FCAEAD] text-[#F83431]">
                    {s}
                  </div>
                ))}
          </div>
          <div className="pt-[12px] text-[32px] text-black/60">{userInfo.phone}</div>
        </div>
      </div>
      <div className="mx-[28px] mt-[40px]">
        <div className="bg-cover p-[32px] rounded memberBg">
          <div className="text-[32px] text-black/90 font-medium">{userInfo.memberName}</div>
          <div className="text-[28px] text-black/45 pt-[32px]">所属门店</div>
          <div className="text-[28px] text-black/60 pt-[12px] flex flex-wrap">
            <ItemsWithDivider items={userInfo?.storeNames?.split(',')} className="break-all" />
          </div>
        </div>
      </div>
      <Cell.Group className="px-[28px] rounded">
        <BluePrintCell />
      </Cell.Group>
      <div
        className="mx-[28px] bg-white rounded-[16px] py-[32px] flex justify-center text-[32px] text-black/90"
        onClick={handelLoginOut}
      >
        退出登录
      </div>
      <CustomTabBar activeTabIndex={3} />
    </div>
  );
}
