export interface LoginPostEntity {
  /**
   * 账户id
   */
  accountId?: string;
  /**
   * 账号名称
   */
  accountName?: string;
  /**
   * etc 账户id
   */
  etcAccountId?: string;
  /**
   * etc 店铺id
   */
  etcMemberId?: string;
  /**
   * 店铺id
   */
  memberId?: string;
  /**
   * 手机号
   */
  phone?: string;
  /**
   * sessionId
   */
  sSessionId?: string;
  /**
   * 自定义list对象
   */
  storeIdList?: string[];

  isFirst?: boolean;
}
