import CustomNavBar from '@/components/CustomNavBar';
import usePermissionStore from '@/pages/splash/permissionStore';
import { menuListQueryPost } from '@/services/common';
import { Button, ConfigProvider, Input } from '@nutui/nutui-react-taro';
import { Image } from '@tarojs/components';
import { setStorageSync, showToast, switchTab, useLoad } from '@tarojs/taro';
import { useBoolean } from 'ahooks';
import CryptoJS from 'crypto-js';
import { isEmpty } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import iconPassword from './images/iconPassword.png';
import iconPasswordOpen from './images/iconPasswordOpen.png';
import { loginPost, sendAuthcodePost } from './services';
import { QueryPostListRequest } from './types/query.post.list.request';

export default function LoginForm() {
  const [state, { setTrue, setFalse }] = useBoolean(true); //登录方式 默认账户密码登录

  const [btnTitle, setBtnTitle] = useState<string>('发送验证码');
  const [btnDisabled, setBtnDisabled] = useState<boolean>(false);
  const [time, setTime] = useState<number>(60);
  const timeFun = useRef<NodeJS.Timer | null>(null);
  const [name, setName] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [phone, setPhone] = useState<string>('');
  const [authcode, setAuthcode] = useState<string>('');
  const permission = usePermissionStore();

  useLoad(() => {
    // @ts-ignore
    clearInterval(timeFun.current);
    return () => {
      // @ts-ignore
      clearInterval(timeFun.current);
    };
  });

  useEffect(() => {
    if (btnDisabled && time > 0 && time < 60) {
      setBtnTitle(`${time}s`);
    } else {
      setBtnTitle('发送验证码');
      setBtnDisabled(false);
      setTime(60);
      // @ts-ignore
      clearInterval(timeFun.current);
    }
  }, [time]);

  const checkStyle = state
    ? { color: '#111111', fontSize: '24px', fontWeight: 500 }
    : { color: '#999999', fontSize: '16px' };
  const noCheckStyle = state
    ? { color: '#999999', fontSize: '16px' }
    : { color: '#111111', fontSize: '24px', fontWeight: 500 };
  const [inputType, setInputType] = useState('password');
  const sendVerifCode = async () => {
    if (isEmpty(phone)) {
      showToast({ title: '请填写正确的手机号！', icon: 'none' });
      return;
    }
    const data = await sendAuthcodePost({ phone, type: 0, channel: 1 });
    if (data) {
      showToast({ title: '验证码发送成功' });
      // const code = await queryAuthcodePost({ phone });
      // showToast({ title: `验证码发送成功!${code}`, icon: 'none' });
      timeFun.current = setInterval(() => setTime((t) => --t), 1000);
      setBtnDisabled(true);
    }
  };

  const handleSubmit = async (values: QueryPostListRequest) => {
    try {
      //验证\
      if (state) {
        //账户名密码
        if (isEmpty(name)) {
          showToast({ title: '请输入账号！', icon: 'none' });
          return;
        } else if (name.trim().length !== 11) {
          showToast({ title: '账号必须为11位手机号！', icon: 'none' });
          return;
        } else if (isEmpty(password)) {
          showToast({ title: '请输入密码！', icon: 'none' });
          return;
        } else {
          values.phone = name;
          values.password = CryptoJS.MD5(password).toString();
        }
      } else {
        //短信验证码
        if (isEmpty(phone)) {
          showToast({ title: '请输入手机号！', icon: 'none' });
          return;
        } else if (phone.trim().length !== 11) {
          showToast({ title: '手机号必须为11位数字！', icon: 'none' });
          return;
        } else if (isEmpty(authcode)) {
          showToast({ title: '请输入验证码！', icon: 'none' });
          return;
        } else {
          values.phone = phone;
          values.authcode = authcode;
        }
      }
      const result = await loginPost({ ...values, type: state ? '0' : '1', channel: '1' });
      if (result) {
        const { sSessionId, accountId } = result;
        if (sSessionId) {
          setStorageSync('token', sSessionId);
        }
        //跳转到首页
        //showToast({ title: '登录成功！' + sSessionId, icon: 'none' });
        //加载权限
        const data = await menuListQueryPost({ type: '2' });
        if (data) {
          permission.setButtonItem(data);
        }
        switchTab({ url: '/pages/home/<USER>' });
        return;
      }
    } catch (error) {
      console.log(error);
      showToast({ title: '登录失败，请重试！', icon: 'none' });
    }
  };

  return (
    <div className="px-[48px]">
      <CustomNavBar title={''} showBack={false} />
      <div className="flex pt-[96px] items-center ">
        <div style={checkStyle} onClick={setTrue} className="pr-[60px]">
          账户密码登录
        </div>
        <div style={noCheckStyle} onClick={setFalse}>
          验证码登录
        </div>
      </div>
      <ConfigProvider
        theme={{
          nutuiInputFontSize: '16px',
          nutuiInputBorderBottomWidth: '1px',
          nutuiInputPadding: '12px 3px',
          nutuiInputBackgroundColor: '#F5F5F5',
          nutuiDividerBorderColor: '#E1E1E1',
          nutuiDividerMargin: '0px',
        }}
      >
        {state ? (
          <div className="pt-[60px]">
            <div>
              <Input
                placeholder="请输入账号"
                type="digit"
                maxLength={11}
                onChange={setName}
                key="name"
              />
            </div>
            <div className="pt-[24px] flex items-center relative">
              <Input
                type={inputType}
                placeholder="请输入密码"
                onChange={setPassword}
                maxLength={16}
                key="password"
              />
              <div
                className="right-0 absolute"
                style={{ zIndex: 10000 }}
                onClick={() => setInputType(inputType == 'text' ? 'password' : 'text')}
              >
                {inputType == 'text' ? (
                  <Image src={iconPasswordOpen} style={{ width: '25px', height: '25px' }} />
                ) : (
                  <Image src={iconPassword} style={{ width: '25px', height: '25px' }} />
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="pt-[60px]">
            <div>
              <Input
                placeholder="请输入手机号"
                type="digit"
                onChange={setPhone}
                key="phone"
                maxLength={11}
              />
            </div>
            <div className="pt-[24px] flex items-center relative">
              <Input
                placeholder="请输入验证码"
                onChange={setAuthcode}
                key="authcode"
                maxLength={6}
              />
              <div className="right-0 absolute">
                <Button
                  fill="none"
                  disabled={btnDisabled}
                  onClick={(e) => {
                    sendVerifCode();
                    e.stopPropagation();
                  }}
                >
                  {btnTitle}
                </Button>
              </div>
            </div>
          </div>
        )}
      </ConfigProvider>
      <div className="pt-[60px]">
        <Button
          block
          type="primary"
          style={{ marginBottom: '28px', borderRadius: '4px' }}
          size="large"
          shape="square"
          onClick={() => handleSubmit({})}
        >
          登录
        </Button>
      </div>
    </div>
  );
}
