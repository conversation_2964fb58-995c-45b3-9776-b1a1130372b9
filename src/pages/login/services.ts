import { post } from '@/utils/request';
import { ResponseDataType } from '../../../types/ResponseDataType';
import { LoginPostEntity } from './types/login.post.entity';
import { QueryPostListRequest } from './types/query.post.list.request';
import { SysProperty } from './types/query.sys.check.reponse';
import { WxloginPostEntity } from './types/wxlogin.post.entity';

/**
 * 登录
 * @param params
 * @returns
 */
export const loginPost = async (params: Partial<QueryPostListRequest>) => {
  return post<LoginPostEntity>(`/ipmspassport/LoginFacade/login`, {
    data: params,
  });
};

/**
 * 发送验证码
 * @param params
 * @returns
 */
export const sendAuthcodePost = async (params: Partial<QueryPostListRequest>) => {
  return post<ResponseDataType<boolean>>(`/ipmspassport/LoginFacade/sendAuthcode`, {
    data: params,
  });
};

/**
 * 根据提供的session信息进行用户注销。
 * @param params
 * @returns
 */
export const logoutPost = async (params: {}) => {
  return post<boolean>(`/ipmspassport/LoginFacade/logout`, {
    data: params,
  });
};

/**
 * 登录
 * @param params
 * @returns
 */
export const wxloginPost = async (params: Partial<WxloginPostEntity>) => {
  return post<ResponseDataType<LoginPostEntity>>(`/ipmspassport/LoginFacade/wxLogin`, {
    origin: true,
    data: params,
  });
};

/**
 * 获取短信验证
 * @param params
 * @returns
 */
export const queryAuthcodePost = async (params: Partial<QueryPostListRequest>) => {
  return post<ResponseDataType<string>>(`/ipmspassport/LoginFacade/queryAuthcode`, {
    data: params,
  });
};
