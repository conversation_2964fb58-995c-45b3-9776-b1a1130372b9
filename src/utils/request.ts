import Imei from '@/utils/imei';
import Taro, { getStorageSync, setStorageSync } from '@tarojs/taro';
import CryptoJS from 'crypto-js';

const MAX_RETRY_COUNT = 5;
let retryCount = 0;

// 计算post请求的加密数据
export const getPostSign = (data: any) => {
  const secret = getStorageSync('$RM_signature');
  const sSessionId = getStorageSync('token');
  const sessionId = Imei.get();
  const timestamp = new Date().getTime();

  let sign = secret;

  sign += `appkey${APP_KEY}`;

  if (data) {
    sign += `data${JSON.stringify(data)}`;
  }

  if (sSessionId) {
    sign += `s-session-id${sSessionId}`;
  }

  if (sessionId) {
    sign += `session-id${sessionId}`;
  }

  sign += `timestamp${timestamp}`;

  return {
    timestamp,
    sign: CryptoJS.MD5(sign).toString()?.toUpperCase(),
  };
};

const request = <T>(
  url: string,
  options: {
    method: Taro.request.Option['method'];
    data: Taro.request.Option['data'];
    loading?: boolean;
    header?: Taro.request.Option['header'];
    excludeCodes?: any[];
  },
): Promise<T> => {
  const isLoading = options.loading || false;
  let sign = getPostSign(options.data.data);
  const urlencoded = new URLSearchParams();
  const data = options.data;

  if (data?.current) {
    data.pageNo = data.current;
    delete data.current;
  }
  urlencoded.append('appkey', APP_KEY!);
  urlencoded.append('timestamp', sign.timestamp?.toString());
  urlencoded.append('sign', sign.sign);
  urlencoded.append('data', JSON.stringify(data?.data));

  return new Promise((resolve, reject) => {
    const token = Taro.getStorageSync('token');
    isLoading &&
    Taro.showLoading({
      title: '加载中',
      mask: true,
    });
    Taro.request({
      url: `/apigateway${url}`,
      method: options.method,
      data: urlencoded.toString(),
      timeout: 180000,
      header: {
        'Content-Type': 'application/x-www-form-urlencoded',
        's-session-id': token,
        appkey: APP_KEY,
        timestamp: sign.timestamp,
        sign: sign.sign,
        'session-id': Imei.get(),
        ...(options?.header || {}),
      },
      success(res: any) {
        isLoading && Taro.hideLoading();
        const { data } = res;
        // 拦截响应数据，进行个性化处理
        const { code, msg } = data;

        if (code !== 0) {
          if (code !== -9580106) {
            if (!options?.data?.silence) {
              if (msg?.length > 20) {
                Taro.showModal({
                  content: msg,
                  showCancel: false,
                });
              } else {
                Taro.showToast({
                  title: msg,
                  icon: 'none',
                  duration: 2000,
                });
              }
            }
          }
          if (code == 200) {
            //登录失效
            Taro.removeStorageSync('token');
            // Taro.redirectTo({ url: '/pages/login/index' });
          } else if (code == 100001) {
            Taro.showToast({ title: '请先登录PC端修改初始密码', icon: 'none' });
          } else if (code === -9580106) {
            setStorageSync('$RM_signature', data.data.secret);
            retryCount++;
            if (retryCount < MAX_RETRY_COUNT) {
              return request(url, {
                ...options,
              })
                .then(resolve)
                .catch(reject);
            } else {
              retryCount = 0;
            }
          }
        }
        retryCount = 0;
        // @ts-ignore
        if (options?.data?.origin) {
          return resolve(data);
        } else {
          //直接返回值
          return resolve(data?.data);
        }
      },
      fail(error) {
        console.error('request => Error', error);
        isLoading && Taro.hideLoading();
        reject(error.errMsg);
      },
    });
  });
};

const post = <T, U = any>(
  url: string,
  options?: U,
  loading?: boolean,
  excludeCodes?: any[],
): Promise<T> => {
  return request<T>(url, { method: 'POST', data: options, excludeCodes, loading });
};

export { post };
