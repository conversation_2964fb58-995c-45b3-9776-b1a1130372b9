import { CapacitorBarcodeScanner, CapacitorBarcodeScannerTypeHint } from '@capacitor/barcode-scanner';
import { isInCapacitorApp } from "@/utils/isInCapacitorApp";
import Taro from "@tarojs/taro";

export async function scanCode() {
  if (isInCapacitorApp()) {
    return CapacitorBarcodeScanner.scanBarcode({
      hint: CapacitorBarcodeScannerTypeHint.ALL
    }).then(result => result.ScanResult);
  } else if(process.env.TARO_ENV === 'weapp'){
    return Taro.scanCode({}).then(result => result.result);
  } else {
    Taro.showToast({icon: "none", title: "当前平台不支持扫码"});
  }
}
