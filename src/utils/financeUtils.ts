import Taro from "@tarojs/taro";

export const isNumeric = (input) => {
  // 正则表达式匹配数字，包括整数、浮点数和负数
  const regex = /^-?\d+(\.\d+)?$/;
  return regex.test(input);
}

export const showToast = (message: string) => {
  Taro.showToast({
    title: message,
    icon: 'none',
    duration: 2000,
  }).then();
};

export const checkAmount = (amount) => {
  if (amount.endsWith(".") || amount == '-') {
    return true;
  }
  if (amount && !isNumeric(amount)) {
    showToast("请输入合法的数字！")
    return false;
  }
  const inputNum = Number(amount);
  if (amount && inputNum > 99999999.99) {
    showToast("输入收款金额大于最大值99999999.99！")
    return false;
  }
  if (amount && inputNum < -99999999.99) {
    showToast("输入收款金额小于最小值-99999999.99！")
    return false;
  }
  return true;
}
