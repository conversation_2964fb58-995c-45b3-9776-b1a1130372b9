/**
 * CameraSource.Prompt 在安卓机（华为鸿蒙测试机）上会出错，所以这里要模拟Prompt。
 */
import {
  Camera,
  CameraResultType,
  CameraSource,
  PermissionStatus,
} from '@capacitor/camera';
import { isInCapacitorApp } from '@/utils/isInCapacitorApp';
import Taro from '@tarojs/taro';

async function checkAndRequestPermissions(): Promise<boolean> {
  try {
    const status: PermissionStatus = await Camera.checkPermissions();
    const cameraGranted = status.camera === 'granted';
    const photosGranted = ['granted', 'limited'].includes(status.photos);
    if (cameraGranted && photosGranted) {
      return true;
    }
    const requestStatus = await Camera.requestPermissions();
    return (
      requestStatus.camera === 'granted' &&
      ['granted', 'limited'].includes(requestStatus.photos)
    );
  } catch (error) {
    console.error('权限检测失败', error);
    return false;
  }
}

function base64ToFile(base64: string, filename: string, mimeType: string): File {
  const arr = base64.split(',');
  const base64String = arr.length > 1 ? arr[1] : arr[0];
  const binaryStr = atob(base64String);
  const len = binaryStr.length;
  const uint8Array = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    uint8Array[i] = binaryStr.charCodeAt(i);
  }
  return new File([uint8Array], filename, { type: mimeType });
}

export const uploadImage = async (): Promise<string> => {
  if (!isInCapacitorApp()) {
    Taro.showToast({ icon: 'none', title: '当前平台不支持上传图片' });
    return Promise.reject('当前平台不支持上传图片');
  }

  const hasPermission = await checkAndRequestPermissions();
  if (!hasPermission) {
    Taro.showToast({ icon: 'none', title: '请允许相机和图库权限' });
    return Promise.reject('请允许相机和图库权限');
  }

  // 主动弹窗让用户选“拍照”或“从相册选择”
  const choose = await Taro.showActionSheet({
    itemList: ['拍照', '从相册选择'],
  });

  const source =
    choose.tapIndex === 0 ? CameraSource.Camera : CameraSource.Photos;

  try {
    const image = await Camera.getPhoto({
      quality: 70,
      resultType: CameraResultType.Base64,
      source,
    });

    if (!image.base64String) {
      return Promise.reject('图片数据为空');
    }

    const file = base64ToFile(
      `data:image/${image.format};base64,${image.base64String}`,
      `photo.${image.format || 'jpg'}`,
      `image/${image.format || 'jpeg'}`
    );

    const formData = new FormData();
    formData.append('file', file);

    const response = await Taro.request({
      url: UPLOAD_URL,
      method: 'POST',
      header: {
        's-session-id': Taro.getStorageSync('token'),
      },
      data: formData,
    });

    if (response?.data?.data?.[0]) {
      return response.data.data[0] as string;
    } else {
      return Promise.reject('上传失败，接口无返回数据');
    }
  } catch (error) {
    console.error('上传出错', error);
    return Promise.reject(error);
  }
};
