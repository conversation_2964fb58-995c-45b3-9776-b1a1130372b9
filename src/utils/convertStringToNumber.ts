/**
 * 把string转换成数字，原inputNumber问题太多，通过input获取数值需要此函数进行转换
 */

interface ConvertOptions {
  value: string;
  min?: number;
  max?: number;
  decimal?: number;
}

export const convertStringToNumber = ({
  value,
  min = Number.NEGATIVE_INFINITY,
  max = Number.POSITIVE_INFINITY,
  decimal = 2,
}: ConvertOptions) => {
  let num = parseFloat(value);

  // 如果转换失败，将num设置为minValue
  if (isNaN(num)) {
    num = min;
  }

  // 限制小数点位数
  num = parseFloat(num.toFixed(decimal));

  // 应用最小值和最大值限制
  if (num < min) {
    num = min;
  }
  if (num > max) {
    num = max;
  }

  return num;
};
