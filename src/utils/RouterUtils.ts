import Taro from '@tarojs/taro';
import { stringify } from 'qs';
export interface RouterProps {
    url: string;
    params?: Record<string, any>,
    options?: Omit<Taro.navigateTo.Option, "url">,
}
/**
 * 带参数路由
 * @param props RouterProps
 * @returns 
 */
const navigateTo = (props: RouterProps) => {
    const { url, params, options } = props
    return Taro.navigateTo({ url: `${url}?${stringify(params)}`, ...options })
}

export default {
    navigateTo,
}