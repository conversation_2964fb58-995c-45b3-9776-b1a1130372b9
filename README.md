
## 1、技术栈

- 框架：[TaroJs](https://taro-docs.jd.com)
- 组件库：[@nutui/nutui-react-taro](https://nutui.jd.com/taro/react/2x/#/zh-CN/component/button)
- 样式方案：[Tailwindcss](https://www.tailwindcss.cn)
- 状态共享：[Zustand](https://docs.pmnd.rs/zustand/getting-started/introduction)

## 2、其它工具

- 日期处理：[dayjs](https://dayjs.gitee.io/zh-CN/)
- 工具函数库：[lodash](https://www.lodashjs.com)
- class 条件工具：[classnames](https://github.com/JedWatson/classnames#readme)
- hooks 工具合集：[ahooks](https://ahooks.gitee.io/zh-CN/guide)

## 3、目录规范

```bash
├── config                          # 系统配置
│   ├── dev.ts                      # 开发环境
│   ├── prod.ts                     # 线上环境
│   └── index.ts                    # 通用配置
├── src
│   ├── assets                      # 通用的本地静态资源
│   ├── components                  # 通用的业务组件
│   ├── constants                   # 通用的常量
│   ├── pages                       # 业务页面入口
│   │   └── demo                    # 一级页面模块
│   │       └── list                # 二级页面模块
│   │           ├── index.tsx       # 二级页面的入口
│   │           ├── components      # 二级页面的通用组件
│   │           ├── services.ts     # 二级页面的接口服务
│   │           ├── index.config.ts # 二级页面的小程序配置
│   │           ├── types           # 二级页面的类型文件目录
│   │           └── config          # 二级页面的配置文件目录
│   ├── services                    # 通用的接口服务
│   ├── types                       # 通用的类型文件目录
│   ├── utils                       # 工具库
│   ├── app.scss                    # 全局样式
│   ├── app.config.ts               # 全局路由等配置
│   └── app.tsx                     # 入口文件
├── README.md
└── package.json
```

特别说明：

- 业务模块目录采用分而治之的原则，能内聚在业务模块内的文件就内聚在业务模块文件夹，如果能公用，则抽取到最小公用目录，例如组件全局能公用则抽取到`src/components`目录，否则就放在业务模块目录
- 非必要避免修改公用目录的文件，如果要修改需要充分沟通和识别影响点

## 4、编码规范

- 严格遵守 Typescript 编码规范
- 全部采用 tailwindcss 样式方案，非必要不得采用其他样式方案
- 非必要不得使用公用状态，必要的场景：1、全局状态，例如登录用户信息; 2、模块层级跨越较多场景共享状态
- 按钮触发数据提交场景必须要增加 loading 状态
- 页面合理拆分，避免大文件，增强可读性和可维护性

##

- 门店小程序
