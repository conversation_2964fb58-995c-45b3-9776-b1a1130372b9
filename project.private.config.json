{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "%E6%B1%BD%E9%85%8D%E8%BF%90%E8%90%A5%E9%80%9A", "setting": {"compileHotReLoad": false, "urlCheck": true, "bigPackageSizeSupport": true, "preloadBackgroundData": false}, "condition": {"miniprogram": {"list": [{"name": "销售开单", "pathName": "packages/sales/order/edit/index", "query": "", "launchMode": "default", "scene": null}, {"name": "埋点校验", "pathName": "pages/home/<USER>", "query": "miniappDebugId=log_96112409046464_9a5995cba827460b9c43101b3cd058f3_log20241202152717", "launchMode": "default", "scene": null}, {"name": "账号密码登录", "pathName": "pages/login/components/loginForm", "query": "", "launchMode": "default", "scene": null}, {"name": "库存管理", "pathName": "packages/stocks/inventory/list/index", "query": "", "launchMode": "default", "scene": null}, {"name": "入库详情", "pathName": "packages/stocks/input/detail/index", "query": "stockInId=240801173700010&warehouseId=111", "launchMode": "default", "scene": null}]}}, "libVersion": "2.25.3"}